import{toNestErrors as r,validateFieldsNatively as e}from"@hookform/resolvers";import{Effect as t}from"effect";import{decodeUnknown as o,ArrayFormatter as n}from"effect/ParseResult";import{appendErrors as a}from"react-hook-form";function i(i,s){return void 0===s&&(s={errors:"all",onExcessProperty:"ignore"}),function(u,c,f){return o(i,s)(u).pipe(t.catchAll(function(r){return t.flip(n.formatIssue(r))}),t.mapError(function(e){var t=!f.shouldUseNativeValidation&&"all"===f.criteriaMode,o=e.reduce(function(r,e){var o=e.path.join(".");if(r[o]||(r[o]={message:e.message,type:e._tag}),t){var n=r[o].types,i=n&&n[String(e._tag)];r[o]=a(o,t,r,e._tag,i?[].concat(i,e.message):e.message)}return r},{});return r(o,f)}),t.tap(function(){return t.sync(function(){return f.shouldUseNativeValidation&&e({},f)})}),t.match({onFailure:function(r){return{errors:r,values:{}}},onSuccess:function(r){return{errors:{},values:r}}}),t.runPromise)}}export{i as effectTsResolver};
//# sourceMappingURL=effect-ts.module.js.map
