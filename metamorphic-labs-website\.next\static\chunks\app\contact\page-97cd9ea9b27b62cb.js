(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[977],{285:(e,t,r)=>{"use strict";r.d(t,{$:()=>l});var a=r(5155);r(2115);var i=r(4624),s=r(2085),n=r(9434);let o=(0,s.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline",gradient:"gradient-metamorphic text-white shadow-xs hover:opacity-90 transition-opacity"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:t,variant:r,size:s,asChild:l=!1,...d}=e,c=l?i.DX:"button";return(0,a.jsx)(c,{"data-slot":"button",className:(0,n.cn)(o({variant:r,size:s,className:t})),...d})}},1831:(e,t,r)=>{"use strict";r.d(t,{ContactForm:()=>h});var a=r(5155),i=r(2115),s=r(285),n=r(9434);function o(e){let{className:t,type:r,...i}=e;return(0,a.jsx)("input",{type:r,"data-slot":"input",className:(0,n.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...i})}function l(e){let{className:t,...r}=e;return(0,a.jsx)("textarea",{"data-slot":"textarea",className:(0,n.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),...r})}var d=r(6695),c=r(9946);let u=(0,c.A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),m=(0,c.A)("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]]),g=["General Inquiry","Catalyst - Prompt Engineering","Metamorphic Reactor - Multi-AI Orchestration","Vault 024 - Generative Art & NFTs","Partnership Opportunities","Technical Support","Investment & Funding","Other"];function h(){let[e,t]=(0,i.useState)({name:"",email:"",interest:"",message:""}),[r,n]=(0,i.useState)(!1),[c,h]=(0,i.useState)(!1),b=async e=>{e.preventDefault(),n(!0),await new Promise(e=>setTimeout(e,2e3)),n(!1),h(!0),setTimeout(()=>{h(!1),t({name:"",email:"",interest:"",message:""})},3e3)},x=e=>{t(t=>({...t,[e.target.name]:e.target.value}))};return c?(0,a.jsx)(d.Zp,{className:"bg-gray-900 border-gray-800",children:(0,a.jsxs)(d.Wu,{className:"p-8 text-center",children:[(0,a.jsx)(u,{className:"h-16 w-16 text-green-500 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-white mb-2",children:"Message Sent!"}),(0,a.jsx)("p",{className:"text-gray-300",children:"Thank you for reaching out. We'll get back to you within 24 hours."})]})}):(0,a.jsx)(d.Zp,{className:"bg-gray-900 border-gray-800",children:(0,a.jsx)(d.Wu,{className:"p-8",children:(0,a.jsxs)("form",{onSubmit:b,className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-white mb-2",children:"Name *"}),(0,a.jsx)(o,{id:"name",name:"name",type:"text",required:!0,value:e.name,onChange:x,className:"bg-gray-800 border-gray-700 text-white placeholder-gray-400 focus:border-blue-500",placeholder:"Your full name"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-white mb-2",children:"Email *"}),(0,a.jsx)(o,{id:"email",name:"email",type:"email",required:!0,value:e.email,onChange:x,className:"bg-gray-800 border-gray-700 text-white placeholder-gray-400 focus:border-blue-500",placeholder:"<EMAIL>"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"interest",className:"block text-sm font-medium text-white mb-2",children:"Area of Interest"}),(0,a.jsxs)("select",{id:"interest",name:"interest",value:e.interest,onChange:x,className:"w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-md text-white focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"",children:"Select an option"}),g.map(e=>(0,a.jsx)("option",{value:e,children:e},e))]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"message",className:"block text-sm font-medium text-white mb-2",children:"Message *"}),(0,a.jsx)(l,{id:"message",name:"message",required:!0,value:e.message,onChange:x,rows:5,className:"bg-gray-800 border-gray-700 text-white placeholder-gray-400 focus:border-blue-500 resize-none",placeholder:"Tell us about your project, questions, or how we can help..."})]}),(0,a.jsx)(s.$,{type:"submit",disabled:r,className:"w-full gradient-metamorphic text-white font-semibold py-3 disabled:opacity-50",children:r?(0,a.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}),"Sending..."]}):(0,a.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,a.jsx)(m,{className:"h-4 w-4"}),"Send Message"]})})]})})})}},4987:(e,t,r)=>{Promise.resolve().then(r.bind(r,1831))},6695:(e,t,r)=>{"use strict";r.d(t,{Wu:()=>n,Zp:()=>s});var a=r(5155);r(2115);var i=r(9434);function s(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,i.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...r})}function n(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,i.cn)("px-6",t),...r})}},9434:(e,t,r)=>{"use strict";r.d(t,{cn:()=>s});var a=r(2596),i=r(9688);function s(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,i.QP)((0,a.$)(t))}}},e=>{var t=t=>e(e.s=t);e.O(0,[497,441,684,358],()=>t(4987)),_N_E=e.O()}]);