'use client';

import { motion } from 'framer-motion';
import { useInView } from 'framer-motion';
import { useRef } from 'react';
import { Brain, Network, Palette, Zap } from 'lucide-react';

const features = [
  {
    name: 'Metamorphic AI Ecosystem',
    description: 'Comprehensive AI platform integrating multiple models and agents for complex problem-solving and creative tasks.',
    icon: Brain,
  },
  {
    name: 'Multi-AI Orchestration',
    description: 'Advanced orchestration system enabling seamless collaboration between different AI models and agents.',
    icon: Network,
  },
  {
    name: 'Generative Art + NFTs',
    description: 'Cutting-edge platform for creating, showcasing, and trading AI-generated art and digital collectibles.',
    icon: Palette,
  },
  {
    name: 'Prompt Engineering',
    description: 'Sophisticated tools and methodologies for optimizing AI interactions and developing prompt ecosystems.',
    icon: Zap,
  },
];

export function FeaturesGrid() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  return (
    <section ref={ref} className="py-24 sm:py-32 bg-gray-900/20">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
          transition={{ duration: 0.8 }}
          className="mx-auto max-w-2xl text-center mb-16"
        >
          <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl lg:text-5xl">
            Our <span className="gradient-text">Core Capabilities</span>
          </h2>
          <p className="mt-6 text-lg leading-8 text-gray-300">
            Discover the technologies and methodologies that power our innovative solutions
          </p>
        </motion.div>

        <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4">
          {features.map((feature, index) => (
            <motion.div
              key={feature.name}
              initial={{ opacity: 0, y: 20 }}
              animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
              transition={{ duration: 0.8, delay: index * 0.1 }}
              className="group relative"
            >
              <div className="holographic-hover rounded-2xl bg-gray-900 p-8 border border-gray-800 h-full">
                <div className="flex items-center justify-center w-12 h-12 rounded-lg bg-gradient-to-br from-blue-500 to-purple-600 mb-6">
                  <feature.icon className="h-6 w-6 text-white" />
                </div>
                
                <h3 className="text-xl font-semibold text-white mb-4 group-hover:gradient-text transition-all duration-300">
                  {feature.name}
                </h3>
                
                <p className="text-gray-300 leading-relaxed">
                  {feature.description}
                </p>

                {/* Hover effect overlay */}
                <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-blue-500/5 to-purple-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
              </div>
            </motion.div>
          ))}
        </div>

        {/* Additional info section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="mt-16 text-center"
        >
          <div className="rounded-2xl bg-gradient-to-r from-blue-900/20 to-purple-900/20 p-8 border border-gray-800">
            <h3 className="text-2xl font-bold text-white mb-4">
              Integrated Innovation
            </h3>
            <p className="text-gray-300 max-w-3xl mx-auto">
              Our features work together seamlessly, creating a powerful ecosystem where AI orchestration, 
              prompt engineering, and creative generation combine to deliver unprecedented capabilities. 
              Each component enhances the others, resulting in solutions that are greater than the sum of their parts.
            </p>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
