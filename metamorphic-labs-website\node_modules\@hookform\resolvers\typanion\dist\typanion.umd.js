!function(e,o){"object"==typeof exports&&"undefined"!=typeof module?o(exports,require("@hookform/resolvers")):"function"==typeof define&&define.amd?define(["exports","@hookform/resolvers"],o):o((e||self).hookformResolversTypanion={},e.hookformResolvers)}(this,function(e,o){e.typanionResolver=function(e,r){return void 0===r&&(r={}),function(n,s,i){var t=[],f=e(n,Object.assign({},{errors:t},r)),l=function(e,o){return void 0===o&&(o={}),e.reduce(function(e,o){var r=o.indexOf(":"),n=o.slice(1,r),s=o.slice(r+1).trim();return e[n]={message:s},e},o)}(t);return f?(i.shouldUseNativeValidation&&o.validateFieldsNatively(l,i),{values:n,errors:{}}):{values:{},errors:o.toNestErrors(l,i)}}}});
//# sourceMappingURL=typanion.umd.js.map
