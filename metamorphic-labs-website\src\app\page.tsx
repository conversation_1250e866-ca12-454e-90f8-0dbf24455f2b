import type { Metadata } from 'next';
import { HeroSection } from '@/components/hero-section';
import { IntroSection } from '@/components/intro-section';
import { FeaturesGrid } from '@/components/features-grid';
import { ProjectsPreview } from '@/components/projects-preview';
import { StructuredData, organizationSchema, websiteSchema } from '@/components/structured-data';

export const metadata: Metadata = {
  title: 'Metamorphic Labs | Redefining Reality with AI & Quantum Systems',
  description: 'Metamorphic Labs pioneers next-generation AI systems, multi-agent orchestration, generative creativity, and boundary-pushing software solutions. From deep prompt ecosystems to decentralized art galleries—we build the future.',
};

export default function Home() {
  return (
    <>
      <StructuredData data={organizationSchema} />
      <StructuredData data={websiteSchema} />
      <div className="bg-black text-white">
        <HeroSection />
        <IntroSection />
        <FeaturesGrid />
        <ProjectsPreview />
      </div>
    </>
  );
}
