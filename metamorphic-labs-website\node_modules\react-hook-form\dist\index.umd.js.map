{"version": 3, "file": "index.umd.js", "sources": ["../src/utils/isCheckBoxInput.ts", "../src/utils/isDateObject.ts", "../src/utils/isNullOrUndefined.ts", "../src/utils/isObject.ts", "../src/logic/getEventValue.ts", "../src/logic/isNameInFieldArray.ts", "../src/logic/getNodeParentName.ts", "../src/utils/isWeb.ts", "../src/utils/cloneObject.ts", "../src/utils/isPlainObject.ts", "../src/utils/isKey.ts", "../src/utils/isUndefined.ts", "../src/utils/compact.ts", "../src/utils/stringToPath.ts", "../src/utils/get.ts", "../src/utils/isBoolean.ts", "../src/utils/set.ts", "../src/constants.ts", "../src/useFormContext.tsx", "../src/logic/getProxyFormState.ts", "../src/useIsomorphicLayoutEffect.ts", "../src/useFormState.ts", "../src/utils/isString.ts", "../src/logic/generateWatchOutput.ts", "../src/useWatch.ts", "../src/useController.ts", "../src/controller.tsx", "../src/utils/flatten.ts", "../src/form.tsx", "../src/logic/appendErrors.ts", "../src/utils/convertToArrayPayload.ts", "../src/utils/createSubject.ts", "../src/utils/isPrimitive.ts", "../src/utils/deepEqual.ts", "../src/utils/isEmptyObject.ts", "../src/utils/isFileInput.ts", "../src/utils/isFunction.ts", "../src/utils/isHTMLElement.ts", "../src/utils/isMultipleSelect.ts", "../src/utils/isRadioInput.ts", "../src/utils/live.ts", "../src/utils/unset.ts", "../src/utils/objectHasFunction.ts", "../src/logic/getDirtyFields.ts", "../src/logic/getCheckboxValue.ts", "../src/logic/getFieldValueAs.ts", "../src/logic/getRadioValue.ts", "../src/logic/getFieldValue.ts", "../src/logic/getResolverOptions.ts", "../src/utils/isRegex.ts", "../src/logic/getRuleValue.ts", "../src/logic/getValidationModes.ts", "../src/logic/hasPromiseValidation.ts", "../src/logic/isWatched.ts", "../src/logic/iterateFieldsByAction.ts", "../src/logic/schemaErrorLookup.ts", "../src/logic/shouldRenderFormState.ts", "../src/logic/updateFieldArrayRootError.ts", "../src/utils/isMessage.ts", "../src/logic/getValidateError.ts", "../src/logic/getValueAndMessage.ts", "../src/logic/validateField.ts", "../src/logic/createFormControl.ts", "../src/logic/hasValidation.ts", "../src/logic/skipValidation.ts", "../src/logic/shouldSubscribeByName.ts", "../src/utils/isRadioOrCheckbox.ts", "../src/logic/unsetEmptyArray.ts", "../src/logic/generateId.ts", "../src/logic/getFocusFieldName.ts", "../src/utils/append.ts", "../src/utils/fillEmptyArray.ts", "../src/utils/insert.ts", "../src/utils/move.ts", "../src/utils/prepend.ts", "../src/utils/remove.ts", "../src/utils/swap.ts", "../src/utils/update.ts", "../src/useFieldArray.ts", "../src/useForm.ts"], "sourcesContent": ["import type { FieldElement } from '../types';\n\nexport default (element: FieldElement): element is HTMLInputElement =>\n  element.type === 'checkbox';\n", "export default (value: unknown): value is Date => value instanceof Date;\n", "export default (value: unknown): value is null | undefined => value == null;\n", "import isDateObject from './isDateObject';\nimport isNullOrUndefined from './isNullOrUndefined';\n\nexport const isObjectType = (value: unknown): value is object =>\n  typeof value === 'object';\n\nexport default <T extends object>(value: unknown): value is T =>\n  !isNullOrUndefined(value) &&\n  !Array.isArray(value) &&\n  isObjectType(value) &&\n  !isDateObject(value);\n", "import isCheckBoxInput from '../utils/isCheckBoxInput';\nimport isObject from '../utils/isObject';\n\ntype Event = { target: any };\n\nexport default (event: unknown) =>\n  isObject(event) && (event as Event).target\n    ? isCheckBoxInput((event as Event).target)\n      ? (event as Event).target.checked\n      : (event as Event).target.value\n    : event;\n", "import type { InternalFieldName } from '../types';\n\nimport getNodeParentName from './getNodeParentName';\n\nexport default (names: Set<InternalFieldName>, name: InternalFieldName) =>\n  names.has(getNodeParentName(name));\n", "export default (name: string) =>\n  name.substring(0, name.search(/\\.\\d+(\\.|$)/)) || name;\n", "export default typeof window !== 'undefined' &&\n  typeof window.HTMLElement !== 'undefined' &&\n  typeof document !== 'undefined';\n", "import isObject from './isObject';\nimport isPlainObject from './isPlainObject';\nimport isWeb from './isWeb';\n\nexport default function cloneObject<T>(data: T): T {\n  let copy: any;\n  const isArray = Array.isArray(data);\n  const isFileListInstance =\n    typeof FileList !== 'undefined' ? data instanceof FileList : false;\n\n  if (data instanceof Date) {\n    copy = new Date(data);\n  } else if (data instanceof Set) {\n    copy = new Set(data);\n  } else if (\n    !(isWeb && (data instanceof Blob || isFileListInstance)) &&\n    (isArray || isObject(data))\n  ) {\n    copy = isArray ? [] : {};\n\n    if (!isArray && !isPlainObject(data)) {\n      copy = data;\n    } else {\n      for (const key in data) {\n        if (data.hasOwnProperty(key)) {\n          copy[key] = cloneObject(data[key]);\n        }\n      }\n    }\n  } else {\n    return data;\n  }\n\n  return copy;\n}\n", "import isObject from './isObject';\n\nexport default (tempObject: object) => {\n  const prototypeCopy =\n    tempObject.constructor && tempObject.constructor.prototype;\n\n  return (\n    isObject(prototypeCopy) && prototypeCopy.hasOwnProperty('isPrototypeOf')\n  );\n};\n", "export default (value: string) => /^\\w*$/.test(value);\n", "export default (val: unknown): val is undefined => val === undefined;\n", "export default <TValue>(value: TValue[]) =>\n  Array.isArray(value) ? value.filter(Boolean) : [];\n", "import compact from './compact';\n\nexport default (input: string): string[] =>\n  compact(input.replace(/[\"|']|\\]/g, '').split(/\\.|\\[/));\n", "import isKey from './isKey';\nimport isNullOrUndefined from './isNullOrUndefined';\nimport isObject from './isObject';\nimport isUndefined from './isUndefined';\nimport stringToPath from './stringToPath';\n\nexport default <T>(\n  object: T,\n  path?: string | null,\n  defaultValue?: unknown,\n): any => {\n  if (!path || !isObject(object)) {\n    return defaultValue;\n  }\n\n  const result = (isKey(path) ? [path] : stringToPath(path)).reduce(\n    (result, key) =>\n      isNullOrUndefined(result) ? result : result[key as keyof {}],\n    object,\n  );\n\n  return isUndefined(result) || result === object\n    ? isUndefined(object[path as keyof T])\n      ? defaultValue\n      : object[path as keyof T]\n    : result;\n};\n", "export default (value: unknown): value is boolean => typeof value === 'boolean';\n", "import type { FieldPath, FieldValues } from '../types';\n\nimport isKey from './isKey';\nimport isObject from './isObject';\nimport stringToPath from './stringToPath';\n\nexport default (\n  object: FieldValues,\n  path: FieldPath<FieldValues>,\n  value?: unknown,\n) => {\n  let index = -1;\n  const tempPath = isKey(path) ? [path] : stringToPath(path);\n  const length = tempPath.length;\n  const lastIndex = length - 1;\n\n  while (++index < length) {\n    const key = tempPath[index];\n    let newValue = value;\n\n    if (index !== lastIndex) {\n      const objValue = object[key];\n      newValue =\n        isObject(objValue) || Array.isArray(objValue)\n          ? objValue\n          : !isNaN(+tempPath[index + 1])\n            ? []\n            : {};\n    }\n\n    if (key === '__proto__' || key === 'constructor' || key === 'prototype') {\n      return;\n    }\n\n    object[key] = newValue;\n    object = object[key];\n  }\n};\n", "export const EVENTS = {\n  BLUR: 'blur',\n  FOCUS_OUT: 'focusout',\n  CHANGE: 'change',\n} as const;\n\nexport const VALIDATION_MODE = {\n  onBlur: 'onBlur',\n  onChange: 'onChange',\n  onSubmit: 'onSubmit',\n  onTouched: 'onTouched',\n  all: 'all',\n} as const;\n\nexport const INPUT_VALIDATION_RULES = {\n  max: 'max',\n  min: 'min',\n  maxLength: 'maxLength',\n  minLength: 'minLength',\n  pattern: 'pattern',\n  required: 'required',\n  validate: 'validate',\n} as const;\n", "import React from 'react';\n\nimport type { FieldValues, FormProviderProps, UseFormReturn } from './types';\n\nconst HookFormContext = React.createContext<UseFormReturn | null>(null);\nHookFormContext.displayName = 'HookFormContext';\n\n/**\n * This custom hook allows you to access the form context. useFormContext is intended to be used in deeply nested structures, where it would become inconvenient to pass the context as a prop. To be used with {@link FormProvider}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @returns return all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */\nexport const useFormContext = <\n  TFieldValues extends FieldValues,\n  TContext = any,\n  TTransformedValues = TFieldValues,\n>(): UseFormReturn<TFieldValues, TContext, TTransformedValues> =>\n  React.useContext(HookFormContext) as UseFormReturn<\n    TFieldValues,\n    TContext,\n    TTransformedValues\n  >;\n\n/**\n * A provider component that propagates the `useForm` methods to all children components via [React Context](https://reactjs.org/docs/context.html) API. To be used with {@link useFormContext}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @param props - all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */\nexport const FormProvider = <\n  TFieldValues extends FieldValues,\n  TContext = any,\n  TTransformedValues = TFieldValues,\n>(\n  props: FormProviderProps<TFieldValues, TContext, TTransformedValues>,\n) => {\n  const { children, ...data } = props;\n  return (\n    <HookFormContext.Provider value={data as unknown as UseFormReturn}>\n      {children}\n    </HookFormContext.Provider>\n  );\n};\n", "import { VALIDATION_MODE } from '../constants';\nimport type { Control, FieldValues, FormState, ReadFormState } from '../types';\n\nexport default <\n  TFieldValues extends FieldValues,\n  TContext = any,\n  TTransformedValues = TFieldValues,\n>(\n  formState: FormState<TFieldValues>,\n  control: Control<TFieldValues, TContext, TTransformedValues>,\n  localProxyFormState?: ReadFormState,\n  isRoot = true,\n) => {\n  const result = {\n    defaultValues: control._defaultValues,\n  } as typeof formState;\n\n  for (const key in formState) {\n    Object.defineProperty(result, key, {\n      get: () => {\n        const _key = key as keyof FormState<TFieldValues> & keyof ReadFormState;\n\n        if (control._proxyFormState[_key] !== VALIDATION_MODE.all) {\n          control._proxyFormState[_key] = !isRoot || VALIDATION_MODE.all;\n        }\n\n        localProxyFormState && (localProxyFormState[_key] = true);\n        return formState[_key];\n      },\n    });\n  }\n\n  return result;\n};\n", "import * as React from 'react';\n\nexport const useIsomorphicLayoutEffect =\n  typeof window !== 'undefined' ? React.useLayoutEffect : React.useEffect;\n", "import React from 'react';\n\nimport getProxyFormState from './logic/getProxyFormState';\nimport type {\n  FieldValues,\n  UseFormStateProps,\n  UseFormStateReturn,\n} from './types';\nimport { useFormContext } from './useFormContext';\nimport { useIsomorphicLayoutEffect } from './useIsomorphicLayoutEffect';\n\n/**\n * This custom hook allows you to subscribe to each form state, and isolate the re-render at the custom hook level. It has its scope in terms of form state subscription, so it would not affect other useFormState and useForm. Using this hook can reduce the re-render impact on large and complex form application.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformstate) • [Demo](https://codesandbox.io/s/useformstate-75xly)\n *\n * @param props - include options on specify fields to subscribe. {@link UseFormStateReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, control } = useForm({\n *     defaultValues: {\n *     firstName: \"firstName\"\n *   }});\n *   const { dirtyFields } = useFormState({\n *     control\n *   });\n *   const onSubmit = (data) => console.log(data);\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input {...register(\"firstName\")} placeholder=\"First Name\" />\n *       {dirtyFields.firstName && <p>Field is dirty.</p>}\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */\nexport function useFormState<\n  TFieldValues extends FieldValues = FieldValues,\n  TTransformedValues = TFieldValues,\n>(\n  props?: UseFormStateProps<TFieldValues, TTransformedValues>,\n): UseFormStateReturn<TFieldValues> {\n  const methods = useFormContext<TFieldValues, any, TTransformedValues>();\n  const { control = methods.control, disabled, name, exact } = props || {};\n  const [formState, updateFormState] = React.useState(control._formState);\n  const _localProxyFormState = React.useRef({\n    isDirty: false,\n    isLoading: false,\n    dirtyFields: false,\n    touchedFields: false,\n    validatingFields: false,\n    isValidating: false,\n    isValid: false,\n    errors: false,\n  });\n\n  useIsomorphicLayoutEffect(\n    () =>\n      control._subscribe({\n        name,\n        formState: _localProxyFormState.current,\n        exact,\n        callback: (formState) => {\n          !disabled &&\n            updateFormState({\n              ...control._formState,\n              ...formState,\n            });\n        },\n      }),\n    [name, disabled, exact],\n  );\n\n  React.useEffect(() => {\n    _localProxyFormState.current.isValid && control._setValid(true);\n  }, [control]);\n\n  return React.useMemo(\n    () =>\n      getProxyFormState(\n        formState,\n        control,\n        _localProxyFormState.current,\n        false,\n      ),\n    [formState, control],\n  );\n}\n", "export default (value: unknown): value is string => typeof value === 'string';\n", "import type { DeepPartial, FieldValues, Names } from '../types';\nimport get from '../utils/get';\nimport isString from '../utils/isString';\n\nexport default <T>(\n  names: string | string[] | undefined,\n  _names: Names,\n  formValues?: FieldValues,\n  isGlobal?: boolean,\n  defaultValue?: DeepPartial<T> | unknown,\n) => {\n  if (isString(names)) {\n    isGlobal && _names.watch.add(names);\n    return get(formValues, names, defaultValue);\n  }\n\n  if (Array.isArray(names)) {\n    return names.map(\n      (fieldName) => (\n        isGlobal && _names.watch.add(fieldName), get(formValues, fieldName)\n      ),\n    );\n  }\n\n  isGlobal && (_names.watchAll = true);\n\n  return formValues;\n};\n", "import React from 'react';\n\nimport generateWatchOutput from './logic/generateWatchOutput';\nimport type {\n  Control,\n  DeepPartialSkipArrayKey,\n  FieldPath,\n  FieldPathValue,\n  FieldPathValues,\n  FieldValues,\n  InternalFieldName,\n  UseWatchProps,\n} from './types';\nimport { useFormContext } from './useFormContext';\nimport { useIsomorphicLayoutEffect } from './useIsomorphicLayoutEffect';\n\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @param props - defaultValue, disable subscription and match exact name.\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   control,\n *   name: \"fieldA\",\n *   defaultValue: \"default value\",\n *   exact: false,\n * })\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n  TFieldName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n  TTransformedValues = TFieldValues,\n>(props: {\n  name: TFieldName;\n  defaultValue?: FieldPathValue<TFieldValues, TFieldName>;\n  control?: Control<TFieldValues, any, TTransformedValues>;\n  disabled?: boolean;\n  exact?: boolean;\n}): FieldPathValue<TFieldValues, TFieldName>;\n/**\n * Subscribe to the entire form values change and re-render at the hook level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @param props - defaultValue, disable subscription and match exact name.\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   control,\n *   defaultValue: {\n *     name: \"data\"\n *   },\n *   exact: false,\n * })\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n  TTransformedValues = TFieldValues,\n>(props: {\n  defaultValue?: DeepPartialSkipArrayKey<TFieldValues>;\n  control?: Control<TFieldValues, any, TTransformedValues>;\n  disabled?: boolean;\n  exact?: boolean;\n}): DeepPartialSkipArrayKey<TFieldValues>;\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @param props - defaultValue, disable subscription and match exact name.\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   control,\n *   name: [\"fieldA\", \"fieldB\"],\n *   defaultValue: {\n *     fieldA: \"data\",\n *     fieldB: \"data\"\n *   },\n *   exact: false,\n * })\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n  TFieldNames extends\n    readonly FieldPath<TFieldValues>[] = readonly FieldPath<TFieldValues>[],\n  TTransformedValues = TFieldValues,\n>(props: {\n  name: readonly [...TFieldNames];\n  defaultValue?: DeepPartialSkipArrayKey<TFieldValues>;\n  control?: Control<TFieldValues, any, TTransformedValues>;\n  disabled?: boolean;\n  exact?: boolean;\n}): FieldPathValues<TFieldValues, TFieldNames>;\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @example\n * ```tsx\n * // can skip passing down the control into useWatch if the form is wrapped with the FormProvider\n * const values = useWatch()\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n>(): DeepPartialSkipArrayKey<TFieldValues>;\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   name: \"fieldName\"\n *   control,\n * })\n * ```\n */\nexport function useWatch<TFieldValues extends FieldValues>(\n  props?: UseWatchProps<TFieldValues>,\n) {\n  const methods = useFormContext<TFieldValues>();\n  const {\n    control = methods.control,\n    name,\n    defaultValue,\n    disabled,\n    exact,\n  } = props || {};\n  const _defaultValue = React.useRef(defaultValue);\n  const [value, updateValue] = React.useState(\n    control._getWatch(\n      name as InternalFieldName,\n      _defaultValue.current as DeepPartialSkipArrayKey<TFieldValues>,\n    ),\n  );\n\n  useIsomorphicLayoutEffect(\n    () =>\n      control._subscribe({\n        name,\n        formState: {\n          values: true,\n        },\n        exact,\n        callback: (formState) =>\n          !disabled &&\n          updateValue(\n            generateWatchOutput(\n              name as InternalFieldName | InternalFieldName[],\n              control._names,\n              formState.values || control._formValues,\n              false,\n              _defaultValue.current,\n            ),\n          ),\n      }),\n    [name, control, disabled, exact],\n  );\n\n  React.useEffect(() => control._removeUnmounted());\n\n  return value;\n}\n", "import React from 'react';\n\nimport getEventValue from './logic/getEventValue';\nimport isNameInFieldArray from './logic/isNameInFieldArray';\nimport cloneObject from './utils/cloneObject';\nimport get from './utils/get';\nimport isBoolean from './utils/isBoolean';\nimport isUndefined from './utils/isUndefined';\nimport set from './utils/set';\nimport { EVENTS } from './constants';\nimport type {\n  ControllerFieldState,\n  Field,\n  FieldPath,\n  FieldPathValue,\n  FieldValues,\n  InternalFieldName,\n  UseControllerProps,\n  UseControllerReturn,\n} from './types';\nimport { useFormContext } from './useFormContext';\nimport { useFormState } from './useFormState';\nimport { useWatch } from './useWatch';\n\n/**\n * Custom hook to work with controlled component, this function provide you with both form and field level state. Re-render is isolated at the hook level.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller) • [Demo](https://codesandbox.io/s/usecontroller-0o8px)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns field properties, field and form state. {@link UseControllerReturn}\n *\n * @example\n * ```tsx\n * function Input(props) {\n *   const { field, fieldState, formState } = useController(props);\n *   return (\n *     <div>\n *       <input {...field} placeholder={props.name} />\n *       <p>{fieldState.isTouched && \"Touched\"}</p>\n *       <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *     </div>\n *   );\n * }\n * ```\n */\nexport function useController<\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n  TTransformedValues = TFieldValues,\n>(\n  props: UseControllerProps<TFieldValues, TName, TTransformedValues>,\n): UseControllerReturn<TFieldValues, TName> {\n  const methods = useFormContext<TFieldValues, any, TTransformedValues>();\n  const { name, disabled, control = methods.control, shouldUnregister } = props;\n  const isArrayField = isNameInFieldArray(control._names.array, name);\n  const value = useWatch({\n    control,\n    name,\n    defaultValue: get(\n      control._formValues,\n      name,\n      get(control._defaultValues, name, props.defaultValue),\n    ),\n    exact: true,\n  }) as FieldPathValue<TFieldValues, TName>;\n  const formState = useFormState({\n    control,\n    name,\n    exact: true,\n  });\n\n  const _props = React.useRef(props);\n  const _registerProps = React.useRef(\n    control.register(name, {\n      ...props.rules,\n      value,\n      ...(isBoolean(props.disabled) ? { disabled: props.disabled } : {}),\n    }),\n  );\n\n  const fieldState = React.useMemo(\n    () =>\n      Object.defineProperties(\n        {},\n        {\n          invalid: {\n            enumerable: true,\n            get: () => !!get(formState.errors, name),\n          },\n          isDirty: {\n            enumerable: true,\n            get: () => !!get(formState.dirtyFields, name),\n          },\n          isTouched: {\n            enumerable: true,\n            get: () => !!get(formState.touchedFields, name),\n          },\n          isValidating: {\n            enumerable: true,\n            get: () => !!get(formState.validatingFields, name),\n          },\n          error: {\n            enumerable: true,\n            get: () => get(formState.errors, name),\n          },\n        },\n      ) as ControllerFieldState,\n    [formState, name],\n  );\n\n  const onChange = React.useCallback(\n    (event: any) =>\n      _registerProps.current.onChange({\n        target: {\n          value: getEventValue(event),\n          name: name as InternalFieldName,\n        },\n        type: EVENTS.CHANGE,\n      }),\n    [name],\n  );\n\n  const onBlur = React.useCallback(\n    () =>\n      _registerProps.current.onBlur({\n        target: {\n          value: get(control._formValues, name),\n          name: name as InternalFieldName,\n        },\n        type: EVENTS.BLUR,\n      }),\n    [name, control._formValues],\n  );\n\n  const ref = React.useCallback(\n    (elm: any) => {\n      const field = get(control._fields, name);\n\n      if (field && elm) {\n        field._f.ref = {\n          focus: () => elm.focus && elm.focus(),\n          select: () => elm.select && elm.select(),\n          setCustomValidity: (message: string) =>\n            elm.setCustomValidity(message),\n          reportValidity: () => elm.reportValidity(),\n        };\n      }\n    },\n    [control._fields, name],\n  );\n\n  const field = React.useMemo(\n    () => ({\n      name,\n      value,\n      ...(isBoolean(disabled) || formState.disabled\n        ? { disabled: formState.disabled || disabled }\n        : {}),\n      onChange,\n      onBlur,\n      ref,\n    }),\n    [name, disabled, formState.disabled, onChange, onBlur, ref, value],\n  );\n\n  React.useEffect(() => {\n    const _shouldUnregisterField =\n      control._options.shouldUnregister || shouldUnregister;\n\n    control.register(name, {\n      ..._props.current.rules,\n      ...(isBoolean(_props.current.disabled)\n        ? { disabled: _props.current.disabled }\n        : {}),\n    });\n\n    const updateMounted = (name: InternalFieldName, value: boolean) => {\n      const field: Field = get(control._fields, name);\n\n      if (field && field._f) {\n        field._f.mount = value;\n      }\n    };\n\n    updateMounted(name, true);\n\n    if (_shouldUnregisterField) {\n      const value = cloneObject(get(control._options.defaultValues, name));\n      set(control._defaultValues, name, value);\n      if (isUndefined(get(control._formValues, name))) {\n        set(control._formValues, name, value);\n      }\n    }\n\n    !isArrayField && control.register(name);\n\n    return () => {\n      (\n        isArrayField\n          ? _shouldUnregisterField && !control._state.action\n          : _shouldUnregisterField\n      )\n        ? control.unregister(name)\n        : updateMounted(name, false);\n    };\n  }, [name, control, isArrayField, shouldUnregister]);\n\n  React.useEffect(() => {\n    control._setDisabledField({\n      disabled,\n      name,\n    });\n  }, [disabled, name, control]);\n\n  return React.useMemo(\n    () => ({\n      field,\n      formState,\n      fieldState,\n    }),\n    [field, formState, fieldState],\n  );\n}\n", "import type { ControllerProps, FieldPath, FieldValues } from './types';\nimport { useController } from './useController';\n\n/**\n * Component based on `useController` hook to work with controlled component.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller/controller) • [Demo](https://codesandbox.io/s/react-hook-form-v6-controller-ts-jwyzw) • [Video](https://www.youtube.com/watch?v=N2UNk_UCVyA)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns provide field handler functions, field and form state.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control } = useForm<FormValues>({\n *     defaultValues: {\n *       test: \"\"\n *     }\n *   });\n *\n *   return (\n *     <form>\n *       <Controller\n *         control={control}\n *         name=\"test\"\n *         render={({ field: { onChange, onBlur, value, ref }, formState, fieldState }) => (\n *           <>\n *             <input\n *               onChange={onChange} // send value to hook form\n *               onBlur={onBlur} // notify when input is touched\n *               value={value} // return updated value\n *               ref={ref} // set ref for focus management\n *             />\n *             <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *             <p>{fieldState.isTouched ? \"touched\" : \"\"}</p>\n *           </>\n *         )}\n *       />\n *     </form>\n *   );\n * }\n * ```\n */\nconst Controller = <\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n  TTransformedValues = TFieldValues,\n>(\n  props: ControllerProps<TFieldValues, TName, TTransformedValues>,\n) =>\n  props.render(useController<TFieldValues, TName, TTransformedValues>(props));\n\nexport { Controller };\n", "import type { FieldValues } from '../types';\n\nimport { isObjectType } from './isObject';\n\nexport const flatten = (obj: FieldValues) => {\n  const output: FieldValues = {};\n\n  for (const key of Object.keys(obj)) {\n    if (isObjectType(obj[key]) && obj[key] !== null) {\n      const nested = flatten(obj[key]);\n\n      for (const nestedKey of Object.keys(nested)) {\n        output[`${key}.${nestedKey}`] = nested[nestedKey];\n      }\n    } else {\n      output[key] = obj[key];\n    }\n  }\n\n  return output;\n};\n", "import React from 'react';\n\nimport { flatten } from './utils/flatten';\nimport type { FieldValues, FormProps } from './types';\nimport { useFormContext } from './useFormContext';\n\nconst POST_REQUEST = 'post';\n\n/**\n * Form component to manage submission.\n *\n * @param props - to setup submission detail. {@link FormProps}\n *\n * @returns form component or headless render prop.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control, formState: { errors } } = useForm();\n *\n *   return (\n *     <Form action=\"/api\" control={control}>\n *       <input {...register(\"name\")} />\n *       <p>{errors?.root?.server && 'Server error'}</p>\n *       <button>Submit</button>\n *     </Form>\n *   );\n * }\n * ```\n */\nfunction Form<\n  TFieldValues extends FieldValues,\n  TTransformedValues = TFieldValues,\n>(props: FormProps<TFieldValues, TTransformedValues>) {\n  const methods = useFormContext<TFieldValues, any, TTransformedValues>();\n  const [mounted, setMounted] = React.useState(false);\n  const {\n    control = methods.control,\n    onSubmit,\n    children,\n    action,\n    method = POST_REQUEST,\n    headers,\n    encType,\n    onError,\n    render,\n    onSuccess,\n    validateStatus,\n    ...rest\n  } = props;\n\n  const submit = async (event?: React.BaseSyntheticEvent) => {\n    let hasError = false;\n    let type = '';\n\n    await control.handleSubmit(async (data) => {\n      const formData = new FormData();\n      let formDataJson = '';\n\n      try {\n        formDataJson = JSON.stringify(data);\n      } catch {}\n\n      const flattenFormValues = flatten(control._formValues);\n\n      for (const key in flattenFormValues) {\n        formData.append(key, flattenFormValues[key]);\n      }\n\n      if (onSubmit) {\n        await onSubmit({\n          data,\n          event,\n          method,\n          formData,\n          formDataJson,\n        });\n      }\n\n      if (action) {\n        try {\n          const shouldStringifySubmissionData = [\n            headers && headers['Content-Type'],\n            encType,\n          ].some((value) => value && value.includes('json'));\n\n          const response = await fetch(String(action), {\n            method,\n            headers: {\n              ...headers,\n              ...(encType ? { 'Content-Type': encType } : {}),\n            },\n            body: shouldStringifySubmissionData ? formDataJson : formData,\n          });\n\n          if (\n            response &&\n            (validateStatus\n              ? !validateStatus(response.status)\n              : response.status < 200 || response.status >= 300)\n          ) {\n            hasError = true;\n            onError && onError({ response });\n            type = String(response.status);\n          } else {\n            onSuccess && onSuccess({ response });\n          }\n        } catch (error: unknown) {\n          hasError = true;\n          onError && onError({ error });\n        }\n      }\n    })(event);\n\n    if (hasError && props.control) {\n      props.control._subjects.state.next({\n        isSubmitSuccessful: false,\n      });\n      props.control.setError('root.server', {\n        type,\n      });\n    }\n  };\n\n  React.useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  return render ? (\n    <>\n      {render({\n        submit,\n      })}\n    </>\n  ) : (\n    <form\n      noValidate={mounted}\n      action={action}\n      method={method}\n      encType={encType}\n      onSubmit={submit}\n      {...rest}\n    >\n      {children}\n    </form>\n  );\n}\n\nexport { Form };\n", "import type {\n  InternalFieldErrors,\n  InternalFieldName,\n  ValidateResult,\n} from '../types';\n\nexport default (\n  name: InternalFieldName,\n  validateAllFieldCriteria: boolean,\n  errors: InternalFieldErrors,\n  type: string,\n  message: ValidateResult,\n) =>\n  validateAllFieldCriteria\n    ? {\n        ...errors[name],\n        types: {\n          ...(errors[name] && errors[name]!.types ? errors[name]!.types : {}),\n          [type]: message || true,\n        },\n      }\n    : {};\n", "export default <T>(value: T) => (Array.isArray(value) ? value : [value]);\n", "import type { Noop } from '../types';\n\nexport type Observer<T> = {\n  next: (value: T) => void;\n};\n\nexport type Subscription = {\n  unsubscribe: Noop;\n};\n\nexport type Subject<T> = {\n  readonly observers: Observer<T>[];\n  subscribe: (value: Observer<T>) => Subscription;\n  unsubscribe: Noop;\n} & Observer<T>;\n\nexport default <T>(): Subject<T> => {\n  let _observers: Observer<T>[] = [];\n\n  const next = (value: T) => {\n    for (const observer of _observers) {\n      observer.next && observer.next(value);\n    }\n  };\n\n  const subscribe = (observer: Observer<T>): Subscription => {\n    _observers.push(observer);\n    return {\n      unsubscribe: () => {\n        _observers = _observers.filter((o) => o !== observer);\n      },\n    };\n  };\n\n  const unsubscribe = () => {\n    _observers = [];\n  };\n\n  return {\n    get observers() {\n      return _observers;\n    },\n    next,\n    subscribe,\n    unsubscribe,\n  };\n};\n", "import type { Primitive } from '../types';\n\nimport isNullOrUndefined from './isNullOrUndefined';\nimport { isObjectType } from './isObject';\n\nexport default (value: unknown): value is Primitive =>\n  isNullOrUndefined(value) || !isObjectType(value);\n", "import isObject from '../utils/isObject';\n\nimport isDateObject from './isDateObject';\nimport isPrimitive from './isPrimitive';\n\nexport default function deepEqual(\n  object1: any,\n  object2: any,\n  _internal_visited = new WeakSet(),\n) {\n  if (isPrimitive(object1) || isPrimitive(object2)) {\n    return object1 === object2;\n  }\n\n  if (isDateObject(object1) && isDateObject(object2)) {\n    return object1.getTime() === object2.getTime();\n  }\n\n  const keys1 = Object.keys(object1);\n  const keys2 = Object.keys(object2);\n\n  if (keys1.length !== keys2.length) {\n    return false;\n  }\n\n  if (_internal_visited.has(object1) || _internal_visited.has(object2)) {\n    return true;\n  }\n  _internal_visited.add(object1);\n  _internal_visited.add(object2);\n\n  for (const key of keys1) {\n    const val1 = object1[key];\n\n    if (!keys2.includes(key)) {\n      return false;\n    }\n\n    if (key !== 'ref') {\n      const val2 = object2[key];\n\n      if (\n        (isDateObject(val1) && isDateObject(val2)) ||\n        (isObject(val1) && isObject(val2)) ||\n        (Array.isArray(val1) && Array.isArray(val2))\n          ? !deepEqual(val1, val2, _internal_visited)\n          : val1 !== val2\n      ) {\n        return false;\n      }\n    }\n  }\n\n  return true;\n}\n", "import type { EmptyObject } from '../types';\n\nimport isObject from './isObject';\n\nexport default (value: unknown): value is EmptyObject =>\n  isObject(value) && !Object.keys(value).length;\n", "import type { FieldElement } from '../types';\n\nexport default (element: FieldElement): element is HTMLInputElement =>\n  element.type === 'file';\n", "export default (value: unknown): value is Function =>\n  typeof value === 'function';\n", "import isWeb from './isWeb';\n\nexport default (value: unknown): value is HTMLElement => {\n  if (!isWeb) {\n    return false;\n  }\n\n  const owner = value ? ((value as HTMLElement).ownerDocument as Document) : 0;\n  return (\n    value instanceof\n    (owner && owner.defaultView ? owner.defaultView.HTMLElement : HTMLElement)\n  );\n};\n", "import type { FieldElement } from '../types';\n\nexport default (element: FieldElement): element is HTMLSelectElement =>\n  element.type === `select-multiple`;\n", "import type { FieldElement } from '../types';\n\nexport default (element: FieldElement): element is HTMLInputElement =>\n  element.type === 'radio';\n", "import type { Ref } from '../types';\n\nimport isHTMLElement from './isHTMLElement';\n\nexport default (ref: Ref) => isHTMLElement(ref) && ref.isConnected;\n", "import isEmptyObject from './isEmptyObject';\nimport isKey from './isKey';\nimport isObject from './isObject';\nimport isUndefined from './isUndefined';\nimport stringToPath from './stringToPath';\n\nfunction baseGet(object: any, updatePath: (string | number)[]) {\n  const length = updatePath.slice(0, -1).length;\n  let index = 0;\n\n  while (index < length) {\n    object = isUndefined(object) ? index++ : object[updatePath[index++]];\n  }\n\n  return object;\n}\n\nfunction isEmptyArray(obj: unknown[]) {\n  for (const key in obj) {\n    if (obj.hasOwnProperty(key) && !isUndefined(obj[key])) {\n      return false;\n    }\n  }\n  return true;\n}\n\nexport default function unset(object: any, path: string | (string | number)[]) {\n  const paths = Array.isArray(path)\n    ? path\n    : isKey(path)\n      ? [path]\n      : stringToPath(path);\n\n  const childObject = paths.length === 1 ? object : baseGet(object, paths);\n\n  const index = paths.length - 1;\n  const key = paths[index];\n\n  if (childObject) {\n    delete childObject[key];\n  }\n\n  if (\n    index !== 0 &&\n    ((isObject(childObject) && isEmptyObject(childObject)) ||\n      (Array.isArray(childObject) && isEmptyArray(childObject)))\n  ) {\n    unset(object, paths.slice(0, -1));\n  }\n\n  return object;\n}\n", "import isFunction from './isFunction';\n\nexport default <T>(data: T): boolean => {\n  for (const key in data) {\n    if (isFunction(data[key])) {\n      return true;\n    }\n  }\n  return false;\n};\n", "import deepEqual from '../utils/deepEqual';\nimport isNullOrUndefined from '../utils/isNullOrUndefined';\nimport isObject from '../utils/isObject';\nimport isPrimitive from '../utils/isPrimitive';\nimport isUndefined from '../utils/isUndefined';\nimport objectHasFunction from '../utils/objectHasFunction';\n\nfunction markFieldsDirty<T>(data: T, fields: Record<string, any> = {}) {\n  const isParentNodeArray = Array.isArray(data);\n\n  if (isObject(data) || isParentNodeArray) {\n    for (const key in data) {\n      if (\n        Array.isArray(data[key]) ||\n        (isObject(data[key]) && !objectHasFunction(data[key]))\n      ) {\n        fields[key] = Array.isArray(data[key]) ? [] : {};\n        markFieldsDirty(data[key], fields[key]);\n      } else if (!isNullOrUndefined(data[key])) {\n        fields[key] = true;\n      }\n    }\n  }\n\n  return fields;\n}\n\nfunction getDirtyFieldsFromDefaultValues<T>(\n  data: T,\n  formValues: T,\n  dirtyFieldsFromValues: Record<\n    Extract<keyof T, string>,\n    ReturnType<typeof markFieldsDirty> | boolean\n  >,\n) {\n  const isParentNodeArray = Array.isArray(data);\n\n  if (isObject(data) || isParentNodeArray) {\n    for (const key in data) {\n      if (\n        Array.isArray(data[key]) ||\n        (isObject(data[key]) && !objectHasFunction(data[key]))\n      ) {\n        if (\n          isUndefined(formValues) ||\n          isPrimitive(dirtyFieldsFromValues[key])\n        ) {\n          dirtyFieldsFromValues[key] = Array.isArray(data[key])\n            ? markFieldsDirty(data[key], [])\n            : { ...markFieldsDirty(data[key]) };\n        } else {\n          getDirtyFieldsFromDefaultValues(\n            data[key],\n            isNullOrUndefined(formValues) ? {} : formValues[key],\n            dirtyFieldsFromValues[key],\n          );\n        }\n      } else {\n        dirtyFieldsFromValues[key] = !deepEqual(data[key], formValues[key]);\n      }\n    }\n  }\n\n  return dirtyFieldsFromValues;\n}\n\nexport default <T>(defaultValues: T, formValues: T) =>\n  getDirtyFieldsFromDefaultValues(\n    defaultValues,\n    formValues,\n    markFieldsDirty(formValues),\n  );\n", "import isUndefined from '../utils/isUndefined';\n\ntype CheckboxFieldResult = {\n  isValid: boolean;\n  value: string | string[] | boolean | undefined;\n};\n\nconst defaultResult: CheckboxFieldResult = {\n  value: false,\n  isValid: false,\n};\n\nconst validResult = { value: true, isValid: true };\n\nexport default (options?: HTMLInputElement[]): CheckboxFieldResult => {\n  if (Array.isArray(options)) {\n    if (options.length > 1) {\n      const values = options\n        .filter((option) => option && option.checked && !option.disabled)\n        .map((option) => option.value);\n      return { value: values, isValid: !!values.length };\n    }\n\n    return options[0].checked && !options[0].disabled\n      ? // @ts-expect-error expected to work in the browser\n        options[0].attributes && !isUndefined(options[0].attributes.value)\n        ? isUndefined(options[0].value) || options[0].value === ''\n          ? validResult\n          : { value: options[0].value, isValid: true }\n        : validResult\n      : defaultResult;\n  }\n\n  return defaultResult;\n};\n", "import type { Field, NativeFieldValue } from '../types';\nimport isString from '../utils/isString';\nimport isUndefined from '../utils/isUndefined';\n\nexport default <T extends NativeFieldValue>(\n  value: T,\n  { valueAsNumber, valueAsDate, setValueAs }: Field['_f'],\n) =>\n  isUndefined(value)\n    ? value\n    : valueAsNumber\n      ? value === ''\n        ? NaN\n        : value\n          ? +value\n          : value\n      : valueAsDate && isString(value)\n        ? new Date(value)\n        : setValueAs\n          ? setValueAs(value)\n          : value;\n", "type RadioFieldResult = {\n  isValid: boolean;\n  value: number | string | null;\n};\n\nconst defaultReturn: RadioFieldResult = {\n  isValid: false,\n  value: null,\n};\n\nexport default (options?: HTMLInputElement[]): RadioFieldResult =>\n  Array.isArray(options)\n    ? options.reduce(\n        (previous, option): RadioFieldResult =>\n          option && option.checked && !option.disabled\n            ? {\n                isValid: true,\n                value: option.value,\n              }\n            : previous,\n        defaultReturn,\n      )\n    : defaultReturn;\n", "import type { Field } from '../types';\nimport isCheckBox from '../utils/isCheckBoxInput';\nimport isFileInput from '../utils/isFileInput';\nimport isMultipleSelect from '../utils/isMultipleSelect';\nimport isRadioInput from '../utils/isRadioInput';\nimport isUndefined from '../utils/isUndefined';\n\nimport getCheckboxValue from './getCheckboxValue';\nimport getFieldValueAs from './getFieldValueAs';\nimport getRadioValue from './getRadioValue';\n\nexport default function getFieldValue(_f: Field['_f']) {\n  const ref = _f.ref;\n\n  if (isFileInput(ref)) {\n    return ref.files;\n  }\n\n  if (isRadioInput(ref)) {\n    return getRadioValue(_f.refs).value;\n  }\n\n  if (isMultipleSelect(ref)) {\n    return [...ref.selectedOptions].map(({ value }) => value);\n  }\n\n  if (isCheckBox(ref)) {\n    return getCheckboxValue(_f.refs).value;\n  }\n\n  return getFieldValueAs(isUndefined(ref.value) ? _f.ref.value : ref.value, _f);\n}\n", "import type {\n  CriteriaMode,\n  Field,\n  FieldName,\n  FieldRefs,\n  FieldValues,\n  InternalFieldName,\n} from '../types';\nimport { get } from '../utils';\nimport set from '../utils/set';\n\nexport default <TFieldValues extends FieldValues>(\n  fieldsNames: Set<InternalFieldName> | InternalFieldName[],\n  _fields: FieldRefs,\n  criteriaMode?: CriteriaMode,\n  shouldUseNativeValidation?: boolean | undefined,\n) => {\n  const fields: Record<InternalFieldName, Field['_f']> = {};\n\n  for (const name of fieldsNames) {\n    const field: Field = get(_fields, name);\n\n    field && set(fields, name, field._f);\n  }\n\n  return {\n    criteriaMode,\n    names: [...fieldsNames] as FieldName<TFieldValues>[],\n    fields,\n    shouldUseNativeValidation,\n  };\n};\n", "export default (value: unknown): value is RegExp => value instanceof RegExp;\n", "import type {\n  ValidationRule,\n  ValidationValue,\n  ValidationValueMessage,\n} from '../types';\nimport isObject from '../utils/isObject';\nimport isRegex from '../utils/isRegex';\nimport isUndefined from '../utils/isUndefined';\n\nexport default <T extends ValidationValue>(\n  rule?: ValidationRule<T> | ValidationValueMessage<T>,\n) =>\n  isUndefined(rule)\n    ? rule\n    : isRegex(rule)\n      ? rule.source\n      : isObject(rule)\n        ? isRegex(rule.value)\n          ? rule.value.source\n          : rule.value\n        : rule;\n", "import { VALIDATION_MODE } from '../constants';\nimport type { Mode, ValidationModeFlags } from '../types';\n\nexport default (mode?: Mode): ValidationModeFlags => ({\n  isOnSubmit: !mode || mode === VALIDATION_MODE.onSubmit,\n  isOnBlur: mode === VALIDATION_MODE.onBlur,\n  isOnChange: mode === VALIDATION_MODE.onChange,\n  isOnAll: mode === VALIDATION_MODE.all,\n  isOnTouch: mode === VALIDATION_MODE.onTouched,\n});\n", "import type { Field, Validate } from '../types';\nimport isFunction from '../utils/isFunction';\nimport isObject from '../utils/isObject';\n\nconst ASYNC_FUNCTION = 'AsyncFunction';\n\nexport default (fieldReference: Field['_f']) =>\n  !!fieldReference &&\n  !!fieldReference.validate &&\n  !!(\n    (isFunction(fieldReference.validate) &&\n      fieldReference.validate.constructor.name === ASYNC_FUNCTION) ||\n    (isObject(fieldReference.validate) &&\n      Object.values(fieldReference.validate).find(\n        (validateFunction: Validate<unknown, unknown>) =>\n          validateFunction.constructor.name === ASYNC_FUNCTION,\n      ))\n  );\n", "import type { InternalFieldName, Names } from '../types';\n\nexport default (\n  name: InternalFieldName,\n  _names: Names,\n  isBlurEvent?: boolean,\n) =>\n  !isBlurEvent &&\n  (_names.watchAll ||\n    _names.watch.has(name) ||\n    [..._names.watch].some(\n      (watchName) =>\n        name.startsWith(watchName) &&\n        /^\\.\\w+/.test(name.slice(watchName.length)),\n    ));\n", "import type { FieldRefs, InternalFieldName, Ref } from '../types';\nimport { get } from '../utils';\nimport isObject from '../utils/isObject';\n\nconst iterateFieldsByAction = (\n  fields: FieldRefs,\n  action: (ref: Ref, name: string) => 1 | undefined | void,\n  fieldsNames?: Set<InternalFieldName> | InternalFieldName[] | 0,\n  abortEarly?: boolean,\n) => {\n  for (const key of fieldsNames || Object.keys(fields)) {\n    const field = get(fields, key);\n\n    if (field) {\n      const { _f, ...currentField } = field;\n\n      if (_f) {\n        if (_f.refs && _f.refs[0] && action(_f.refs[0], key) && !abortEarly) {\n          return true;\n        } else if (_f.ref && action(_f.ref, _f.name) && !abortEarly) {\n          return true;\n        } else {\n          if (iterateFieldsByAction(currentField, action)) {\n            break;\n          }\n        }\n      } else if (isObject(currentField)) {\n        if (iterateFieldsByAction(currentField as FieldRefs, action)) {\n          break;\n        }\n      }\n    }\n  }\n  return;\n};\nexport default iterateFieldsByAction;\n", "import type { FieldError, FieldErrors, FieldValues } from '../types';\nimport get from '../utils/get';\nimport isKey from '../utils/isKey';\n\nexport default function schemaErrorLookup<T extends FieldValues = FieldValues>(\n  errors: FieldErrors<T>,\n  _fields: FieldValues,\n  name: string,\n): {\n  error?: FieldError;\n  name: string;\n} {\n  const error = get(errors, name);\n\n  if (error || isKey(name)) {\n    return {\n      error,\n      name,\n    };\n  }\n\n  const names = name.split('.');\n\n  while (names.length) {\n    const fieldName = names.join('.');\n    const field = get(_fields, fieldName);\n    const foundError = get(errors, fieldName);\n\n    if (field && !Array.isArray(field) && name !== fieldName) {\n      return { name };\n    }\n\n    if (foundError && foundError.type) {\n      return {\n        name: fieldName,\n        error: foundError,\n      };\n    }\n\n    if (foundError && foundError.root && foundError.root.type) {\n      return {\n        name: `${fieldName}.root`,\n        error: foundError.root,\n      };\n    }\n\n    names.pop();\n  }\n\n  return {\n    name,\n  };\n}\n", "import { VALIDATION_MODE } from '../constants';\nimport type {\n  FieldValues,\n  FormState,\n  InternalFieldName,\n  ReadFormState,\n} from '../types';\nimport isEmptyObject from '../utils/isEmptyObject';\n\nexport default <T extends FieldValues, K extends ReadFormState>(\n  formStateData: Partial<FormState<T>> & {\n    name?: InternalFieldName;\n    values?: T;\n  },\n  _proxyFormState: K,\n  updateFormState: (formState: Partial<FormState<T>>) => void,\n  isRoot?: boolean,\n) => {\n  updateFormState(formStateData);\n  const { name, ...formState } = formStateData;\n\n  return (\n    isEmptyObject(formState) ||\n    Object.keys(formState).length >= Object.keys(_proxyFormState).length ||\n    Object.keys(formState).find(\n      (key) =>\n        _proxyFormState[key as keyof ReadFormState] ===\n        (!isRoot || VALIDATION_MODE.all),\n    )\n  );\n};\n", "import type {\n  FieldError,\n  FieldErrors,\n  FieldValues,\n  InternalFieldName,\n} from '../types';\nimport convertToArrayPayload from '../utils/convertToArrayPayload';\nimport get from '../utils/get';\nimport set from '../utils/set';\n\nexport default <T extends FieldValues = FieldValues>(\n  errors: FieldErrors<T>,\n  error: Partial<Record<string, FieldError>>,\n  name: InternalFieldName,\n): FieldErrors<T> => {\n  const fieldArrayErrors = convertToArrayPayload(get(errors, name));\n  set(fieldArrayErrors, 'root', error[name]);\n  set(errors, name, fieldArrayErrors);\n  return errors;\n};\n", "import type { Message } from '../types';\nimport isString from '../utils/isString';\n\nexport default (value: unknown): value is Message => isString(value);\n", "import type { Field<PERSON>rror, Ref, ValidateResult } from '../types';\nimport isBoolean from '../utils/isBoolean';\nimport isMessage from '../utils/isMessage';\n\nexport default function getValidateError(\n  result: ValidateResult,\n  ref: Ref,\n  type = 'validate',\n): FieldError | void {\n  if (\n    isMessage(result) ||\n    (Array.isArray(result) && result.every(isMessage)) ||\n    (isBoolean(result) && !result)\n  ) {\n    return {\n      type,\n      message: isMessage(result) ? result : '',\n      ref,\n    };\n  }\n}\n", "import type { ValidationRule } from '../types';\nimport isObject from '../utils/isObject';\nimport isRegex from '../utils/isRegex';\n\nexport default (validationData?: ValidationRule) =>\n  isObject(validationData) && !isRegex(validationData)\n    ? validationData\n    : {\n        value: validationData,\n        message: '',\n      };\n", "import { INPUT_VALIDATION_RULES } from '../constants';\nimport type {\n  Field,\n  FieldError,\n  FieldValues,\n  InternalFieldErrors,\n  InternalNameSet,\n  MaxType,\n  Message,\n  MinType,\n  NativeFieldValue,\n} from '../types';\nimport get from '../utils/get';\nimport isBoolean from '../utils/isBoolean';\nimport isCheckBoxInput from '../utils/isCheckBoxInput';\nimport isEmptyObject from '../utils/isEmptyObject';\nimport isFileInput from '../utils/isFileInput';\nimport isFunction from '../utils/isFunction';\nimport isHTMLElement from '../utils/isHTMLElement';\nimport isMessage from '../utils/isMessage';\nimport isNullOrUndefined from '../utils/isNullOrUndefined';\nimport isObject from '../utils/isObject';\nimport isRadioInput from '../utils/isRadioInput';\nimport isRegex from '../utils/isRegex';\nimport isString from '../utils/isString';\nimport isUndefined from '../utils/isUndefined';\n\nimport appendErrors from './appendErrors';\nimport getCheckboxValue from './getCheckboxValue';\nimport getRadioValue from './getRadioValue';\nimport getValidateError from './getValidateError';\nimport getValueAndMessage from './getValueAndMessage';\n\nexport default async <T extends FieldValues>(\n  field: Field,\n  disabledFieldNames: InternalNameSet,\n  formValues: T,\n  validateAllFieldCriteria: boolean,\n  shouldUseNativeValidation?: boolean,\n  isFieldArray?: boolean,\n): Promise<InternalFieldErrors> => {\n  const {\n    ref,\n    refs,\n    required,\n    maxLength,\n    minLength,\n    min,\n    max,\n    pattern,\n    validate,\n    name,\n    valueAsNumber,\n    mount,\n  } = field._f;\n  const inputValue: NativeFieldValue = get(formValues, name);\n  if (!mount || disabledFieldNames.has(name)) {\n    return {};\n  }\n  const inputRef: HTMLInputElement = refs ? refs[0] : (ref as HTMLInputElement);\n  const setCustomValidity = (message?: string | boolean) => {\n    if (shouldUseNativeValidation && inputRef.reportValidity) {\n      inputRef.setCustomValidity(isBoolean(message) ? '' : message || '');\n      inputRef.reportValidity();\n    }\n  };\n  const error: InternalFieldErrors = {};\n  const isRadio = isRadioInput(ref);\n  const isCheckBox = isCheckBoxInput(ref);\n  const isRadioOrCheckbox = isRadio || isCheckBox;\n  const isEmpty =\n    ((valueAsNumber || isFileInput(ref)) &&\n      isUndefined(ref.value) &&\n      isUndefined(inputValue)) ||\n    (isHTMLElement(ref) && ref.value === '') ||\n    inputValue === '' ||\n    (Array.isArray(inputValue) && !inputValue.length);\n  const appendErrorsCurry = appendErrors.bind(\n    null,\n    name,\n    validateAllFieldCriteria,\n    error,\n  );\n  const getMinMaxMessage = (\n    exceedMax: boolean,\n    maxLengthMessage: Message,\n    minLengthMessage: Message,\n    maxType: MaxType = INPUT_VALIDATION_RULES.maxLength,\n    minType: MinType = INPUT_VALIDATION_RULES.minLength,\n  ) => {\n    const message = exceedMax ? maxLengthMessage : minLengthMessage;\n    error[name] = {\n      type: exceedMax ? maxType : minType,\n      message,\n      ref,\n      ...appendErrorsCurry(exceedMax ? maxType : minType, message),\n    };\n  };\n\n  if (\n    isFieldArray\n      ? !Array.isArray(inputValue) || !inputValue.length\n      : required &&\n        ((!isRadioOrCheckbox && (isEmpty || isNullOrUndefined(inputValue))) ||\n          (isBoolean(inputValue) && !inputValue) ||\n          (isCheckBox && !getCheckboxValue(refs).isValid) ||\n          (isRadio && !getRadioValue(refs).isValid))\n  ) {\n    const { value, message } = isMessage(required)\n      ? { value: !!required, message: required }\n      : getValueAndMessage(required);\n\n    if (value) {\n      error[name] = {\n        type: INPUT_VALIDATION_RULES.required,\n        message,\n        ref: inputRef,\n        ...appendErrorsCurry(INPUT_VALIDATION_RULES.required, message),\n      };\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(message);\n        return error;\n      }\n    }\n  }\n\n  if (!isEmpty && (!isNullOrUndefined(min) || !isNullOrUndefined(max))) {\n    let exceedMax;\n    let exceedMin;\n    const maxOutput = getValueAndMessage(max);\n    const minOutput = getValueAndMessage(min);\n\n    if (!isNullOrUndefined(inputValue) && !isNaN(inputValue as number)) {\n      const valueNumber =\n        (ref as HTMLInputElement).valueAsNumber ||\n        (inputValue ? +inputValue : inputValue);\n      if (!isNullOrUndefined(maxOutput.value)) {\n        exceedMax = valueNumber > maxOutput.value;\n      }\n      if (!isNullOrUndefined(minOutput.value)) {\n        exceedMin = valueNumber < minOutput.value;\n      }\n    } else {\n      const valueDate =\n        (ref as HTMLInputElement).valueAsDate || new Date(inputValue as string);\n      const convertTimeToDate = (time: unknown) =>\n        new Date(new Date().toDateString() + ' ' + time);\n      const isTime = ref.type == 'time';\n      const isWeek = ref.type == 'week';\n\n      if (isString(maxOutput.value) && inputValue) {\n        exceedMax = isTime\n          ? convertTimeToDate(inputValue) > convertTimeToDate(maxOutput.value)\n          : isWeek\n            ? inputValue > maxOutput.value\n            : valueDate > new Date(maxOutput.value);\n      }\n\n      if (isString(minOutput.value) && inputValue) {\n        exceedMin = isTime\n          ? convertTimeToDate(inputValue) < convertTimeToDate(minOutput.value)\n          : isWeek\n            ? inputValue < minOutput.value\n            : valueDate < new Date(minOutput.value);\n      }\n    }\n\n    if (exceedMax || exceedMin) {\n      getMinMaxMessage(\n        !!exceedMax,\n        maxOutput.message,\n        minOutput.message,\n        INPUT_VALIDATION_RULES.max,\n        INPUT_VALIDATION_RULES.min,\n      );\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(error[name]!.message);\n        return error;\n      }\n    }\n  }\n\n  if (\n    (maxLength || minLength) &&\n    !isEmpty &&\n    (isString(inputValue) || (isFieldArray && Array.isArray(inputValue)))\n  ) {\n    const maxLengthOutput = getValueAndMessage(maxLength);\n    const minLengthOutput = getValueAndMessage(minLength);\n    const exceedMax =\n      !isNullOrUndefined(maxLengthOutput.value) &&\n      inputValue.length > +maxLengthOutput.value;\n    const exceedMin =\n      !isNullOrUndefined(minLengthOutput.value) &&\n      inputValue.length < +minLengthOutput.value;\n\n    if (exceedMax || exceedMin) {\n      getMinMaxMessage(\n        exceedMax,\n        maxLengthOutput.message,\n        minLengthOutput.message,\n      );\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(error[name]!.message);\n        return error;\n      }\n    }\n  }\n\n  if (pattern && !isEmpty && isString(inputValue)) {\n    const { value: patternValue, message } = getValueAndMessage(pattern);\n\n    if (isRegex(patternValue) && !inputValue.match(patternValue)) {\n      error[name] = {\n        type: INPUT_VALIDATION_RULES.pattern,\n        message,\n        ref,\n        ...appendErrorsCurry(INPUT_VALIDATION_RULES.pattern, message),\n      };\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(message);\n        return error;\n      }\n    }\n  }\n\n  if (validate) {\n    if (isFunction(validate)) {\n      const result = await validate(inputValue, formValues);\n      const validateError = getValidateError(result, inputRef);\n\n      if (validateError) {\n        error[name] = {\n          ...validateError,\n          ...appendErrorsCurry(\n            INPUT_VALIDATION_RULES.validate,\n            validateError.message,\n          ),\n        };\n        if (!validateAllFieldCriteria) {\n          setCustomValidity(validateError.message);\n          return error;\n        }\n      }\n    } else if (isObject(validate)) {\n      let validationResult = {} as FieldError;\n\n      for (const key in validate) {\n        if (!isEmptyObject(validationResult) && !validateAllFieldCriteria) {\n          break;\n        }\n\n        const validateError = getValidateError(\n          await validate[key](inputValue, formValues),\n          inputRef,\n          key,\n        );\n\n        if (validateError) {\n          validationResult = {\n            ...validateError,\n            ...appendErrorsCurry(key, validateError.message),\n          };\n\n          setCustomValidity(validateError.message);\n\n          if (validateAllFieldCriteria) {\n            error[name] = validationResult;\n          }\n        }\n      }\n\n      if (!isEmptyObject(validationResult)) {\n        error[name] = {\n          ref: inputRef,\n          ...validationResult,\n        };\n        if (!validateAllFieldCriteria) {\n          return error;\n        }\n      }\n    }\n  }\n\n  setCustomValidity(true);\n  return error;\n};\n", "import { EVENTS, VALIDATION_MODE } from '../constants';\nimport type {\n  BatchField<PERSON>rrayUpdate,\n  ChangeHandler,\n  Control,\n  DeepPartial,\n  DelayCallback,\n  EventType,\n  Field,\n  FieldError,\n  FieldErrors,\n  FieldNamesMarkedBoolean,\n  FieldPath,\n  FieldRefs,\n  FieldValues,\n  FormState,\n  FromSubscribe,\n  GetIsDirty,\n  InternalFieldName,\n  Names,\n  Path,\n  ReadFormState,\n  Ref,\n  SetFieldValue,\n  SetValueConfig,\n  Subjects,\n  UseFormClearErrors,\n  UseFormGetFieldState,\n  UseFormGetValues,\n  UseFormHandleSubmit,\n  UseFormProps,\n  UseFormRegister,\n  UseFormReset,\n  UseFormResetField,\n  UseFormReturn,\n  UseFormSetError,\n  UseFormSetFocus,\n  UseFormSetValue,\n  UseFormSubscribe,\n  UseFormTrigger,\n  UseFormUnregister,\n  UseFormWatch,\n  WatchInternal,\n  WatchObserver,\n} from '../types';\nimport cloneObject from '../utils/cloneObject';\nimport compact from '../utils/compact';\nimport convertToArrayPayload from '../utils/convertToArrayPayload';\nimport createSubject from '../utils/createSubject';\nimport deepEqual from '../utils/deepEqual';\nimport get from '../utils/get';\nimport isBoolean from '../utils/isBoolean';\nimport isCheckBoxInput from '../utils/isCheckBoxInput';\nimport isDateObject from '../utils/isDateObject';\nimport isEmptyObject from '../utils/isEmptyObject';\nimport isFileInput from '../utils/isFileInput';\nimport isFunction from '../utils/isFunction';\nimport isHTMLElement from '../utils/isHTMLElement';\nimport isMultipleSelect from '../utils/isMultipleSelect';\nimport isNullOrUndefined from '../utils/isNullOrUndefined';\nimport isObject from '../utils/isObject';\nimport isRadioOrCheckbox from '../utils/isRadioOrCheckbox';\nimport isString from '../utils/isString';\nimport isUndefined from '../utils/isUndefined';\nimport isWeb from '../utils/isWeb';\nimport live from '../utils/live';\nimport set from '../utils/set';\nimport unset from '../utils/unset';\n\nimport generateWatchOutput from './generateWatchOutput';\nimport getDirtyFields from './getDirtyFields';\nimport getEventValue from './getEventValue';\nimport getFieldValue from './getFieldValue';\nimport getFieldValueAs from './getFieldValueAs';\nimport getResolverOptions from './getResolverOptions';\nimport getRuleValue from './getRuleValue';\nimport getValidationModes from './getValidationModes';\nimport hasPromiseValidation from './hasPromiseValidation';\nimport hasValidation from './hasValidation';\nimport isNameInFieldArray from './isNameInFieldArray';\nimport isWatched from './isWatched';\nimport iterateFieldsByAction from './iterateFieldsByAction';\nimport schemaErrorLookup from './schemaErrorLookup';\nimport shouldRenderFormState from './shouldRenderFormState';\nimport shouldSubscribeByName from './shouldSubscribeByName';\nimport skipValidation from './skipValidation';\nimport unsetEmptyArray from './unsetEmptyArray';\nimport updateFieldArrayRootError from './updateFieldArrayRootError';\nimport validateField from './validateField';\n\nconst defaultOptions = {\n  mode: VALIDATION_MODE.onSubmit,\n  reValidateMode: VALIDATION_MODE.onChange,\n  shouldFocusError: true,\n} as const;\n\nexport function createFormControl<\n  TFieldValues extends FieldValues = FieldValues,\n  TContext = any,\n  TTransformedValues = TFieldValues,\n>(\n  props: UseFormProps<TFieldValues, TContext, TTransformedValues> = {},\n): Omit<\n  UseFormReturn<TFieldValues, TContext, TTransformedValues>,\n  'formState'\n> & {\n  formControl: Omit<\n    UseFormReturn<TFieldValues, TContext, TTransformedValues>,\n    'formState'\n  >;\n} {\n  let _options = {\n    ...defaultOptions,\n    ...props,\n  };\n  let _formState: FormState<TFieldValues> = {\n    submitCount: 0,\n    isDirty: false,\n    isReady: false,\n    isLoading: isFunction(_options.defaultValues),\n    isValidating: false,\n    isSubmitted: false,\n    isSubmitting: false,\n    isSubmitSuccessful: false,\n    isValid: false,\n    touchedFields: {},\n    dirtyFields: {},\n    validatingFields: {},\n    errors: _options.errors || {},\n    disabled: _options.disabled || false,\n  };\n  const _fields: FieldRefs = {};\n  let _defaultValues =\n    isObject(_options.defaultValues) || isObject(_options.values)\n      ? cloneObject(_options.defaultValues || _options.values) || {}\n      : {};\n  let _formValues = _options.shouldUnregister\n    ? ({} as TFieldValues)\n    : (cloneObject(_defaultValues) as TFieldValues);\n  let _state = {\n    action: false,\n    mount: false,\n    watch: false,\n  };\n  let _names: Names = {\n    mount: new Set(),\n    disabled: new Set(),\n    unMount: new Set(),\n    array: new Set(),\n    watch: new Set(),\n  };\n  let delayErrorCallback: DelayCallback | null;\n  let timer = 0;\n  const _proxyFormState: ReadFormState = {\n    isDirty: false,\n    dirtyFields: false,\n    validatingFields: false,\n    touchedFields: false,\n    isValidating: false,\n    isValid: false,\n    errors: false,\n  };\n  let _proxySubscribeFormState = {\n    ..._proxyFormState,\n  };\n  const _subjects: Subjects<TFieldValues> = {\n    array: createSubject(),\n    state: createSubject(),\n  };\n\n  const shouldDisplayAllAssociatedErrors =\n    _options.criteriaMode === VALIDATION_MODE.all;\n\n  const debounce =\n    <T extends Function>(callback: T) =>\n    (wait: number) => {\n      clearTimeout(timer);\n      timer = setTimeout(callback, wait);\n    };\n\n  const _setValid = async (shouldUpdateValid?: boolean) => {\n    if (\n      !_options.disabled &&\n      (_proxyFormState.isValid ||\n        _proxySubscribeFormState.isValid ||\n        shouldUpdateValid)\n    ) {\n      const isValid = _options.resolver\n        ? isEmptyObject((await _runSchema()).errors)\n        : await executeBuiltInValidation(_fields, true);\n\n      if (isValid !== _formState.isValid) {\n        _subjects.state.next({\n          isValid,\n        });\n      }\n    }\n  };\n\n  const _updateIsValidating = (names?: string[], isValidating?: boolean) => {\n    if (\n      !_options.disabled &&\n      (_proxyFormState.isValidating ||\n        _proxyFormState.validatingFields ||\n        _proxySubscribeFormState.isValidating ||\n        _proxySubscribeFormState.validatingFields)\n    ) {\n      (names || Array.from(_names.mount)).forEach((name) => {\n        if (name) {\n          isValidating\n            ? set(_formState.validatingFields, name, isValidating)\n            : unset(_formState.validatingFields, name);\n        }\n      });\n\n      _subjects.state.next({\n        validatingFields: _formState.validatingFields,\n        isValidating: !isEmptyObject(_formState.validatingFields),\n      });\n    }\n  };\n\n  const _setFieldArray: BatchFieldArrayUpdate = (\n    name,\n    values = [],\n    method,\n    args,\n    shouldSetValues = true,\n    shouldUpdateFieldsAndState = true,\n  ) => {\n    if (args && method && !_options.disabled) {\n      _state.action = true;\n      if (shouldUpdateFieldsAndState && Array.isArray(get(_fields, name))) {\n        const fieldValues = method(get(_fields, name), args.argA, args.argB);\n        shouldSetValues && set(_fields, name, fieldValues);\n      }\n\n      if (\n        shouldUpdateFieldsAndState &&\n        Array.isArray(get(_formState.errors, name))\n      ) {\n        const errors = method(\n          get(_formState.errors, name),\n          args.argA,\n          args.argB,\n        );\n        shouldSetValues && set(_formState.errors, name, errors);\n        unsetEmptyArray(_formState.errors, name);\n      }\n\n      if (\n        (_proxyFormState.touchedFields ||\n          _proxySubscribeFormState.touchedFields) &&\n        shouldUpdateFieldsAndState &&\n        Array.isArray(get(_formState.touchedFields, name))\n      ) {\n        const touchedFields = method(\n          get(_formState.touchedFields, name),\n          args.argA,\n          args.argB,\n        );\n        shouldSetValues && set(_formState.touchedFields, name, touchedFields);\n      }\n\n      if (_proxyFormState.dirtyFields || _proxySubscribeFormState.dirtyFields) {\n        _formState.dirtyFields = getDirtyFields(_defaultValues, _formValues);\n      }\n\n      _subjects.state.next({\n        name,\n        isDirty: _getDirty(name, values),\n        dirtyFields: _formState.dirtyFields,\n        errors: _formState.errors,\n        isValid: _formState.isValid,\n      });\n    } else {\n      set(_formValues, name, values);\n    }\n  };\n\n  const updateErrors = (name: InternalFieldName, error: FieldError) => {\n    set(_formState.errors, name, error);\n    _subjects.state.next({\n      errors: _formState.errors,\n    });\n  };\n\n  const _setErrors = (errors: FieldErrors<TFieldValues>) => {\n    _formState.errors = errors;\n    _subjects.state.next({\n      errors: _formState.errors,\n      isValid: false,\n    });\n  };\n\n  const updateValidAndValue = (\n    name: InternalFieldName,\n    shouldSkipSetValueAs: boolean,\n    value?: unknown,\n    ref?: Ref,\n  ) => {\n    const field: Field = get(_fields, name);\n\n    if (field) {\n      const defaultValue = get(\n        _formValues,\n        name,\n        isUndefined(value) ? get(_defaultValues, name) : value,\n      );\n\n      isUndefined(defaultValue) ||\n      (ref && (ref as HTMLInputElement).defaultChecked) ||\n      shouldSkipSetValueAs\n        ? set(\n            _formValues,\n            name,\n            shouldSkipSetValueAs ? defaultValue : getFieldValue(field._f),\n          )\n        : setFieldValue(name, defaultValue);\n\n      _state.mount && _setValid();\n    }\n  };\n\n  const updateTouchAndDirty = (\n    name: InternalFieldName,\n    fieldValue: unknown,\n    isBlurEvent?: boolean,\n    shouldDirty?: boolean,\n    shouldRender?: boolean,\n  ): Partial<\n    Pick<FormState<TFieldValues>, 'dirtyFields' | 'isDirty' | 'touchedFields'>\n  > => {\n    let shouldUpdateField = false;\n    let isPreviousDirty = false;\n    const output: Partial<FormState<TFieldValues>> & { name: string } = {\n      name,\n    };\n\n    if (!_options.disabled) {\n      if (!isBlurEvent || shouldDirty) {\n        if (_proxyFormState.isDirty || _proxySubscribeFormState.isDirty) {\n          isPreviousDirty = _formState.isDirty;\n          _formState.isDirty = output.isDirty = _getDirty();\n          shouldUpdateField = isPreviousDirty !== output.isDirty;\n        }\n\n        const isCurrentFieldPristine = deepEqual(\n          get(_defaultValues, name),\n          fieldValue,\n        );\n\n        isPreviousDirty = !!get(_formState.dirtyFields, name);\n        isCurrentFieldPristine\n          ? unset(_formState.dirtyFields, name)\n          : set(_formState.dirtyFields, name, true);\n        output.dirtyFields = _formState.dirtyFields;\n        shouldUpdateField =\n          shouldUpdateField ||\n          ((_proxyFormState.dirtyFields ||\n            _proxySubscribeFormState.dirtyFields) &&\n            isPreviousDirty !== !isCurrentFieldPristine);\n      }\n\n      if (isBlurEvent) {\n        const isPreviousFieldTouched = get(_formState.touchedFields, name);\n\n        if (!isPreviousFieldTouched) {\n          set(_formState.touchedFields, name, isBlurEvent);\n          output.touchedFields = _formState.touchedFields;\n          shouldUpdateField =\n            shouldUpdateField ||\n            ((_proxyFormState.touchedFields ||\n              _proxySubscribeFormState.touchedFields) &&\n              isPreviousFieldTouched !== isBlurEvent);\n        }\n      }\n\n      shouldUpdateField && shouldRender && _subjects.state.next(output);\n    }\n\n    return shouldUpdateField ? output : {};\n  };\n\n  const shouldRenderByError = (\n    name: InternalFieldName,\n    isValid?: boolean,\n    error?: FieldError,\n    fieldState?: {\n      dirty?: FieldNamesMarkedBoolean<TFieldValues>;\n      isDirty?: boolean;\n      touched?: FieldNamesMarkedBoolean<TFieldValues>;\n    },\n  ) => {\n    const previousFieldError = get(_formState.errors, name);\n    const shouldUpdateValid =\n      (_proxyFormState.isValid || _proxySubscribeFormState.isValid) &&\n      isBoolean(isValid) &&\n      _formState.isValid !== isValid;\n\n    if (_options.delayError && error) {\n      delayErrorCallback = debounce(() => updateErrors(name, error));\n      delayErrorCallback(_options.delayError);\n    } else {\n      clearTimeout(timer);\n      delayErrorCallback = null;\n      error\n        ? set(_formState.errors, name, error)\n        : unset(_formState.errors, name);\n    }\n\n    if (\n      (error ? !deepEqual(previousFieldError, error) : previousFieldError) ||\n      !isEmptyObject(fieldState) ||\n      shouldUpdateValid\n    ) {\n      const updatedFormState = {\n        ...fieldState,\n        ...(shouldUpdateValid && isBoolean(isValid) ? { isValid } : {}),\n        errors: _formState.errors,\n        name,\n      };\n\n      _formState = {\n        ..._formState,\n        ...updatedFormState,\n      };\n\n      _subjects.state.next(updatedFormState);\n    }\n  };\n\n  const _runSchema = async (name?: InternalFieldName[]) => {\n    _updateIsValidating(name, true);\n    const result = await _options.resolver!(\n      _formValues as TFieldValues,\n      _options.context,\n      getResolverOptions(\n        name || _names.mount,\n        _fields,\n        _options.criteriaMode,\n        _options.shouldUseNativeValidation,\n      ),\n    );\n    _updateIsValidating(name);\n    return result;\n  };\n\n  const executeSchemaAndUpdateState = async (names?: InternalFieldName[]) => {\n    const { errors } = await _runSchema(names);\n\n    if (names) {\n      for (const name of names) {\n        const error = get(errors, name);\n        error\n          ? set(_formState.errors, name, error)\n          : unset(_formState.errors, name);\n      }\n    } else {\n      _formState.errors = errors;\n    }\n\n    return errors;\n  };\n\n  const executeBuiltInValidation = async (\n    fields: FieldRefs,\n    shouldOnlyCheckValid?: boolean,\n    context: {\n      valid: boolean;\n    } = {\n      valid: true,\n    },\n  ) => {\n    for (const name in fields) {\n      const field = fields[name];\n\n      if (field) {\n        const { _f, ...fieldValue } = field as Field;\n\n        if (_f) {\n          const isFieldArrayRoot = _names.array.has(_f.name);\n          const isPromiseFunction =\n            field._f && hasPromiseValidation((field as Field)._f);\n\n          if (isPromiseFunction && _proxyFormState.validatingFields) {\n            _updateIsValidating([name], true);\n          }\n\n          const fieldError = await validateField(\n            field as Field,\n            _names.disabled,\n            _formValues,\n            shouldDisplayAllAssociatedErrors,\n            _options.shouldUseNativeValidation && !shouldOnlyCheckValid,\n            isFieldArrayRoot,\n          );\n\n          if (isPromiseFunction && _proxyFormState.validatingFields) {\n            _updateIsValidating([name]);\n          }\n\n          if (fieldError[_f.name]) {\n            context.valid = false;\n            if (shouldOnlyCheckValid) {\n              break;\n            }\n          }\n\n          !shouldOnlyCheckValid &&\n            (get(fieldError, _f.name)\n              ? isFieldArrayRoot\n                ? updateFieldArrayRootError(\n                    _formState.errors,\n                    fieldError,\n                    _f.name,\n                  )\n                : set(_formState.errors, _f.name, fieldError[_f.name])\n              : unset(_formState.errors, _f.name));\n        }\n\n        !isEmptyObject(fieldValue) &&\n          (await executeBuiltInValidation(\n            fieldValue,\n            shouldOnlyCheckValid,\n            context,\n          ));\n      }\n    }\n\n    return context.valid;\n  };\n\n  const _removeUnmounted = () => {\n    for (const name of _names.unMount) {\n      const field: Field = get(_fields, name);\n\n      field &&\n        (field._f.refs\n          ? field._f.refs.every((ref) => !live(ref))\n          : !live(field._f.ref)) &&\n        unregister(name as FieldPath<TFieldValues>);\n    }\n\n    _names.unMount = new Set();\n  };\n\n  const _getDirty: GetIsDirty = (name, data) =>\n    !_options.disabled &&\n    (name && data && set(_formValues, name, data),\n    !deepEqual(getValues(), _defaultValues));\n\n  const _getWatch: WatchInternal<TFieldValues> = (\n    names,\n    defaultValue,\n    isGlobal,\n  ) =>\n    generateWatchOutput(\n      names,\n      _names,\n      {\n        ...(_state.mount\n          ? _formValues\n          : isUndefined(defaultValue)\n            ? _defaultValues\n            : isString(names)\n              ? { [names]: defaultValue }\n              : defaultValue),\n      },\n      isGlobal,\n      defaultValue,\n    );\n\n  const _getFieldArray = <TFieldArrayValues>(\n    name: InternalFieldName,\n  ): Partial<TFieldArrayValues>[] =>\n    compact(\n      get(\n        _state.mount ? _formValues : _defaultValues,\n        name,\n        _options.shouldUnregister ? get(_defaultValues, name, []) : [],\n      ),\n    );\n\n  const setFieldValue = (\n    name: InternalFieldName,\n    value: SetFieldValue<TFieldValues>,\n    options: SetValueConfig = {},\n  ) => {\n    const field: Field = get(_fields, name);\n    let fieldValue: unknown = value;\n\n    if (field) {\n      const fieldReference = field._f;\n\n      if (fieldReference) {\n        !fieldReference.disabled &&\n          set(_formValues, name, getFieldValueAs(value, fieldReference));\n\n        fieldValue =\n          isHTMLElement(fieldReference.ref) && isNullOrUndefined(value)\n            ? ''\n            : value;\n\n        if (isMultipleSelect(fieldReference.ref)) {\n          [...fieldReference.ref.options].forEach(\n            (optionRef) =>\n              (optionRef.selected = (\n                fieldValue as InternalFieldName[]\n              ).includes(optionRef.value)),\n          );\n        } else if (fieldReference.refs) {\n          if (isCheckBoxInput(fieldReference.ref)) {\n            fieldReference.refs.forEach((checkboxRef) => {\n              if (!checkboxRef.defaultChecked || !checkboxRef.disabled) {\n                if (Array.isArray(fieldValue)) {\n                  checkboxRef.checked = !!fieldValue.find(\n                    (data: string) => data === checkboxRef.value,\n                  );\n                } else {\n                  checkboxRef.checked =\n                    fieldValue === checkboxRef.value || !!fieldValue;\n                }\n              }\n            });\n          } else {\n            fieldReference.refs.forEach(\n              (radioRef: HTMLInputElement) =>\n                (radioRef.checked = radioRef.value === fieldValue),\n            );\n          }\n        } else if (isFileInput(fieldReference.ref)) {\n          fieldReference.ref.value = '';\n        } else {\n          fieldReference.ref.value = fieldValue;\n\n          if (!fieldReference.ref.type) {\n            _subjects.state.next({\n              name,\n              values: cloneObject(_formValues),\n            });\n          }\n        }\n      }\n    }\n\n    (options.shouldDirty || options.shouldTouch) &&\n      updateTouchAndDirty(\n        name,\n        fieldValue,\n        options.shouldTouch,\n        options.shouldDirty,\n        true,\n      );\n\n    options.shouldValidate && trigger(name as Path<TFieldValues>);\n  };\n\n  const setValues = <\n    T extends InternalFieldName,\n    K extends SetFieldValue<TFieldValues>,\n    U extends SetValueConfig,\n  >(\n    name: T,\n    value: K,\n    options: U,\n  ) => {\n    for (const fieldKey in value) {\n      if (!value.hasOwnProperty(fieldKey)) {\n        return;\n      }\n      const fieldValue = value[fieldKey];\n      const fieldName = name + '.' + fieldKey;\n      const field = get(_fields, fieldName);\n\n      (_names.array.has(name) ||\n        isObject(fieldValue) ||\n        (field && !field._f)) &&\n      !isDateObject(fieldValue)\n        ? setValues(fieldName, fieldValue, options)\n        : setFieldValue(fieldName, fieldValue, options);\n    }\n  };\n\n  const setValue: UseFormSetValue<TFieldValues> = (\n    name,\n    value,\n    options = {},\n  ) => {\n    const field = get(_fields, name);\n    const isFieldArray = _names.array.has(name);\n    const cloneValue = cloneObject(value);\n\n    set(_formValues, name, cloneValue);\n\n    if (isFieldArray) {\n      _subjects.array.next({\n        name,\n        values: cloneObject(_formValues),\n      });\n\n      if (\n        (_proxyFormState.isDirty ||\n          _proxyFormState.dirtyFields ||\n          _proxySubscribeFormState.isDirty ||\n          _proxySubscribeFormState.dirtyFields) &&\n        options.shouldDirty\n      ) {\n        _subjects.state.next({\n          name,\n          dirtyFields: getDirtyFields(_defaultValues, _formValues),\n          isDirty: _getDirty(name, cloneValue),\n        });\n      }\n    } else {\n      field && !field._f && !isNullOrUndefined(cloneValue)\n        ? setValues(name, cloneValue, options)\n        : setFieldValue(name, cloneValue, options);\n    }\n\n    isWatched(name, _names) && _subjects.state.next({ ..._formState });\n    _subjects.state.next({\n      name: _state.mount ? name : undefined,\n      values: cloneObject(_formValues),\n    });\n  };\n\n  const onChange: ChangeHandler = async (event) => {\n    _state.mount = true;\n    const target = event.target;\n    let name: string = target.name;\n    let isFieldValueUpdated = true;\n    const field: Field = get(_fields, name);\n    const _updateIsFieldValueUpdated = (fieldValue: unknown) => {\n      isFieldValueUpdated =\n        Number.isNaN(fieldValue) ||\n        (isDateObject(fieldValue) && isNaN(fieldValue.getTime())) ||\n        deepEqual(fieldValue, get(_formValues, name, fieldValue));\n    };\n    const validationModeBeforeSubmit = getValidationModes(_options.mode);\n    const validationModeAfterSubmit = getValidationModes(\n      _options.reValidateMode,\n    );\n\n    if (field) {\n      let error;\n      let isValid;\n      const fieldValue = target.type\n        ? getFieldValue(field._f)\n        : getEventValue(event);\n      const isBlurEvent =\n        event.type === EVENTS.BLUR || event.type === EVENTS.FOCUS_OUT;\n      const shouldSkipValidation =\n        (!hasValidation(field._f) &&\n          !_options.resolver &&\n          !get(_formState.errors, name) &&\n          !field._f.deps) ||\n        skipValidation(\n          isBlurEvent,\n          get(_formState.touchedFields, name),\n          _formState.isSubmitted,\n          validationModeAfterSubmit,\n          validationModeBeforeSubmit,\n        );\n      const watched = isWatched(name, _names, isBlurEvent);\n\n      set(_formValues, name, fieldValue);\n\n      if (isBlurEvent) {\n        field._f.onBlur && field._f.onBlur(event);\n        delayErrorCallback && delayErrorCallback(0);\n      } else if (field._f.onChange) {\n        field._f.onChange(event);\n      }\n\n      const fieldState = updateTouchAndDirty(name, fieldValue, isBlurEvent);\n\n      const shouldRender = !isEmptyObject(fieldState) || watched;\n\n      !isBlurEvent &&\n        _subjects.state.next({\n          name,\n          type: event.type,\n          values: cloneObject(_formValues),\n        });\n\n      if (shouldSkipValidation) {\n        if (_proxyFormState.isValid || _proxySubscribeFormState.isValid) {\n          if (_options.mode === 'onBlur') {\n            if (isBlurEvent) {\n              _setValid();\n            }\n          } else if (!isBlurEvent) {\n            _setValid();\n          }\n        }\n\n        return (\n          shouldRender &&\n          _subjects.state.next({ name, ...(watched ? {} : fieldState) })\n        );\n      }\n\n      !isBlurEvent && watched && _subjects.state.next({ ..._formState });\n\n      if (_options.resolver) {\n        const { errors } = await _runSchema([name]);\n\n        _updateIsFieldValueUpdated(fieldValue);\n\n        if (isFieldValueUpdated) {\n          const previousErrorLookupResult = schemaErrorLookup(\n            _formState.errors,\n            _fields,\n            name,\n          );\n          const errorLookupResult = schemaErrorLookup(\n            errors,\n            _fields,\n            previousErrorLookupResult.name || name,\n          );\n\n          error = errorLookupResult.error;\n          name = errorLookupResult.name;\n\n          isValid = isEmptyObject(errors);\n        }\n      } else {\n        _updateIsValidating([name], true);\n        error = (\n          await validateField(\n            field,\n            _names.disabled,\n            _formValues,\n            shouldDisplayAllAssociatedErrors,\n            _options.shouldUseNativeValidation,\n          )\n        )[name];\n        _updateIsValidating([name]);\n\n        _updateIsFieldValueUpdated(fieldValue);\n\n        if (isFieldValueUpdated) {\n          if (error) {\n            isValid = false;\n          } else if (\n            _proxyFormState.isValid ||\n            _proxySubscribeFormState.isValid\n          ) {\n            isValid = await executeBuiltInValidation(_fields, true);\n          }\n        }\n      }\n\n      if (isFieldValueUpdated) {\n        field._f.deps &&\n          trigger(\n            field._f.deps as\n              | FieldPath<TFieldValues>\n              | FieldPath<TFieldValues>[],\n          );\n        shouldRenderByError(name, isValid, error, fieldState);\n      }\n    }\n  };\n\n  const _focusInput = (ref: Ref, key: string) => {\n    if (get(_formState.errors, key) && ref.focus) {\n      ref.focus();\n      return 1;\n    }\n    return;\n  };\n\n  const trigger: UseFormTrigger<TFieldValues> = async (name, options = {}) => {\n    let isValid;\n    let validationResult;\n    const fieldNames = convertToArrayPayload(name) as InternalFieldName[];\n\n    if (_options.resolver) {\n      const errors = await executeSchemaAndUpdateState(\n        isUndefined(name) ? name : fieldNames,\n      );\n\n      isValid = isEmptyObject(errors);\n      validationResult = name\n        ? !fieldNames.some((name) => get(errors, name))\n        : isValid;\n    } else if (name) {\n      validationResult = (\n        await Promise.all(\n          fieldNames.map(async (fieldName) => {\n            const field = get(_fields, fieldName);\n            return await executeBuiltInValidation(\n              field && field._f ? { [fieldName]: field } : field,\n            );\n          }),\n        )\n      ).every(Boolean);\n      !(!validationResult && !_formState.isValid) && _setValid();\n    } else {\n      validationResult = isValid = await executeBuiltInValidation(_fields);\n    }\n\n    _subjects.state.next({\n      ...(!isString(name) ||\n      ((_proxyFormState.isValid || _proxySubscribeFormState.isValid) &&\n        isValid !== _formState.isValid)\n        ? {}\n        : { name }),\n      ...(_options.resolver || !name ? { isValid } : {}),\n      errors: _formState.errors,\n    });\n\n    options.shouldFocus &&\n      !validationResult &&\n      iterateFieldsByAction(\n        _fields,\n        _focusInput,\n        name ? fieldNames : _names.mount,\n      );\n\n    return validationResult;\n  };\n\n  const getValues: UseFormGetValues<TFieldValues> = (\n    fieldNames?:\n      | FieldPath<TFieldValues>\n      | ReadonlyArray<FieldPath<TFieldValues>>,\n  ) => {\n    const values = {\n      ...(_state.mount ? _formValues : _defaultValues),\n    };\n\n    return isUndefined(fieldNames)\n      ? values\n      : isString(fieldNames)\n        ? get(values, fieldNames)\n        : fieldNames.map((name) => get(values, name));\n  };\n\n  const getFieldState: UseFormGetFieldState<TFieldValues> = (\n    name,\n    formState,\n  ) => ({\n    invalid: !!get((formState || _formState).errors, name),\n    isDirty: !!get((formState || _formState).dirtyFields, name),\n    error: get((formState || _formState).errors, name),\n    isValidating: !!get(_formState.validatingFields, name),\n    isTouched: !!get((formState || _formState).touchedFields, name),\n  });\n\n  const clearErrors: UseFormClearErrors<TFieldValues> = (name) => {\n    name &&\n      convertToArrayPayload(name).forEach((inputName) =>\n        unset(_formState.errors, inputName),\n      );\n\n    _subjects.state.next({\n      errors: name ? _formState.errors : {},\n    });\n  };\n\n  const setError: UseFormSetError<TFieldValues> = (name, error, options) => {\n    const ref = (get(_fields, name, { _f: {} })._f || {}).ref;\n    const currentError = get(_formState.errors, name) || {};\n\n    // Don't override existing error messages elsewhere in the object tree.\n    const { ref: currentRef, message, type, ...restOfErrorTree } = currentError;\n\n    set(_formState.errors, name, {\n      ...restOfErrorTree,\n      ...error,\n      ref,\n    });\n\n    _subjects.state.next({\n      name,\n      errors: _formState.errors,\n      isValid: false,\n    });\n\n    options && options.shouldFocus && ref && ref.focus && ref.focus();\n  };\n\n  const watch: UseFormWatch<TFieldValues> = (\n    name?:\n      | FieldPath<TFieldValues>\n      | ReadonlyArray<FieldPath<TFieldValues>>\n      | WatchObserver<TFieldValues>,\n    defaultValue?: DeepPartial<TFieldValues>,\n  ) =>\n    isFunction(name)\n      ? _subjects.state.subscribe({\n          next: (payload) =>\n            name(\n              _getWatch(undefined, defaultValue),\n              payload as {\n                name?: FieldPath<TFieldValues>;\n                type?: EventType;\n                value?: unknown;\n              },\n            ),\n        })\n      : _getWatch(\n          name as InternalFieldName | InternalFieldName[],\n          defaultValue,\n          true,\n        );\n\n  const _subscribe: FromSubscribe<TFieldValues> = (props) =>\n    _subjects.state.subscribe({\n      next: (\n        formState: Partial<FormState<TFieldValues>> & {\n          name?: InternalFieldName;\n          values?: TFieldValues | undefined;\n          type?: EventType;\n        },\n      ) => {\n        if (\n          shouldSubscribeByName(props.name, formState.name, props.exact) &&\n          shouldRenderFormState(\n            formState,\n            (props.formState as ReadFormState) || _proxyFormState,\n            _setFormState,\n            props.reRenderRoot,\n          )\n        ) {\n          props.callback({\n            values: { ..._formValues } as TFieldValues,\n            ..._formState,\n            ...formState,\n          });\n        }\n      },\n    }).unsubscribe;\n\n  const subscribe: UseFormSubscribe<TFieldValues> = (props) => {\n    _state.mount = true;\n    _proxySubscribeFormState = {\n      ..._proxySubscribeFormState,\n      ...props.formState,\n    };\n    return _subscribe({\n      ...props,\n      formState: _proxySubscribeFormState,\n    });\n  };\n\n  const unregister: UseFormUnregister<TFieldValues> = (name, options = {}) => {\n    for (const fieldName of name ? convertToArrayPayload(name) : _names.mount) {\n      _names.mount.delete(fieldName);\n      _names.array.delete(fieldName);\n\n      if (!options.keepValue) {\n        unset(_fields, fieldName);\n        unset(_formValues, fieldName);\n      }\n\n      !options.keepError && unset(_formState.errors, fieldName);\n      !options.keepDirty && unset(_formState.dirtyFields, fieldName);\n      !options.keepTouched && unset(_formState.touchedFields, fieldName);\n      !options.keepIsValidating &&\n        unset(_formState.validatingFields, fieldName);\n      !_options.shouldUnregister &&\n        !options.keepDefaultValue &&\n        unset(_defaultValues, fieldName);\n    }\n\n    _subjects.state.next({\n      values: cloneObject(_formValues),\n    });\n\n    _subjects.state.next({\n      ..._formState,\n      ...(!options.keepDirty ? {} : { isDirty: _getDirty() }),\n    });\n\n    !options.keepIsValid && _setValid();\n  };\n\n  const _setDisabledField: Control<TFieldValues>['_setDisabledField'] = ({\n    disabled,\n    name,\n  }) => {\n    if (\n      (isBoolean(disabled) && _state.mount) ||\n      !!disabled ||\n      _names.disabled.has(name)\n    ) {\n      disabled ? _names.disabled.add(name) : _names.disabled.delete(name);\n    }\n  };\n\n  const register: UseFormRegister<TFieldValues> = (name, options = {}) => {\n    let field = get(_fields, name);\n    const disabledIsDefined =\n      isBoolean(options.disabled) || isBoolean(_options.disabled);\n\n    set(_fields, name, {\n      ...(field || {}),\n      _f: {\n        ...(field && field._f ? field._f : { ref: { name } }),\n        name,\n        mount: true,\n        ...options,\n      },\n    });\n    _names.mount.add(name);\n\n    if (field) {\n      _setDisabledField({\n        disabled: isBoolean(options.disabled)\n          ? options.disabled\n          : _options.disabled,\n        name,\n      });\n    } else {\n      updateValidAndValue(name, true, options.value);\n    }\n\n    return {\n      ...(disabledIsDefined\n        ? { disabled: options.disabled || _options.disabled }\n        : {}),\n      ...(_options.progressive\n        ? {\n            required: !!options.required,\n            min: getRuleValue(options.min),\n            max: getRuleValue(options.max),\n            minLength: getRuleValue<number>(options.minLength) as number,\n            maxLength: getRuleValue(options.maxLength) as number,\n            pattern: getRuleValue(options.pattern) as string,\n          }\n        : {}),\n      name,\n      onChange,\n      onBlur: onChange,\n      ref: (ref: HTMLInputElement | null): void => {\n        if (ref) {\n          register(name, options);\n          field = get(_fields, name);\n\n          const fieldRef = isUndefined(ref.value)\n            ? ref.querySelectorAll\n              ? (ref.querySelectorAll('input,select,textarea')[0] as Ref) || ref\n              : ref\n            : ref;\n          const radioOrCheckbox = isRadioOrCheckbox(fieldRef);\n          const refs = field._f.refs || [];\n\n          if (\n            radioOrCheckbox\n              ? refs.find((option: Ref) => option === fieldRef)\n              : fieldRef === field._f.ref\n          ) {\n            return;\n          }\n\n          set(_fields, name, {\n            _f: {\n              ...field._f,\n              ...(radioOrCheckbox\n                ? {\n                    refs: [\n                      ...refs.filter(live),\n                      fieldRef,\n                      ...(Array.isArray(get(_defaultValues, name)) ? [{}] : []),\n                    ],\n                    ref: { type: fieldRef.type, name },\n                  }\n                : { ref: fieldRef }),\n            },\n          });\n\n          updateValidAndValue(name, false, undefined, fieldRef);\n        } else {\n          field = get(_fields, name, {});\n\n          if (field._f) {\n            field._f.mount = false;\n          }\n\n          (_options.shouldUnregister || options.shouldUnregister) &&\n            !(isNameInFieldArray(_names.array, name) && _state.action) &&\n            _names.unMount.add(name);\n        }\n      },\n    };\n  };\n\n  const _focusError = () =>\n    _options.shouldFocusError &&\n    iterateFieldsByAction(_fields, _focusInput, _names.mount);\n\n  const _disableForm = (disabled?: boolean) => {\n    if (isBoolean(disabled)) {\n      _subjects.state.next({ disabled });\n      iterateFieldsByAction(\n        _fields,\n        (ref, name) => {\n          const currentField: Field = get(_fields, name);\n          if (currentField) {\n            ref.disabled = currentField._f.disabled || disabled;\n\n            if (Array.isArray(currentField._f.refs)) {\n              currentField._f.refs.forEach((inputRef) => {\n                inputRef.disabled = currentField._f.disabled || disabled;\n              });\n            }\n          }\n        },\n        0,\n        false,\n      );\n    }\n  };\n\n  const handleSubmit: UseFormHandleSubmit<TFieldValues, TTransformedValues> =\n    (onValid, onInvalid) => async (e) => {\n      let onValidError = undefined;\n      if (e) {\n        e.preventDefault && e.preventDefault();\n        (e as React.BaseSyntheticEvent).persist &&\n          (e as React.BaseSyntheticEvent).persist();\n      }\n      let fieldValues: TFieldValues | TTransformedValues | {} =\n        cloneObject(_formValues);\n\n      _subjects.state.next({\n        isSubmitting: true,\n      });\n\n      if (_options.resolver) {\n        const { errors, values } = await _runSchema();\n        _formState.errors = errors;\n        fieldValues = cloneObject(values) as TFieldValues;\n      } else {\n        await executeBuiltInValidation(_fields);\n      }\n\n      if (_names.disabled.size) {\n        for (const name of _names.disabled) {\n          unset(fieldValues, name);\n        }\n      }\n\n      unset(_formState.errors, 'root');\n\n      if (isEmptyObject(_formState.errors)) {\n        _subjects.state.next({\n          errors: {},\n        });\n        try {\n          await onValid(fieldValues as TTransformedValues, e);\n        } catch (error) {\n          onValidError = error;\n        }\n      } else {\n        if (onInvalid) {\n          await onInvalid({ ..._formState.errors }, e);\n        }\n        _focusError();\n        setTimeout(_focusError);\n      }\n\n      _subjects.state.next({\n        isSubmitted: true,\n        isSubmitting: false,\n        isSubmitSuccessful: isEmptyObject(_formState.errors) && !onValidError,\n        submitCount: _formState.submitCount + 1,\n        errors: _formState.errors,\n      });\n      if (onValidError) {\n        throw onValidError;\n      }\n    };\n\n  const resetField: UseFormResetField<TFieldValues> = (name, options = {}) => {\n    if (get(_fields, name)) {\n      if (isUndefined(options.defaultValue)) {\n        setValue(name, cloneObject(get(_defaultValues, name)));\n      } else {\n        setValue(\n          name,\n          options.defaultValue as Parameters<typeof setValue<typeof name>>[1],\n        );\n        set(_defaultValues, name, cloneObject(options.defaultValue));\n      }\n\n      if (!options.keepTouched) {\n        unset(_formState.touchedFields, name);\n      }\n\n      if (!options.keepDirty) {\n        unset(_formState.dirtyFields, name);\n        _formState.isDirty = options.defaultValue\n          ? _getDirty(name, cloneObject(get(_defaultValues, name)))\n          : _getDirty();\n      }\n\n      if (!options.keepError) {\n        unset(_formState.errors, name);\n        _proxyFormState.isValid && _setValid();\n      }\n\n      _subjects.state.next({ ..._formState });\n    }\n  };\n\n  const _reset: UseFormReset<TFieldValues> = (\n    formValues,\n    keepStateOptions = {},\n  ) => {\n    const updatedValues = formValues ? cloneObject(formValues) : _defaultValues;\n    const cloneUpdatedValues = cloneObject(updatedValues);\n    const isEmptyResetValues = isEmptyObject(formValues);\n    const values = isEmptyResetValues ? _defaultValues : cloneUpdatedValues;\n\n    if (!keepStateOptions.keepDefaultValues) {\n      _defaultValues = updatedValues;\n    }\n\n    if (!keepStateOptions.keepValues) {\n      if (keepStateOptions.keepDirtyValues) {\n        const fieldsToCheck = new Set([\n          ..._names.mount,\n          ...Object.keys(getDirtyFields(_defaultValues, _formValues)),\n        ]);\n        for (const fieldName of Array.from(fieldsToCheck)) {\n          get(_formState.dirtyFields, fieldName)\n            ? set(values, fieldName, get(_formValues, fieldName))\n            : setValue(\n                fieldName as FieldPath<TFieldValues>,\n                get(values, fieldName),\n              );\n        }\n      } else {\n        if (isWeb && isUndefined(formValues)) {\n          for (const name of _names.mount) {\n            const field = get(_fields, name);\n            if (field && field._f) {\n              const fieldReference = Array.isArray(field._f.refs)\n                ? field._f.refs[0]\n                : field._f.ref;\n\n              if (isHTMLElement(fieldReference)) {\n                const form = fieldReference.closest('form');\n                if (form) {\n                  form.reset();\n                  break;\n                }\n              }\n            }\n          }\n        }\n\n        for (const fieldName of _names.mount) {\n          const value = get(values, fieldName, get(_defaultValues, fieldName));\n\n          if (!isUndefined(value)) {\n            set(values, fieldName, value);\n            setValue(\n              fieldName as FieldPath<TFieldValues>,\n              get(values, fieldName),\n            );\n          }\n        }\n      }\n\n      _formValues = cloneObject(values) as TFieldValues;\n\n      _subjects.array.next({\n        values: { ...values },\n      });\n\n      _subjects.state.next({\n        values: { ...values } as TFieldValues,\n      });\n    }\n\n    _names = {\n      mount: keepStateOptions.keepDirtyValues ? _names.mount : new Set(),\n      unMount: new Set(),\n      array: new Set(),\n      disabled: new Set(),\n      watch: new Set(),\n      watchAll: false,\n      focus: '',\n    };\n\n    _state.mount =\n      !_proxyFormState.isValid ||\n      !!keepStateOptions.keepIsValid ||\n      !!keepStateOptions.keepDirtyValues;\n\n    _state.watch = !!_options.shouldUnregister;\n\n    _subjects.state.next({\n      submitCount: keepStateOptions.keepSubmitCount\n        ? _formState.submitCount\n        : 0,\n      isDirty: isEmptyResetValues\n        ? false\n        : keepStateOptions.keepDirty\n          ? _formState.isDirty\n          : !!(\n              keepStateOptions.keepDefaultValues &&\n              !deepEqual(formValues, _defaultValues)\n            ),\n      isSubmitted: keepStateOptions.keepIsSubmitted\n        ? _formState.isSubmitted\n        : false,\n      dirtyFields: isEmptyResetValues\n        ? {}\n        : keepStateOptions.keepDirtyValues\n          ? keepStateOptions.keepDefaultValues && _formValues\n            ? getDirtyFields(_defaultValues, _formValues)\n            : _formState.dirtyFields\n          : keepStateOptions.keepDefaultValues && formValues\n            ? getDirtyFields(_defaultValues, formValues)\n            : keepStateOptions.keepDirty\n              ? _formState.dirtyFields\n              : {},\n      touchedFields: keepStateOptions.keepTouched\n        ? _formState.touchedFields\n        : {},\n      errors: keepStateOptions.keepErrors ? _formState.errors : {},\n      isSubmitSuccessful: keepStateOptions.keepIsSubmitSuccessful\n        ? _formState.isSubmitSuccessful\n        : false,\n      isSubmitting: false,\n    });\n  };\n\n  const reset: UseFormReset<TFieldValues> = (formValues, keepStateOptions) =>\n    _reset(\n      isFunction(formValues)\n        ? (formValues as Function)(_formValues as TFieldValues)\n        : formValues,\n      keepStateOptions,\n    );\n\n  const setFocus: UseFormSetFocus<TFieldValues> = (name, options = {}) => {\n    const field = get(_fields, name);\n    const fieldReference = field && field._f;\n\n    if (fieldReference) {\n      const fieldRef = fieldReference.refs\n        ? fieldReference.refs[0]\n        : fieldReference.ref;\n\n      if (fieldRef.focus) {\n        fieldRef.focus();\n        options.shouldSelect &&\n          isFunction(fieldRef.select) &&\n          fieldRef.select();\n      }\n    }\n  };\n\n  const _setFormState = (\n    updatedFormState: Partial<FormState<TFieldValues>>,\n  ) => {\n    _formState = {\n      ..._formState,\n      ...updatedFormState,\n    };\n  };\n\n  const _resetDefaultValues = () =>\n    isFunction(_options.defaultValues) &&\n    (_options.defaultValues as Function)().then((values: TFieldValues) => {\n      reset(values, _options.resetOptions);\n      _subjects.state.next({\n        isLoading: false,\n      });\n    });\n\n  const methods = {\n    control: {\n      register,\n      unregister,\n      getFieldState,\n      handleSubmit,\n      setError,\n      _subscribe,\n      _runSchema,\n      _focusError,\n      _getWatch,\n      _getDirty,\n      _setValid,\n      _setFieldArray,\n      _setDisabledField,\n      _setErrors,\n      _getFieldArray,\n      _reset,\n      _resetDefaultValues,\n      _removeUnmounted,\n      _disableForm,\n      _subjects,\n      _proxyFormState,\n      get _fields() {\n        return _fields;\n      },\n      get _formValues() {\n        return _formValues;\n      },\n      get _state() {\n        return _state;\n      },\n      set _state(value) {\n        _state = value;\n      },\n      get _defaultValues() {\n        return _defaultValues;\n      },\n      get _names() {\n        return _names;\n      },\n      set _names(value) {\n        _names = value;\n      },\n      get _formState() {\n        return _formState;\n      },\n      get _options() {\n        return _options;\n      },\n      set _options(value) {\n        _options = {\n          ..._options,\n          ...value,\n        };\n      },\n    },\n    subscribe,\n    trigger,\n    register,\n    handleSubmit,\n    watch,\n    setValue,\n    getValues,\n    reset,\n    resetField,\n    clearErrors,\n    unregister,\n    setError,\n    setFocus,\n    getFieldState,\n  };\n\n  return {\n    ...methods,\n    formControl: methods,\n  };\n}\n", "import type { Field } from '../types';\n\nexport default (options: Field['_f']) =>\n  options.mount &&\n  (options.required ||\n    options.min ||\n    options.max ||\n    options.maxLength ||\n    options.minLength ||\n    options.pattern ||\n    options.validate);\n", "import type { ValidationModeFlags } from '../types';\n\nexport default (\n  isBlurEvent: boolean,\n  isTouched: boolean,\n  isSubmitted: boolean,\n  reValidateMode: {\n    isOnBlur: boolean;\n    isOnChange: boolean;\n  },\n  mode: Partial<ValidationModeFlags>,\n) => {\n  if (mode.isOnAll) {\n    return false;\n  } else if (!isSubmitted && mode.isOnTouch) {\n    return !(isTouched || isBlurEvent);\n  } else if (isSubmitted ? reValidateMode.isOnBlur : mode.isOnBlur) {\n    return !isBlurEvent;\n  } else if (isSubmitted ? reValidateMode.isOnChange : mode.isOnChange) {\n    return isBlurEvent;\n  }\n  return true;\n};\n", "import convertToArrayPayload from '../utils/convertToArrayPayload';\n\nexport default <T extends string | readonly string[] | undefined>(\n  name?: T,\n  signalName?: string,\n  exact?: boolean,\n) =>\n  !name ||\n  !signalName ||\n  name === signalName ||\n  convertToArrayPayload(name).some(\n    (currentName) =>\n      currentName &&\n      (exact\n        ? currentName === signalName\n        : currentName.startsWith(signalName) ||\n          signalName.startsWith(currentName)),\n  );\n", "import type { FieldElement } from '../types';\n\nimport isCheckBoxInput from './isCheckBoxInput';\nimport isRadioInput from './isRadioInput';\n\nexport default (ref: FieldElement): ref is HTMLInputElement =>\n  isRadioInput(ref) || isCheckBoxInput(ref);\n", "import compact from '../utils/compact';\nimport get from '../utils/get';\nimport unset from '../utils/unset';\n\nexport default <T>(ref: T, name: string) =>\n  !compact(get(ref, name)).length && unset(ref, name);\n", "export default () => {\n  if (typeof crypto !== 'undefined' && crypto.randomUUID) {\n    return crypto.randomUUID();\n  }\n\n  const d =\n    typeof performance === 'undefined' ? Date.now() : performance.now() * 1000;\n\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\n    const r = (Math.random() * 16 + d) % 16 | 0;\n\n    return (c == 'x' ? r : (r & 0x3) | 0x8).toString(16);\n  });\n};\n", "import type { FieldArrayMethodProps, InternalFieldName } from '../types';\nimport isUndefined from '../utils/isUndefined';\n\nexport default (\n  name: InternalFieldName,\n  index: number,\n  options: FieldArrayMethodProps = {},\n): string =>\n  options.shouldFocus || isUndefined(options.shouldFocus)\n    ? options.focusName ||\n      `${name}.${isUndefined(options.focusIndex) ? index : options.focusIndex}.`\n    : '';\n", "import convertToArrayPayload from './convertToArrayPayload';\n\nexport default <T>(data: T[], value: T | T[]): T[] => [\n  ...data,\n  ...convertToArrayPayload(value),\n];\n", "export default <T>(value: T | T[]): undefined[] | undefined =>\n  Array.isArray(value) ? value.map(() => undefined) : undefined;\n", "import convertToArrayPayload from './convertToArrayPayload';\n\nexport default function insert<T>(data: T[], index: number): (T | undefined)[];\nexport default function insert<T>(\n  data: T[],\n  index: number,\n  value: T | T[],\n): T[];\nexport default function insert<T>(\n  data: T[],\n  index: number,\n  value?: T | T[],\n): (T | undefined)[] {\n  return [\n    ...data.slice(0, index),\n    ...convertToArrayPayload(value),\n    ...data.slice(index),\n  ];\n}\n", "import isUndefined from './isUndefined';\n\nexport default <T>(\n  data: (T | undefined)[],\n  from: number,\n  to: number,\n): (T | undefined)[] => {\n  if (!Array.isArray(data)) {\n    return [];\n  }\n\n  if (isUndefined(data[to])) {\n    data[to] = undefined;\n  }\n  data.splice(to, 0, data.splice(from, 1)[0]);\n\n  return data;\n};\n", "import convertToArrayPayload from './convertToArrayPayload';\n\nexport default <T>(data: T[], value: T | T[]): T[] => [\n  ...convertToArrayPayload(value),\n  ...convertToArrayPayload(data),\n];\n", "import compact from './compact';\nimport convertToArrayPayload from './convertToArrayPayload';\nimport isUndefined from './isUndefined';\n\nfunction removeAtIndexes<T>(data: T[], indexes: number[]): T[] {\n  let i = 0;\n  const temp = [...data];\n\n  for (const index of indexes) {\n    temp.splice(index - i, 1);\n    i++;\n  }\n\n  return compact(temp).length ? temp : [];\n}\n\nexport default <T>(data: T[], index?: number | number[]): T[] =>\n  isUndefined(index)\n    ? []\n    : removeAtIndexes(\n        data,\n        (convertToArrayPayload(index) as number[]).sort((a, b) => a - b),\n      );\n", "export default <T>(data: T[], indexA: number, indexB: number): void => {\n  [data[indexA], data[indexB]] = [data[indexB], data[indexA]];\n};\n", "export default <T>(fieldValues: T[], index: number, value: T) => {\n  fieldValues[index] = value;\n  return fieldValues;\n};\n", "import React from 'react';\n\nimport generateId from './logic/generateId';\nimport getFocusFieldName from './logic/getFocusFieldName';\nimport getValidationModes from './logic/getValidationModes';\nimport isWatched from './logic/isWatched';\nimport iterateFieldsByAction from './logic/iterateFieldsByAction';\nimport updateFieldArrayRootError from './logic/updateFieldArrayRootError';\nimport validateField from './logic/validateField';\nimport appendAt from './utils/append';\nimport cloneObject from './utils/cloneObject';\nimport convertToArrayPayload from './utils/convertToArrayPayload';\nimport fillEmptyArray from './utils/fillEmptyArray';\nimport get from './utils/get';\nimport insertAt from './utils/insert';\nimport isEmptyObject from './utils/isEmptyObject';\nimport moveArrayAt from './utils/move';\nimport prependAt from './utils/prepend';\nimport removeArrayAt from './utils/remove';\nimport set from './utils/set';\nimport swapArrayAt from './utils/swap';\nimport unset from './utils/unset';\nimport updateAt from './utils/update';\nimport { VALIDATION_MODE } from './constants';\nimport type {\n  Control,\n  Field,\n  FieldArray,\n  FieldArrayMethodProps,\n  FieldArrayPath,\n  FieldArrayWithId,\n  FieldErrors,\n  FieldPath,\n  FieldValues,\n  FormState,\n  InternalFieldName,\n  RegisterOptions,\n  UseFieldArrayProps,\n  UseFieldArrayReturn,\n} from './types';\nimport { useFormContext } from './useFormContext';\nimport { useIsomorphicLayoutEffect } from './useIsomorphicLayoutEffect';\n\n/**\n * A custom hook that exposes convenient methods to perform operations with a list of dynamic inputs that need to be appended, updated, removed etc. • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn) • [Video](https://youtu.be/4MrbfGSFY2A)\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usefieldarray) • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn)\n *\n * @param props - useFieldArray props\n *\n * @returns methods - functions to manipulate with the Field Arrays (dynamic inputs) {@link UseFieldArrayReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, control, handleSubmit, reset, trigger, setError } = useForm({\n *     defaultValues: {\n *       test: []\n *     }\n *   });\n *   const { fields, append } = useFieldArray({\n *     control,\n *     name: \"test\"\n *   });\n *\n *   return (\n *     <form onSubmit={handleSubmit(data => console.log(data))}>\n *       {fields.map((item, index) => (\n *          <input key={item.id} {...register(`test.${index}.firstName`)}  />\n *       ))}\n *       <button type=\"button\" onClick={() => append({ firstName: \"bill\" })}>\n *         append\n *       </button>\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */\nexport function useFieldArray<\n  TFieldValues extends FieldValues = FieldValues,\n  TFieldArrayName extends\n    FieldArrayPath<TFieldValues> = FieldArrayPath<TFieldValues>,\n  TKeyName extends string = 'id',\n  TTransformedValues = TFieldValues,\n>(\n  props: UseFieldArrayProps<\n    TFieldValues,\n    TFieldArrayName,\n    TKeyName,\n    TTransformedValues\n  >,\n): UseFieldArrayReturn<TFieldValues, TFieldArrayName, TKeyName> {\n  const methods = useFormContext();\n  const {\n    control = methods.control,\n    name,\n    keyName = 'id',\n    shouldUnregister,\n    rules,\n  } = props;\n  const [fields, setFields] = React.useState(control._getFieldArray(name));\n  const ids = React.useRef<string[]>(\n    control._getFieldArray(name).map(generateId),\n  );\n  const _fieldIds = React.useRef(fields);\n  const _name = React.useRef(name);\n  const _actioned = React.useRef(false);\n\n  _name.current = name;\n  _fieldIds.current = fields;\n  control._names.array.add(name);\n\n  rules &&\n    (control as Control<TFieldValues, any, TTransformedValues>).register(\n      name as FieldPath<TFieldValues>,\n      rules as RegisterOptions<TFieldValues>,\n    );\n\n  useIsomorphicLayoutEffect(\n    () =>\n      control._subjects.array.subscribe({\n        next: ({\n          values,\n          name: fieldArrayName,\n        }: {\n          values?: FieldValues;\n          name?: InternalFieldName;\n        }) => {\n          if (fieldArrayName === _name.current || !fieldArrayName) {\n            const fieldValues = get(values, _name.current);\n            if (Array.isArray(fieldValues)) {\n              setFields(fieldValues);\n              ids.current = fieldValues.map(generateId);\n            }\n          }\n        },\n      }).unsubscribe,\n    [control],\n  );\n\n  const updateValues = React.useCallback(\n    <\n      T extends Partial<\n        FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>\n      >[],\n    >(\n      updatedFieldArrayValues: T,\n    ) => {\n      _actioned.current = true;\n      control._setFieldArray(name, updatedFieldArrayValues);\n    },\n    [control, name],\n  );\n\n  const append = (\n    value:\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>[],\n    options?: FieldArrayMethodProps,\n  ) => {\n    const appendValue = convertToArrayPayload(cloneObject(value));\n    const updatedFieldArrayValues = appendAt(\n      control._getFieldArray(name),\n      appendValue,\n    );\n    control._names.focus = getFocusFieldName(\n      name,\n      updatedFieldArrayValues.length - 1,\n      options,\n    );\n    ids.current = appendAt(ids.current, appendValue.map(generateId));\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(name, updatedFieldArrayValues, appendAt, {\n      argA: fillEmptyArray(value),\n    });\n  };\n\n  const prepend = (\n    value:\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>[],\n    options?: FieldArrayMethodProps,\n  ) => {\n    const prependValue = convertToArrayPayload(cloneObject(value));\n    const updatedFieldArrayValues = prependAt(\n      control._getFieldArray(name),\n      prependValue,\n    );\n    control._names.focus = getFocusFieldName(name, 0, options);\n    ids.current = prependAt(ids.current, prependValue.map(generateId));\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(name, updatedFieldArrayValues, prependAt, {\n      argA: fillEmptyArray(value),\n    });\n  };\n\n  const remove = (index?: number | number[]) => {\n    const updatedFieldArrayValues: Partial<\n      FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>\n    >[] = removeArrayAt(control._getFieldArray(name), index);\n    ids.current = removeArrayAt(ids.current, index);\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    !Array.isArray(get(control._fields, name)) &&\n      set(control._fields, name, undefined);\n    control._setFieldArray(name, updatedFieldArrayValues, removeArrayAt, {\n      argA: index,\n    });\n  };\n\n  const insert = (\n    index: number,\n    value:\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>[],\n    options?: FieldArrayMethodProps,\n  ) => {\n    const insertValue = convertToArrayPayload(cloneObject(value));\n    const updatedFieldArrayValues = insertAt(\n      control._getFieldArray(name),\n      index,\n      insertValue,\n    );\n    control._names.focus = getFocusFieldName(name, index, options);\n    ids.current = insertAt(ids.current, index, insertValue.map(generateId));\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(name, updatedFieldArrayValues, insertAt, {\n      argA: index,\n      argB: fillEmptyArray(value),\n    });\n  };\n\n  const swap = (indexA: number, indexB: number) => {\n    const updatedFieldArrayValues = control._getFieldArray(name);\n    swapArrayAt(updatedFieldArrayValues, indexA, indexB);\n    swapArrayAt(ids.current, indexA, indexB);\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(\n      name,\n      updatedFieldArrayValues,\n      swapArrayAt,\n      {\n        argA: indexA,\n        argB: indexB,\n      },\n      false,\n    );\n  };\n\n  const move = (from: number, to: number) => {\n    const updatedFieldArrayValues = control._getFieldArray(name);\n    moveArrayAt(updatedFieldArrayValues, from, to);\n    moveArrayAt(ids.current, from, to);\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(\n      name,\n      updatedFieldArrayValues,\n      moveArrayAt,\n      {\n        argA: from,\n        argB: to,\n      },\n      false,\n    );\n  };\n\n  const update = (\n    index: number,\n    value: FieldArray<TFieldValues, TFieldArrayName>,\n  ) => {\n    const updateValue = cloneObject(value);\n    const updatedFieldArrayValues = updateAt(\n      control._getFieldArray<\n        FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>\n      >(name),\n      index,\n      updateValue as FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>,\n    );\n    ids.current = [...updatedFieldArrayValues].map((item, i) =>\n      !item || i === index ? generateId() : ids.current[i],\n    );\n    updateValues(updatedFieldArrayValues);\n    setFields([...updatedFieldArrayValues]);\n    control._setFieldArray(\n      name,\n      updatedFieldArrayValues,\n      updateAt,\n      {\n        argA: index,\n        argB: updateValue,\n      },\n      true,\n      false,\n    );\n  };\n\n  const replace = (\n    value:\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>[],\n  ) => {\n    const updatedFieldArrayValues = convertToArrayPayload(cloneObject(value));\n    ids.current = updatedFieldArrayValues.map(generateId);\n    updateValues([...updatedFieldArrayValues]);\n    setFields([...updatedFieldArrayValues]);\n    control._setFieldArray(\n      name,\n      [...updatedFieldArrayValues],\n      <T>(data: T): T => data,\n      {},\n      true,\n      false,\n    );\n  };\n\n  React.useEffect(() => {\n    control._state.action = false;\n\n    isWatched(name, control._names) &&\n      control._subjects.state.next({\n        ...control._formState,\n      } as FormState<TFieldValues>);\n\n    if (\n      _actioned.current &&\n      (!getValidationModes(control._options.mode).isOnSubmit ||\n        control._formState.isSubmitted) &&\n      !getValidationModes(control._options.reValidateMode).isOnSubmit\n    ) {\n      if (control._options.resolver) {\n        control._runSchema([name]).then((result) => {\n          const error = get(result.errors, name);\n          const existingError = get(control._formState.errors, name);\n\n          if (\n            existingError\n              ? (!error && existingError.type) ||\n                (error &&\n                  (existingError.type !== error.type ||\n                    existingError.message !== error.message))\n              : error && error.type\n          ) {\n            error\n              ? set(control._formState.errors, name, error)\n              : unset(control._formState.errors, name);\n            control._subjects.state.next({\n              errors: control._formState.errors as FieldErrors<TFieldValues>,\n            });\n          }\n        });\n      } else {\n        const field: Field = get(control._fields, name);\n        if (\n          field &&\n          field._f &&\n          !(\n            getValidationModes(control._options.reValidateMode).isOnSubmit &&\n            getValidationModes(control._options.mode).isOnSubmit\n          )\n        ) {\n          validateField(\n            field,\n            control._names.disabled,\n            control._formValues,\n            control._options.criteriaMode === VALIDATION_MODE.all,\n            control._options.shouldUseNativeValidation,\n            true,\n          ).then(\n            (error) =>\n              !isEmptyObject(error) &&\n              control._subjects.state.next({\n                errors: updateFieldArrayRootError(\n                  control._formState.errors as FieldErrors<TFieldValues>,\n                  error,\n                  name,\n                ) as FieldErrors<TFieldValues>,\n              }),\n          );\n        }\n      }\n    }\n\n    control._subjects.state.next({\n      name,\n      values: cloneObject(control._formValues) as TFieldValues,\n    });\n\n    control._names.focus &&\n      iterateFieldsByAction(control._fields, (ref, key: string) => {\n        if (\n          control._names.focus &&\n          key.startsWith(control._names.focus) &&\n          ref.focus\n        ) {\n          ref.focus();\n          return 1;\n        }\n        return;\n      });\n\n    control._names.focus = '';\n\n    control._setValid();\n    _actioned.current = false;\n  }, [fields, name, control]);\n\n  React.useEffect(() => {\n    !get(control._formValues, name) && control._setFieldArray(name);\n\n    return () => {\n      const updateMounted = (name: InternalFieldName, value: boolean) => {\n        const field: Field = get(control._fields, name);\n        if (field && field._f) {\n          field._f.mount = value;\n        }\n      };\n\n      control._options.shouldUnregister || shouldUnregister\n        ? control.unregister(name as FieldPath<TFieldValues>)\n        : updateMounted(name, false);\n    };\n  }, [name, control, keyName, shouldUnregister]);\n\n  return {\n    swap: React.useCallback(swap, [updateValues, name, control]),\n    move: React.useCallback(move, [updateValues, name, control]),\n    prepend: React.useCallback(prepend, [updateValues, name, control]),\n    append: React.useCallback(append, [updateValues, name, control]),\n    remove: React.useCallback(remove, [updateValues, name, control]),\n    insert: React.useCallback(insert, [updateValues, name, control]),\n    update: React.useCallback(update, [updateValues, name, control]),\n    replace: React.useCallback(replace, [updateValues, name, control]),\n    fields: React.useMemo(\n      () =>\n        fields.map((field, index) => ({\n          ...field,\n          [keyName]: ids.current[index] || generateId(),\n        })) as FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>[],\n      [fields, keyName],\n    ),\n  };\n}\n", "import React from 'react';\n\nimport getProxyFormState from './logic/getProxyFormState';\nimport deepEqual from './utils/deepEqual';\nimport isFunction from './utils/isFunction';\nimport { createFormControl } from './logic';\nimport type {\n  FieldValues,\n  FormState,\n  UseFormProps,\n  UseFormReturn,\n} from './types';\nimport { useIsomorphicLayoutEffect } from './useIsomorphicLayoutEffect';\n\n/**\n * Custom hook to manage the entire form.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useform) • [Demo](https://codesandbox.io/s/react-hook-form-get-started-ts-5ksmm) • [Video](https://www.youtube.com/watch?v=RkXv4AXXC_4)\n *\n * @param props - form configuration and validation parameters.\n *\n * @returns methods - individual functions to manage the form state. {@link UseFormReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, watch, formState: { errors } } = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   console.log(watch(\"example\"));\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input defaultValue=\"test\" {...register(\"example\")} />\n *       <input {...register(\"exampleRequired\", { required: true })} />\n *       {errors.exampleRequired && <span>This field is required</span>}\n *       <button>Submit</button>\n *     </form>\n *   );\n * }\n * ```\n */\nexport function useForm<\n  TFieldValues extends FieldValues = FieldValues,\n  TContext = any,\n  TTransformedValues = TFieldValues,\n>(\n  props: UseFormProps<TFieldValues, TContext, TTransformedValues> = {},\n): UseFormReturn<TFieldValues, TContext, TTransformedValues> {\n  const _formControl = React.useRef<\n    UseFormReturn<TFieldValues, TContext, TTransformedValues> | undefined\n  >(undefined);\n  const _values = React.useRef<typeof props.values>(undefined);\n  const [formState, updateFormState] = React.useState<FormState<TFieldValues>>({\n    isDirty: false,\n    isValidating: false,\n    isLoading: isFunction(props.defaultValues),\n    isSubmitted: false,\n    isSubmitting: false,\n    isSubmitSuccessful: false,\n    isValid: false,\n    submitCount: 0,\n    dirtyFields: {},\n    touchedFields: {},\n    validatingFields: {},\n    errors: props.errors || {},\n    disabled: props.disabled || false,\n    isReady: false,\n    defaultValues: isFunction(props.defaultValues)\n      ? undefined\n      : props.defaultValues,\n  });\n\n  if (!_formControl.current) {\n    if (props.formControl) {\n      _formControl.current = {\n        ...props.formControl,\n        formState,\n      };\n\n      if (props.defaultValues && !isFunction(props.defaultValues)) {\n        props.formControl.reset(props.defaultValues, props.resetOptions);\n      }\n    } else {\n      const { formControl, ...rest } = createFormControl(props);\n\n      _formControl.current = {\n        ...rest,\n        formState,\n      };\n    }\n  }\n\n  const control = _formControl.current.control;\n  control._options = props;\n\n  useIsomorphicLayoutEffect(() => {\n    const sub = control._subscribe({\n      formState: control._proxyFormState,\n      callback: () => updateFormState({ ...control._formState }),\n      reRenderRoot: true,\n    });\n\n    updateFormState((data) => ({\n      ...data,\n      isReady: true,\n    }));\n\n    control._formState.isReady = true;\n\n    return sub;\n  }, [control]);\n\n  React.useEffect(\n    () => control._disableForm(props.disabled),\n    [control, props.disabled],\n  );\n\n  React.useEffect(() => {\n    if (props.mode) {\n      control._options.mode = props.mode;\n    }\n    if (props.reValidateMode) {\n      control._options.reValidateMode = props.reValidateMode;\n    }\n  }, [control, props.mode, props.reValidateMode]);\n\n  React.useEffect(() => {\n    if (props.errors) {\n      control._setErrors(props.errors);\n      control._focusError();\n    }\n  }, [control, props.errors]);\n\n  React.useEffect(() => {\n    props.shouldUnregister &&\n      control._subjects.state.next({\n        values: control._getWatch(),\n      });\n  }, [control, props.shouldUnregister]);\n\n  React.useEffect(() => {\n    if (control._proxyFormState.isDirty) {\n      const isDirty = control._getDirty();\n      if (isDirty !== formState.isDirty) {\n        control._subjects.state.next({\n          isDirty,\n        });\n      }\n    }\n  }, [control, formState.isDirty]);\n\n  React.useEffect(() => {\n    if (props.values && !deepEqual(props.values, _values.current)) {\n      control._reset(props.values, control._options.resetOptions);\n      _values.current = props.values;\n      updateFormState((state) => ({ ...state }));\n    } else {\n      control._resetDefaultValues();\n    }\n  }, [control, props.values]);\n\n  React.useEffect(() => {\n    if (!control._state.mount) {\n      control._setValid();\n      control._state.mount = true;\n    }\n\n    if (control._state.watch) {\n      control._state.watch = false;\n      control._subjects.state.next({ ...control._formState });\n    }\n\n    control._removeUnmounted();\n  });\n\n  _formControl.current.formState = getProxyFormState(formState, control);\n\n  return _formControl.current;\n}\n"], "names": ["isCheckBoxInput", "element", "type", "isDateObject", "value", "Date", "isNullOrUndefined", "isObjectType", "isObject", "Array", "isArray", "getEventValue", "event", "target", "checked", "isNameInFieldArray", "names", "name", "has", "substring", "search", "getNodeParentName", "isWeb", "window", "HTMLElement", "document", "cloneObject", "data", "copy", "isFileListInstance", "FileList", "Set", "Blob", "tempObject", "prototypeCopy", "constructor", "prototype", "hasOwnProperty", "isPlainObject", "key", "is<PERSON>ey", "test", "isUndefined", "val", "undefined", "compact", "filter", "Boolean", "stringToPath", "input", "replace", "split", "get", "object", "path", "defaultValue", "result", "reduce", "isBoolean", "set", "index", "temp<PERSON>ath", "length", "lastIndex", "newValue", "objValue", "isNaN", "EVENTS", "VALIDATION_MODE", "INPUT_VALIDATION_RULES", "HookFormContext", "React", "createContext", "displayName", "useFormContext", "useContext", "getProxyFormState", "formState", "control", "localProxyFormState", "isRoot", "defaultValues", "_defaultValues", "Object", "defineProperty", "_key", "_proxyFormState", "useIsomorphicLayoutEffect", "useLayoutEffect", "useEffect", "useFormState", "props", "methods", "disabled", "exact", "updateFormState", "useState", "_formState", "_localProxyFormState", "useRef", "isDirty", "isLoading", "dirtyFields", "touchedFields", "validatingFields", "isValidating", "<PERSON><PERSON><PERSON><PERSON>", "errors", "_subscribe", "current", "callback", "_setValid", "useMemo", "isString", "generateWatchOutput", "_names", "formValues", "isGlobal", "watch", "add", "map", "fieldName", "watchAll", "useWatch", "_defaultValue", "updateValue", "_getWatch", "values", "_formValues", "_removeUnmounted", "useController", "shouldUnregister", "isArrayField", "array", "_props", "_registerProps", "register", "rules", "fieldState", "defineProperties", "invalid", "enumerable", "isTouched", "error", "onChange", "useCallback", "onBlur", "ref", "elm", "field", "_fields", "_f", "focus", "select", "setCustomValidity", "message", "reportValidity", "_shouldUnregisterField", "_options", "updateMounted", "mount", "_state", "action", "unregister", "_setDisabledField", "flatten", "obj", "output", "keys", "nested", "nested<PERSON><PERSON>", "POST_REQUEST", "appendErrors", "validateAllFieldCriteria", "types", "convertToArrayPayload", "createSubject", "_observers", "observers", "next", "observer", "subscribe", "push", "unsubscribe", "o", "isPrimitive", "deepEqual", "object1", "object2", "_internal_visited", "WeakSet", "getTime", "keys1", "keys2", "val1", "includes", "val2", "isEmptyObject", "isFileInput", "isFunction", "isHTMLElement", "owner", "ownerDocument", "defaultView", "isMultipleSelect", "isRadioInput", "live", "isConnected", "unset", "paths", "childObject", "updatePath", "slice", "baseGet", "isEmptyArray", "objectHasFunction", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fields", "isParentNodeArray", "getDirtyFieldsFromDefaultValues", "dirtyField<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getDirty<PERSON>ields", "defaultResult", "validResult", "getCheckboxValue", "options", "option", "attributes", "getFieldValueAs", "valueAsNumber", "valueAsDate", "setValueAs", "NaN", "defaultReturn", "getRadioValue", "previous", "getFieldValue", "files", "refs", "selectedOptions", "isCheckBox", "isRegex", "RegExp", "getRuleValue", "rule", "source", "getValidationModes", "mode", "isOnSubmit", "isOnBlur", "isOnChange", "isOnAll", "isOnTouch", "ASYNC_FUNCTION", "isWatched", "isBlurEvent", "some", "watchName", "startsWith", "iterateFieldsByAction", "fieldsNames", "abort<PERSON><PERSON><PERSON>", "current<PERSON><PERSON>", "schemaErrorLookup", "join", "found<PERSON><PERSON>r", "root", "pop", "updateFieldArrayRootError", "fieldArrayErrors", "isMessage", "getValidateError", "every", "getValueAndMessage", "validationData", "validateField", "async", "disabled<PERSON>ieldN<PERSON>s", "shouldUseNativeValidation", "isFieldArray", "required", "max<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "min", "max", "pattern", "validate", "inputValue", "inputRef", "isRadio", "isRadioOrCheckbox", "isEmpty", "appendErrors<PERSON><PERSON><PERSON>", "bind", "getMinMaxMessage", "exceedMax", "maxLengthMessage", "minLengthMessage", "maxType", "minType", "exceedMin", "maxOutput", "minOutput", "valueDate", "convertTimeToDate", "time", "toDateString", "isTime", "isWeek", "valueNumber", "maxLengthOutput", "minLengthOutput", "patternValue", "match", "validateError", "validationResult", "defaultOptions", "reValidateMode", "shouldFocusError", "createFormControl", "submitCount", "isReady", "isSubmitted", "isSubmitting", "isSubmitSuccessful", "delayError<PERSON><PERSON><PERSON>", "unMount", "timer", "_proxySubscribeFormState", "_subjects", "state", "shouldDisplayAllAssociatedErrors", "criteriaMode", "shouldUpdateValid", "resolver", "_runSchema", "executeBuiltInValidation", "_updateIsValidating", "from", "for<PERSON>ach", "updateValidAndValue", "shouldSkipSetValueAs", "defaultChecked", "setFieldValue", "updateTouchAndDirty", "fieldValue", "should<PERSON>irty", "shouldRender", "shouldUpdateField", "is<PERSON>revious<PERSON><PERSON>y", "_getDirty", "isCurrentFieldPristine", "isPreviousFieldTouched", "shouldRenderByError", "previousFieldError", "delayError", "updateErrors", "wait", "clearTimeout", "setTimeout", "updatedFormState", "context", "getResolverOptions", "should<PERSON>nly<PERSON><PERSON><PERSON><PERSON>d", "valid", "isFieldArrayRoot", "isPromiseFunction", "fieldReference", "find", "validateFunction", "fieldError", "getV<PERSON>ues", "optionRef", "selected", "checkboxRef", "radioRef", "shouldTouch", "shouldValidate", "trigger", "set<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "setValue", "cloneValue", "isFieldValueUpdated", "_updateIsFieldValueUpdated", "Number", "validationModeBeforeSubmit", "validationModeAfterSubmit", "shouldSkipValidation", "deps", "skipValidation", "watched", "previousErrorLookupResult", "errorLookupResult", "_focusInput", "fieldNames", "executeSchemaAndUpdateState", "Promise", "all", "shouldFocus", "getFieldState", "setError", "currentError", "currentRef", "restOfErrorTree", "signalName", "currentName", "formStateData", "shouldRenderFormState", "_setFormState", "reRenderRoot", "delete", "keepValue", "keepError", "keep<PERSON>irty", "keepTouched", "keepIsValidating", "keepDefaultValue", "keepIsValid", "disabledIsDefined", "progressive", "fieldRef", "querySelectorAll", "radioOrCheckbox", "_focusError", "handleSubmit", "onValid", "onInvalid", "e", "onValidError", "preventDefault", "persist", "field<PERSON><PERSON><PERSON>", "size", "_reset", "keepStateOptions", "updatedValues", "cloneUpdatedValues", "isEmptyResetValues", "keepDefaultValues", "keepV<PERSON>ues", "keepDirtyV<PERSON>ues", "fieldsToCheck", "form", "closest", "reset", "keepSubmitCount", "keepIsSubmitted", "keepErrors", "keepIsSubmitSuccessful", "_setFieldArray", "method", "args", "shouldSetValues", "shouldUpdateFieldsAndState", "argA", "argB", "unsetEmptyArray", "_setErrors", "_getFieldArray", "_resetDefaultValues", "then", "resetOptions", "_disableForm", "payload", "reset<PERSON>ield", "clearErrors", "inputName", "setFocus", "shouldSelect", "formControl", "generateId", "crypto", "randomUUID", "d", "performance", "now", "c", "r", "Math", "random", "toString", "getFocusFieldName", "focusName", "focusIndex", "appendAt", "fillEmptyArray", "insert", "moveArrayAt", "to", "splice", "prependAt", "removeArrayAt", "indexes", "i", "temp", "removeAtIndexes", "sort", "a", "b", "swapArrayAt", "indexA", "indexB", "updateAt", "render", "mounted", "setMounted", "onSubmit", "children", "headers", "encType", "onError", "onSuccess", "validateStatus", "rest", "submit", "<PERSON><PERSON><PERSON><PERSON>", "formData", "FormData", "formDataJson", "JSON", "stringify", "_a", "flattenForm<PERSON><PERSON>ues", "append", "shouldStringifySubmissionData", "response", "fetch", "String", "body", "status", "createElement", "Fragment", "noValidate", "Provider", "keyName", "setFields", "ids", "_fieldIds", "_name", "_actioned", "fieldArrayName", "updateValues", "updatedFieldArrayValues", "existingError", "swap", "move", "prepend", "prependValue", "appendValue", "remove", "insertValue", "insertAt", "update", "item", "_formControl", "_values", "sub"], "mappings": "siBAEAA,EAAgBC,GACG,aAAjBA,EAAQC,KCHVC,EAAgBC,GAAkCA,aAAiBC,KCAnEC,EAAgBF,GAAuD,MAATA,ECGvD,MAAMG,EAAgBH,GACV,iBAAVA,EAET,IAAAI,EAAkCJ,IAC/BE,EAAkBF,KAClBK,MAAMC,QAAQN,IACfG,EAAaH,KACZD,EAAaC,GCLDO,EAACC,GACdJ,EAASI,IAAWA,EAAgBC,OAChCb,EAAiBY,EAAgBC,QAC9BD,EAAgBC,OAAOC,QACvBF,EAAgBC,OAAOT,MAC1BQ,ECNNG,EAAe,CAACC,EAA+BC,IAC7CD,EAAME,ICLO,CAACD,GACdA,EAAKE,UAAU,EAAGF,EAAKG,OAAO,iBAAmBH,EDIvCI,CAAkBJ,IELfK,EAAkB,oBAAXC,aACU,IAAvBA,OAAOC,aACM,oBAAbC,SCEe,SAAAC,EAAeC,GACrC,IAAIC,EACJ,MAAMlB,EAAUD,MAAMC,QAAQiB,GACxBE,EACgB,oBAAbC,UAA2BH,aAAgBG,SAEpD,GAAIH,aAAgBtB,KAClBuB,EAAO,IAAIvB,KAAKsB,QACX,GAAIA,aAAgBI,IACzBH,EAAO,IAAIG,IAAIJ,OACV,IACHL,IAAUK,aAAgBK,MAAQH,KACnCnB,IAAWF,EAASmB,GAcrB,OAAOA,EAVP,GAFAC,EAAOlB,EAAU,GAAK,CAAE,EAEnBA,GClBM,CAACuB,IACd,MAAMC,EACJD,EAAWE,aAAeF,EAAWE,YAAYC,UAEnD,OACE5B,EAAS0B,IAAkBA,EAAcG,eAAe,kBDavCC,CAAcX,GAG7B,IAAK,MAAMY,KAAOZ,EACZA,EAAKU,eAAeE,KACtBX,EAAKW,GAAOb,EAAYC,EAAKY,UAJjCX,EAAOD,EAYX,OAAOC,CACT,CElCe,IAAAY,EAACpC,GAAkB,QAAQqC,KAAKrC,GCA/CsC,EAAgBC,QAA2CC,IAARD,ECAnDE,EAAwBzC,GACtBK,MAAMC,QAAQN,GAASA,EAAM0C,OAAOC,SAAW,GCCjDC,EAAgBC,GACdJ,EAAQI,EAAMC,QAAQ,YAAa,IAAIC,MAAM,UCG/CC,EAAe,CACbC,EACAC,EACAC,KAEA,IAAKD,IAAS9C,EAAS6C,GACrB,OAAOE,EAGT,MAAMC,GAAUhB,EAAMc,GAAQ,CAACA,GAAQN,EAAaM,IAAOG,QACzD,CAACD,EAAQjB,IACPjC,EAAkBkD,GAAUA,EAASA,EAAOjB,IAC9Cc,GAGF,OAAOX,EAAYc,IAAWA,IAAWH,EACrCX,EAAYW,EAAOC,IACjBC,EACAF,EAAOC,GACTE,GCzBNE,EAAgBtD,GAAsD,kBAAVA,ECM5DuD,EAAe,CACbN,EACAC,EACAlD,KAEA,IAAIwD,GAAU,EACd,MAAMC,EAAWrB,EAAMc,GAAQ,CAACA,GAAQN,EAAaM,GAC/CQ,EAASD,EAASC,OAClBC,EAAYD,EAAS,EAE3B,OAASF,EAAQE,GAAQ,CACvB,MAAMvB,EAAMsB,EAASD,GACrB,IAAII,EAAW5D,EAEf,GAAIwD,IAAUG,EAAW,CACvB,MAAME,EAAWZ,EAAOd,GACxByB,EACExD,EAASyD,IAAaxD,MAAMC,QAAQuD,GAChCA,EACCC,OAAOL,EAASD,EAAQ,IAEvB,CAAE,EADF,GAIV,GAAY,cAARrB,GAA+B,gBAARA,GAAiC,cAARA,EAClD,OAGFc,EAAOd,GAAOyB,EACdX,EAASA,EAAOd,KCnCb,MAAM4B,EACL,OADKA,EAEA,WAFAA,EAGH,SAGGC,EACH,SADGA,EAED,WAFCA,EAGD,WAHCA,EAIA,YAJAA,EAKN,MAGMC,EACN,MADMA,EAEN,MAFMA,EAGA,YAHAA,EAIA,YAJAA,EAKF,UALEA,EAMD,WANCA,EAOD,WCjBNC,EAAkBC,EAAMC,cAAoC,MAClEF,EAAgBG,YAAc,kBAgCvB,MAAMC,EAAiB,IAK5BH,EAAMI,WAAWL,GCvCJ,IAAAM,EAAA,CAKbC,EACAC,EACAC,EACAC,GAAS,KAET,MAAMxB,EAAS,CACbyB,cAAeH,EAAQI,gBAGzB,IAAK,MAAM3C,KAAOsC,EAChBM,OAAOC,eAAe5B,EAAQjB,EAAK,CACjCa,IAAK,KACH,MAAMiC,EAAO9C,EAOb,OALIuC,EAAQQ,gBAAgBD,KAAUjB,IACpCU,EAAQQ,gBAAgBD,IAASL,GAAUZ,GAG7CW,IAAwBA,EAAoBM,IAAQ,GAC7CR,EAAUQ,MAKvB,OAAO7B,GC9BF,MAAM+B,EACO,oBAAXhE,OAAyBgD,EAAMiB,gBAAkBjB,EAAMkB,UCsC1D,SAAUC,EAIdC,GAEA,MAAMC,EAAUlB,KACVI,QAAEA,EAAUc,EAAQd,QAAOe,SAAEA,EAAQ5E,KAAEA,EAAI6E,MAAEA,GAAUH,GAAS,CAAE,GACjEd,EAAWkB,GAAmBxB,EAAMyB,SAASlB,EAAQmB,YACtDC,EAAuB3B,EAAM4B,OAAO,CACxCC,SAAS,EACTC,WAAW,EACXC,aAAa,EACbC,eAAe,EACfC,kBAAkB,EAClBC,cAAc,EACdC,SAAS,EACTC,QAAQ,IAwBV,OArBApB,GACE,IACET,EAAQ8B,WAAW,CACjB3F,OACA4D,UAAWqB,EAAqBW,QAChCf,QACAgB,SAAWjC,KACRgB,GACCE,EAAgB,IACXjB,EAAQmB,cACRpB,QAIb,CAAC5D,EAAM4E,EAAUC,IAGnBvB,EAAMkB,WAAU,KACdS,EAAqBW,QAAQH,SAAW5B,EAAQiC,WAAU,KACzD,CAACjC,IAEGP,EAAMyC,SACX,IACEpC,EACEC,EACAC,EACAoB,EAAqBW,SACrB,IAEJ,CAAChC,EAAWC,GAEhB,CC5FA,IAAAmC,EAAgB7G,GAAqD,iBAAVA,ECI5C8G,EAAA,CACblG,EACAmG,EACAC,EACAC,EACA9D,IAEI0D,EAASjG,IACXqG,GAAYF,EAAOG,MAAMC,IAAIvG,GACtBoC,EAAIgE,EAAYpG,EAAOuC,IAG5B9C,MAAMC,QAAQM,GACTA,EAAMwG,KACVC,IACCJ,GAAYF,EAAOG,MAAMC,IAAIE,GAAYrE,EAAIgE,EAAYK,OAK/DJ,IAAaF,EAAOO,UAAW,GAExBN,GCsHH,SAAUO,EACdhC,GAEA,MAAMC,EAAUlB,KACVI,QACJA,EAAUc,EAAQd,QAAO7D,KACzBA,EAAIsC,aACJA,EAAYsC,SACZA,EAAQC,MACRA,GACEH,GAAS,CAAE,EACTiC,EAAgBrD,EAAM4B,OAAO5C,IAC5BnD,EAAOyH,GAAetD,EAAMyB,SACjClB,EAAQgD,UACN7G,EACA2G,EAAcf,UA6BlB,OAzBAtB,GACE,IACET,EAAQ8B,WAAW,CACjB3F,OACA4D,UAAW,CACTkD,QAAQ,GAEVjC,QACAgB,SAAWjC,IACRgB,GACDgC,EACEX,EACEjG,EACA6D,EAAQqC,OACRtC,EAAUkD,QAAUjD,EAAQkD,aAC5B,EACAJ,EAAcf,aAIxB,CAAC5F,EAAM6D,EAASe,EAAUC,IAG5BvB,EAAMkB,WAAU,IAAMX,EAAQmD,qBAEvB7H,CACT,CC7IM,SAAU8H,EAKdvC,GAEA,MAAMC,EAAUlB,KACVzD,KAAEA,EAAI4E,SAAEA,EAAQf,QAAEA,EAAUc,EAAQd,QAAOqD,iBAAEA,GAAqBxC,EAClEyC,EAAerH,EAAmB+D,EAAQqC,OAAOkB,MAAOpH,GACxDb,EAAQuH,EAAS,CACrB7C,UACA7D,OACAsC,aAAcH,EACZ0B,EAAQkD,YACR/G,EACAmC,EAAI0B,EAAQI,eAAgBjE,EAAM0E,EAAMpC,eAE1CuC,OAAO,IAEHjB,EAAYa,EAAa,CAC7BZ,UACA7D,OACA6E,OAAO,IAGHwC,EAAS/D,EAAM4B,OAAOR,GACtB4C,EAAiBhE,EAAM4B,OAC3BrB,EAAQ0D,SAASvH,EAAM,IAClB0E,EAAM8C,MACTrI,WACIsD,EAAUiC,EAAME,UAAY,CAAEA,SAAUF,EAAME,UAAa,MAI7D6C,EAAanE,EAAMyC,SACvB,IACE7B,OAAOwD,iBACL,GACA,CACEC,QAAS,CACPC,YAAY,EACZzF,IAAK,MAAQA,EAAIyB,EAAU8B,OAAQ1F,IAErCmF,QAAS,CACPyC,YAAY,EACZzF,IAAK,MAAQA,EAAIyB,EAAUyB,YAAarF,IAE1C6H,UAAW,CACTD,YAAY,EACZzF,IAAK,MAAQA,EAAIyB,EAAU0B,cAAetF,IAE5CwF,aAAc,CACZoC,YAAY,EACZzF,IAAK,MAAQA,EAAIyB,EAAU2B,iBAAkBvF,IAE/C8H,MAAO,CACLF,YAAY,EACZzF,IAAK,IAAMA,EAAIyB,EAAU8B,OAAQ1F,OAIzC,CAAC4D,EAAW5D,IAGR+H,EAAWzE,EAAM0E,aACpBrI,GACC2H,EAAe1B,QAAQmC,SAAS,CAC9BnI,OAAQ,CACNT,MAAOO,EAAcC,GACrBK,KAAMA,GAERf,KAAMiE,KAEV,CAAClD,IAGGiI,EAAS3E,EAAM0E,aACnB,IACEV,EAAe1B,QAAQqC,OAAO,CAC5BrI,OAAQ,CACNT,MAAOgD,EAAI0B,EAAQkD,YAAa/G,GAChCA,KAAMA,GAERf,KAAMiE,KAEV,CAAClD,EAAM6D,EAAQkD,cAGXmB,EAAM5E,EAAM0E,aACfG,IACC,MAAMC,EAAQjG,EAAI0B,EAAQwE,QAASrI,GAE/BoI,GAASD,IACXC,EAAME,GAAGJ,IAAM,CACbK,MAAO,IAAMJ,EAAII,OAASJ,EAAII,QAC9BC,OAAQ,IAAML,EAAIK,QAAUL,EAAIK,SAChCC,kBAAoBC,GAClBP,EAAIM,kBAAkBC,GACxBC,eAAgB,IAAMR,EAAIQ,qBAIhC,CAAC9E,EAAQwE,QAASrI,IAGdoI,EAAQ9E,EAAMyC,SAClB,KAAO,CACL/F,OACAb,WACIsD,EAAUmC,IAAahB,EAAUgB,SACjC,CAAEA,SAAUhB,EAAUgB,UAAYA,GAClC,GACJmD,WACAE,SACAC,SAEF,CAAClI,EAAM4E,EAAUhB,EAAUgB,SAAUmD,EAAUE,EAAQC,EAAK/I,IAoD9D,OAjDAmE,EAAMkB,WAAU,KACd,MAAMoE,EACJ/E,EAAQgF,SAAS3B,kBAAoBA,EAEvCrD,EAAQ0D,SAASvH,EAAM,IAClBqH,EAAOzB,QAAQ4B,SACd/E,EAAU4E,EAAOzB,QAAQhB,UACzB,CAAEA,SAAUyC,EAAOzB,QAAQhB,UAC3B,KAGN,MAAMkE,EAAgB,CAAC9I,EAAyBb,KAC9C,MAAMiJ,EAAejG,EAAI0B,EAAQwE,QAASrI,GAEtCoI,GAASA,EAAME,KACjBF,EAAME,GAAGS,MAAQ5J,IAMrB,GAFA2J,EAAc9I,GAAM,GAEhB4I,EAAwB,CAC1B,MAAMzJ,EAAQsB,EAAY0B,EAAI0B,EAAQgF,SAAS7E,cAAehE,IAC9D0C,EAAImB,EAAQI,eAAgBjE,EAAMb,GAC9BsC,EAAYU,EAAI0B,EAAQkD,YAAa/G,KACvC0C,EAAImB,EAAQkD,YAAa/G,EAAMb,GAMnC,OAFCgI,GAAgBtD,EAAQ0D,SAASvH,GAE3B,MAEHmH,EACIyB,IAA2B/E,EAAQmF,OAAOC,OAC1CL,GAEF/E,EAAQqF,WAAWlJ,GACnB8I,EAAc9I,GAAM,MAEzB,CAACA,EAAM6D,EAASsD,EAAcD,IAEjC5D,EAAMkB,WAAU,KACdX,EAAQsF,kBAAkB,CACxBvE,WACA5E,WAED,CAAC4E,EAAU5E,EAAM6D,IAEbP,EAAMyC,SACX,KAAO,CACLqC,QACAxE,YACA6D,gBAEF,CAACW,EAAOxE,EAAW6D,GAEvB,CCpLA,MCzCa2B,EAAWC,IACtB,MAAMC,EAAsB,CAAE,EAE9B,IAAK,MAAMhI,KAAO4C,OAAOqF,KAAKF,GAC5B,GAAI/J,EAAa+J,EAAI/H,KAAsB,OAAb+H,EAAI/H,GAAe,CAC/C,MAAMkI,EAASJ,EAAQC,EAAI/H,IAE3B,IAAK,MAAMmI,KAAavF,OAAOqF,KAAKC,GAClCF,EAAO,GAAGhI,KAAOmI,KAAeD,EAAOC,QAGzCH,EAAOhI,GAAO+H,EAAI/H,GAItB,OAAOgI,GCbHI,EAAe,OCAN,IAAAC,EAAA,CACb3J,EACA4J,EACAlE,EACAzG,EACAyJ,IAEAkB,EACI,IACKlE,EAAO1F,GACV6J,MAAO,IACDnE,EAAO1F,IAAS0F,EAAO1F,GAAO6J,MAAQnE,EAAO1F,GAAO6J,MAAQ,CAAA,EAChE5K,CAACA,GAAOyJ,IAAW,IAGvB,CAAE,ECrBRoB,EAAmB3K,GAAcK,MAAMC,QAAQN,GAASA,EAAQ,CAACA,GCgBjE4K,EAAe,KACb,IAAIC,EAA4B,GAqBhC,MAAO,CACL,aAAIC,GACF,OAAOD,CACR,EACDE,KAvBY/K,IACZ,IAAK,MAAMgL,KAAYH,EACrBG,EAASD,MAAQC,EAASD,KAAK/K,IAsBjCiL,UAlBiBD,IACjBH,EAAWK,KAAKF,GACT,CACLG,YAAa,KACXN,EAAaA,EAAWnI,QAAQ0I,GAAMA,IAAMJ,OAehDG,YAVkB,KAClBN,EAAa,MC9BjBQ,EAAgBrL,GACdE,EAAkBF,KAAWG,EAAaH,GCDpB,SAAAsL,EACtBC,EACAC,EACAC,EAAoB,IAAIC,SAExB,GAAIL,EAAYE,IAAYF,EAAYG,GACtC,OAAOD,IAAYC,EAGrB,GAAIzL,EAAawL,IAAYxL,EAAayL,GACxC,OAAOD,EAAQI,YAAcH,EAAQG,UAGvC,MAAMC,EAAQ7G,OAAOqF,KAAKmB,GACpBM,EAAQ9G,OAAOqF,KAAKoB,GAE1B,GAAII,EAAMlI,SAAWmI,EAAMnI,OACzB,OAAO,EAGT,GAAI+H,EAAkB3K,IAAIyK,IAAYE,EAAkB3K,IAAI0K,GAC1D,OAAO,EAETC,EAAkBtE,IAAIoE,GACtBE,EAAkBtE,IAAIqE,GAEtB,IAAK,MAAMrJ,KAAOyJ,EAAO,CACvB,MAAME,EAAOP,EAAQpJ,GAErB,IAAK0J,EAAME,SAAS5J,GAClB,OAAO,EAGT,GAAY,QAARA,EAAe,CACjB,MAAM6J,EAAOR,EAAQrJ,GAErB,GACGpC,EAAa+L,IAAS/L,EAAaiM,IACnC5L,EAAS0L,IAAS1L,EAAS4L,IAC3B3L,MAAMC,QAAQwL,IAASzL,MAAMC,QAAQ0L,IACjCV,EAAUQ,EAAME,EAAMP,GACvBK,IAASE,EAEb,OAAO,GAKb,OAAO,CACT,CClDA,IAAAC,EAAgBjM,GACdI,EAASJ,KAAW+E,OAAOqF,KAAKpK,GAAO0D,OCHzCwI,EAAgBrM,GACG,SAAjBA,EAAQC,KCHVqM,EAAgBnM,GACG,mBAAVA,ECCMoM,GAACpM,IACd,IAAKkB,EACH,OAAO,EAGT,MAAMmL,EAAQrM,EAAUA,EAAsBsM,cAA6B,EAC3E,OACEtM,aACCqM,GAASA,EAAME,YAAcF,EAAME,YAAYnL,YAAcA,cCRnDoL,GAAC3M,GACG,oBAAjBA,EAAQC,KCDV2M,GAAgB5M,GACG,UAAjBA,EAAQC,KCCK4M,GAAC3D,GAAaqD,GAAcrD,IAAQA,EAAI4D,YCsBzC,SAAUC,GAAM3J,EAAaC,GACzC,MAAM2J,EAAQxM,MAAMC,QAAQ4C,GACxBA,EACAd,EAAMc,GACJ,CAACA,GACDN,EAAaM,GAEb4J,EAA+B,IAAjBD,EAAMnJ,OAAeT,EA3B3C,SAAiBA,EAAa8J,GAC5B,MAAMrJ,EAASqJ,EAAWC,MAAM,GAAG,GAAItJ,OACvC,IAAIF,EAAQ,EAEZ,KAAOA,EAAQE,GACbT,EAASX,EAAYW,GAAUO,IAAUP,EAAO8J,EAAWvJ,MAG7D,OAAOP,CACT,CAkBoDgK,CAAQhK,EAAQ4J,GAE5DrJ,EAAQqJ,EAAMnJ,OAAS,EACvBvB,EAAM0K,EAAMrJ,GAclB,OAZIsJ,UACKA,EAAY3K,GAIT,IAAVqB,IACEpD,EAAS0M,IAAgBb,EAAca,IACtCzM,MAAMC,QAAQwM,IA5BrB,SAAsB5C,GACpB,IAAK,MAAM/H,KAAO+H,EAChB,GAAIA,EAAIjI,eAAeE,KAASG,EAAY4H,EAAI/H,IAC9C,OAAO,EAGX,OAAO,CACT,CAqBqC+K,CAAaJ,KAE9CF,GAAM3J,EAAQ4J,EAAMG,MAAM,GAAK,IAG1B/J,CACT,CCjDe,IAAAkK,GAAI5L,IACjB,IAAK,MAAMY,KAAOZ,EAChB,GAAI4K,EAAW5K,EAAKY,IAClB,OAAO,EAGX,OAAO,GCDT,SAASiL,GAAmB7L,EAAS8L,EAA8B,IACjE,MAAMC,EAAoBjN,MAAMC,QAAQiB,GAExC,GAAInB,EAASmB,IAAS+L,EACpB,IAAK,MAAMnL,KAAOZ,EAEdlB,MAAMC,QAAQiB,EAAKY,KAClB/B,EAASmB,EAAKY,MAAUgL,GAAkB5L,EAAKY,KAEhDkL,EAAOlL,GAAO9B,MAAMC,QAAQiB,EAAKY,IAAQ,GAAK,CAAE,EAChDiL,GAAgB7L,EAAKY,GAAMkL,EAAOlL,KACxBjC,EAAkBqB,EAAKY,MACjCkL,EAAOlL,IAAO,GAKpB,OAAOkL,CACT,CAEA,SAASE,GACPhM,EACAyF,EACAwG,GAKA,MAAMF,EAAoBjN,MAAMC,QAAQiB,GAExC,GAAInB,EAASmB,IAAS+L,EACpB,IAAK,MAAMnL,KAAOZ,EAEdlB,MAAMC,QAAQiB,EAAKY,KAClB/B,EAASmB,EAAKY,MAAUgL,GAAkB5L,EAAKY,IAG9CG,EAAY0E,IACZqE,EAAYmC,EAAsBrL,IAElCqL,EAAsBrL,GAAO9B,MAAMC,QAAQiB,EAAKY,IAC5CiL,GAAgB7L,EAAKY,GAAM,IAC3B,IAAKiL,GAAgB7L,EAAKY,KAE9BoL,GACEhM,EAAKY,GACLjC,EAAkB8G,GAAc,CAAE,EAAGA,EAAW7E,GAChDqL,EAAsBrL,IAI1BqL,EAAsBrL,IAAQmJ,EAAU/J,EAAKY,GAAM6E,EAAW7E,IAKpE,OAAOqL,CACT,CAEA,IAAAC,GAAe,CAAI5I,EAAkBmC,IACnCuG,GACE1I,EACAmC,EACAoG,GAAgBpG,IC/DpB,MAAM0G,GAAqC,CACzC1N,OAAO,EACPsG,SAAS,GAGLqH,GAAc,CAAE3N,OAAO,EAAMsG,SAAS,GAE7B,IAAAsH,GAACC,IACd,GAAIxN,MAAMC,QAAQuN,GAAU,CAC1B,GAAIA,EAAQnK,OAAS,EAAG,CACtB,MAAMiE,EAASkG,EACZnL,QAAQoL,GAAWA,GAAUA,EAAOpN,UAAYoN,EAAOrI,WACvD2B,KAAK0G,GAAWA,EAAO9N,QAC1B,MAAO,CAAEA,MAAO2H,EAAQrB,UAAWqB,EAAOjE,QAG5C,OAAOmK,EAAQ,GAAGnN,UAAYmN,EAAQ,GAAGpI,SAErCoI,EAAQ,GAAGE,aAAezL,EAAYuL,EAAQ,GAAGE,WAAW/N,OAC1DsC,EAAYuL,EAAQ,GAAG7N,QAA+B,KAArB6N,EAAQ,GAAG7N,MAC1C2N,GACA,CAAE3N,MAAO6N,EAAQ,GAAG7N,MAAOsG,SAAS,GACtCqH,GACFD,GAGN,OAAOA,IC7BTM,GAAe,CACbhO,GACEiO,gBAAeC,cAAaC,gBAE9B7L,EAAYtC,GACRA,EACAiO,EACY,KAAVjO,EACEoO,IACApO,GACGA,EACDA,EACJkO,GAAerH,EAAS7G,GACtB,IAAIC,KAAKD,GACTmO,EACEA,EAAWnO,GACXA,ECfZ,MAAMqO,GAAkC,CACtC/H,SAAS,EACTtG,MAAO,MAGT,IAAAsO,GAAgBT,GACdxN,MAAMC,QAAQuN,GACVA,EAAQxK,QACN,CAACkL,EAAUT,IACTA,GAAUA,EAAOpN,UAAYoN,EAAOrI,SAChC,CACEa,SAAS,EACTtG,MAAO8N,EAAO9N,OAEhBuO,GACNF,IAEFA,GCXkB,SAAAG,GAAcrF,GACpC,MAAMJ,EAAMI,EAAGJ,IAEf,OAAImD,EAAYnD,GACPA,EAAI0F,MAGThC,GAAa1D,GACRuF,GAAcnF,EAAGuF,MAAM1O,MAG5BwM,GAAiBzD,GACZ,IAAIA,EAAI4F,iBAAiBvH,KAAI,EAAGpH,WAAYA,IAGjD4O,EAAW7F,GACN6E,GAAiBzE,EAAGuF,MAAM1O,MAG5BgO,GAAgB1L,EAAYyG,EAAI/I,OAASmJ,EAAGJ,IAAI/I,MAAQ+I,EAAI/I,MAAOmJ,EAC5E,CCpBe,ICXf0F,GAAgB7O,GAAoCA,aAAiB8O,OCSrEC,GACEC,GAEA1M,EAAY0M,GACRA,EACAH,GAAQG,GACNA,EAAKC,OACL7O,EAAS4O,GACPH,GAAQG,EAAKhP,OACXgP,EAAKhP,MAAMiP,OACXD,EAAKhP,MACPgP,ECjBKE,GAACC,IAAsC,CACpDC,YAAaD,GAAQA,IAASnL,EAC9BqL,SAAUF,IAASnL,EACnBsL,WAAYH,IAASnL,EACrBuL,QAASJ,IAASnL,EAClBwL,UAAWL,IAASnL,ICJtB,MAAMyL,GAAiB,gBAEvB,ICJeC,GAAA,CACb7O,EACAkG,EACA4I,KAECA,IACA5I,EAAOO,UACNP,EAAOG,MAAMpG,IAAID,IACjB,IAAIkG,EAAOG,OAAO0I,MACfC,GACChP,EAAKiP,WAAWD,IAChB,SAASxN,KAAKxB,EAAKmM,MAAM6C,EAAUnM,YCT3C,MAAMqM,GAAwB,CAC5B1C,EACAvD,EACAkG,EACAC,KAEA,IAAK,MAAM9N,KAAO6N,GAAejL,OAAOqF,KAAKiD,GAAS,CACpD,MAAMpE,EAAQjG,EAAIqK,EAAQlL,GAE1B,GAAI8G,EAAO,CACT,MAAME,GAAEA,KAAO+G,GAAiBjH,EAEhC,GAAIE,EAAI,CACN,GAAIA,EAAGuF,MAAQvF,EAAGuF,KAAK,IAAM5E,EAAOX,EAAGuF,KAAK,GAAIvM,KAAS8N,EACvD,OAAO,EACF,GAAI9G,EAAGJ,KAAOe,EAAOX,EAAGJ,IAAKI,EAAGtI,QAAUoP,EAC/C,OAAO,EAEP,GAAIF,GAAsBG,EAAcpG,GACtC,WAGC,GAAI1J,EAAS8P,IACdH,GAAsBG,EAA2BpG,GACnD,SCxBc,SAAAqG,GACtB5J,EACA2C,EACArI,GAKA,MAAM8H,EAAQ3F,EAAIuD,EAAQ1F,GAE1B,GAAI8H,GAASvG,EAAMvB,GACjB,MAAO,CACL8H,QACA9H,QAIJ,MAAMD,EAAQC,EAAKkC,MAAM,KAEzB,KAAOnC,EAAM8C,QAAQ,CACnB,MAAM2D,EAAYzG,EAAMwP,KAAK,KACvBnH,EAAQjG,EAAIkG,EAAS7B,GACrBgJ,EAAarN,EAAIuD,EAAQc,GAE/B,GAAI4B,IAAU5I,MAAMC,QAAQ2I,IAAUpI,IAASwG,EAC7C,MAAO,CAAExG,QAGX,GAAIwP,GAAcA,EAAWvQ,KAC3B,MAAO,CACLe,KAAMwG,EACNsB,MAAO0H,GAIX,GAAIA,GAAcA,EAAWC,MAAQD,EAAWC,KAAKxQ,KACnD,MAAO,CACLe,KAAM,GAAGwG,SACTsB,MAAO0H,EAAWC,MAItB1P,EAAM2P,MAGR,MAAO,CACL1P,OAEJ,CC3Ce,ICCf2P,GAAe,CACbjK,EACAoC,EACA9H,KAEA,MAAM4P,EAAmB9F,EAAsB3H,EAAIuD,EAAQ1F,IAG3D,OAFA0C,EAAIkN,EAAkB,OAAQ9H,EAAM9H,IACpC0C,EAAIgD,EAAQ1F,EAAM4P,GACXlK,GCfTmK,GAAgB1Q,GAAqC6G,EAAS7G,GCChD,SAAU2Q,GACtBvN,EACA2F,EACAjJ,EAAO,YAEP,GACE4Q,GAAUtN,IACT/C,MAAMC,QAAQ8C,IAAWA,EAAOwN,MAAMF,KACtCpN,EAAUF,KAAYA,EAEvB,MAAO,CACLtD,OACAyJ,QAASmH,GAAUtN,GAAUA,EAAS,GACtC2F,MAGN,CChBe,IAAA8H,GAACC,GACd1Q,EAAS0Q,KAAoBjC,GAAQiC,GACjCA,EACA,CACE9Q,MAAO8Q,EACPvH,QAAS,ICwBjBwH,GAAeC,MACb/H,EACAgI,EACAjK,EACAyD,EACAyG,EACAC,KAEA,MAAMpI,IACJA,EAAG2F,KACHA,EAAI0C,SACJA,EAAQC,UACRA,EAASC,UACTA,EAASC,IACTA,EAAGC,IACHA,EAAGC,QACHA,EAAOC,SACPA,EAAQ7Q,KACRA,EAAIoN,cACJA,EAAarE,MACbA,GACEX,EAAME,GACJwI,EAA+B3O,EAAIgE,EAAYnG,GACrD,IAAK+I,GAASqH,EAAmBnQ,IAAID,GACnC,MAAO,CAAE,EAEX,MAAM+Q,EAA6BlD,EAAOA,EAAK,GAAM3F,EAC/CO,EAAqBC,IACrB2H,GAA6BU,EAASpI,iBACxCoI,EAAStI,kBAAkBhG,EAAUiG,GAAW,GAAKA,GAAW,IAChEqI,EAASpI,mBAGPb,EAA6B,CAAE,EAC/BkJ,EAAUpF,GAAa1D,GACvB6F,EAAahP,EAAgBmJ,GAC7B+I,EAAoBD,GAAWjD,EAC/BmD,GACF9D,GAAiB/B,EAAYnD,KAC7BzG,EAAYyG,EAAI/I,QAChBsC,EAAYqP,IACbvF,GAAcrD,IAAsB,KAAdA,EAAI/I,OACZ,KAAf2R,GACCtR,MAAMC,QAAQqR,KAAgBA,EAAWjO,OACtCsO,EAAoBxH,EAAayH,KACrC,KACApR,EACA4J,EACA9B,GAEIuJ,EAAmB,CACvBC,EACAC,EACAC,EACAC,EAAmBrO,EACnBsO,EAAmBtO,KAEnB,MAAMsF,EAAU4I,EAAYC,EAAmBC,EAC/C1J,EAAM9H,GAAQ,CACZf,KAAMqS,EAAYG,EAAUC,EAC5BhJ,UACAR,SACGiJ,EAAkBG,EAAYG,EAAUC,EAAShJ,KAIxD,GACE4H,GACK9Q,MAAMC,QAAQqR,KAAgBA,EAAWjO,OAC1C0N,KACGU,IAAsBC,GAAW7R,EAAkByR,KACnDrO,EAAUqO,KAAgBA,GAC1B/C,IAAehB,GAAiBc,GAAMpI,SACtCuL,IAAYvD,GAAcI,GAAMpI,SACvC,CACA,MAAMtG,MAAEA,EAAKuJ,QAAEA,GAAYmH,GAAUU,GACjC,CAAEpR,QAASoR,EAAU7H,QAAS6H,GAC9BP,GAAmBO,GAEvB,GAAIpR,IACF2I,EAAM9H,GAAQ,CACZf,KAAMmE,EACNsF,UACAR,IAAK6I,KACFI,EAAkB/N,EAAiCsF,KAEnDkB,GAEH,OADAnB,EAAkBC,GACXZ,EAKb,KAAKoJ,GAAa7R,EAAkBqR,IAASrR,EAAkBsR,IAAO,CACpE,IAAIW,EACAK,EACJ,MAAMC,EAAY5B,GAAmBW,GAC/BkB,EAAY7B,GAAmBU,GAErC,GAAKrR,EAAkByR,IAAgB7N,MAAM6N,GAUtC,CACL,MAAMgB,EACH5J,EAAyBmF,aAAe,IAAIjO,KAAK0R,GAC9CiB,EAAqBC,GACzB,IAAI5S,MAAK,IAAIA,MAAO6S,eAAiB,IAAMD,GACvCE,EAAqB,QAAZhK,EAAIjJ,KACbkT,EAAqB,QAAZjK,EAAIjJ,KAEf+G,EAAS4L,EAAUzS,QAAU2R,IAC/BQ,EAAYY,EACRH,EAAkBjB,GAAciB,EAAkBH,EAAUzS,OAC5DgT,EACErB,EAAac,EAAUzS,MACvB2S,EAAY,IAAI1S,KAAKwS,EAAUzS,QAGnC6G,EAAS6L,EAAU1S,QAAU2R,IAC/Ba,EAAYO,EACRH,EAAkBjB,GAAciB,EAAkBF,EAAU1S,OAC5DgT,EACErB,EAAae,EAAU1S,MACvB2S,EAAY,IAAI1S,KAAKyS,EAAU1S,YA/B2B,CAClE,MAAMiT,EACHlK,EAAyBkF,gBACzB0D,GAAcA,EAAaA,GACzBzR,EAAkBuS,EAAUzS,SAC/BmS,EAAYc,EAAcR,EAAUzS,OAEjCE,EAAkBwS,EAAU1S,SAC/BwS,EAAYS,EAAcP,EAAU1S,OA2BxC,IAAImS,GAAaK,KACfN,IACIC,EACFM,EAAUlJ,QACVmJ,EAAUnJ,QACVtF,EACAA,IAEGwG,GAEH,OADAnB,EAAkBX,EAAM9H,GAAO0I,SACxBZ,EAKb,IACG0I,GAAaC,KACbS,IACAlL,EAAS8K,IAAgBR,GAAgB9Q,MAAMC,QAAQqR,IACxD,CACA,MAAMuB,EAAkBrC,GAAmBQ,GACrC8B,EAAkBtC,GAAmBS,GACrCa,GACHjS,EAAkBgT,EAAgBlT,QACnC2R,EAAWjO,QAAUwP,EAAgBlT,MACjCwS,GACHtS,EAAkBiT,EAAgBnT,QACnC2R,EAAWjO,QAAUyP,EAAgBnT,MAEvC,IAAImS,GAAaK,KACfN,EACEC,EACAe,EAAgB3J,QAChB4J,EAAgB5J,UAEbkB,GAEH,OADAnB,EAAkBX,EAAM9H,GAAO0I,SACxBZ,EAKb,GAAI8I,IAAYM,GAAWlL,EAAS8K,GAAa,CAC/C,MAAQ3R,MAAOoT,EAAY7J,QAAEA,GAAYsH,GAAmBY,GAE5D,GAAI5C,GAAQuE,KAAkBzB,EAAW0B,MAAMD,KAC7CzK,EAAM9H,GAAQ,CACZf,KAAMmE,EACNsF,UACAR,SACGiJ,EAAkB/N,EAAgCsF,KAElDkB,GAEH,OADAnB,EAAkBC,GACXZ,EAKb,GAAI+I,EACF,GAAIvF,EAAWuF,GAAW,CACxB,MACM4B,EAAgB3C,SADDe,EAASC,EAAY3K,GACK4K,GAE/C,GAAI0B,IACF3K,EAAM9H,GAAQ,IACTyS,KACAtB,EACD/N,EACAqP,EAAc/J,WAGbkB,GAEH,OADAnB,EAAkBgK,EAAc/J,SACzBZ,OAGN,GAAIvI,EAASsR,GAAW,CAC7B,IAAI6B,EAAmB,CAAgB,EAEvC,IAAK,MAAMpR,KAAOuP,EAAU,CAC1B,IAAKzF,EAAcsH,KAAsB9I,EACvC,MAGF,MAAM6I,EAAgB3C,SACde,EAASvP,GAAKwP,EAAY3K,GAChC4K,EACAzP,GAGEmR,IACFC,EAAmB,IACdD,KACAtB,EAAkB7P,EAAKmR,EAAc/J,UAG1CD,EAAkBgK,EAAc/J,SAE5BkB,IACF9B,EAAM9H,GAAQ0S,IAKpB,IAAKtH,EAAcsH,KACjB5K,EAAM9H,GAAQ,CACZkI,IAAK6I,KACF2B,IAEA9I,GACH,OAAO9B,EAOf,OADAW,GAAkB,GACXX,GCnMT,MAAM6K,GAAiB,CACrBrE,KAAMnL,EACNyP,eAAgBzP,EAChB0P,kBAAkB,GAGJ,SAAAC,GAKdpO,EAAkE,IAUlE,IAAImE,EAAW,IACV8J,MACAjO,GAEDM,EAAsC,CACxC+N,YAAa,EACb5N,SAAS,EACT6N,SAAS,EACT5N,UAAWkG,EAAWzC,EAAS7E,eAC/BwB,cAAc,EACdyN,aAAa,EACbC,cAAc,EACdC,oBAAoB,EACpB1N,SAAS,EACTH,cAAe,CAAE,EACjBD,YAAa,CAAE,EACfE,iBAAkB,CAAE,EACpBG,OAAQmD,EAASnD,QAAU,CAAE,EAC7Bd,SAAUiE,EAASjE,WAAY,GAEjC,MAAMyD,EAAqB,CAAE,EAC7B,IAmBI+K,EAnBAnP,GACF1E,EAASsJ,EAAS7E,gBAAkBzE,EAASsJ,EAAS/B,UAClDrG,EAAYoI,EAAS7E,eAAiB6E,EAAS/B,SAC/C,CAAE,EACJC,EAAc8B,EAAS3B,iBACtB,CAAA,EACAzG,EAAYwD,GACb+E,EAAS,CACXC,QAAQ,EACRF,OAAO,EACP1C,OAAO,GAELH,EAAgB,CAClB6C,MAAO,IAAIjI,IACX8D,SAAU,IAAI9D,IACduS,QAAS,IAAIvS,IACbsG,MAAO,IAAItG,IACXuF,MAAO,IAAIvF,KAGTwS,EAAQ,EACZ,MAAMjP,EAAiC,CACrCc,SAAS,EACTE,aAAa,EACbE,kBAAkB,EAClBD,eAAe,EACfE,cAAc,EACdC,SAAS,EACTC,QAAQ,GAEV,IAAI6N,EAA2B,IAC1BlP,GAEL,MAAMmP,EAAoC,CACxCpM,MAAO2C,IACP0J,MAAO1J,KAGH2J,EACJ7K,EAAS8K,eAAiBxQ,EAStB2C,EAAYqK,MAAOyD,IACvB,IACG/K,EAASjE,WACTP,EAAgBoB,SACf8N,EAAyB9N,SACzBmO,GACF,CACA,MAAMnO,EAAUoD,EAASgL,SACrBzI,SAAqB0I,KAAcpO,cAC7BqO,EAAyB1L,GAAS,GAExC5C,IAAYT,EAAWS,SACzB+N,EAAUC,MAAMvJ,KAAK,CACnBzE,cAMFuO,EAAsB,CAACjU,EAAkByF,MAE1CqD,EAASjE,WACTP,EAAgBmB,cACfnB,EAAgBkB,kBAChBgO,EAAyB/N,cACzB+N,EAAyBhO,qBAE1BxF,GAASP,MAAMyU,KAAK/N,EAAO6C,QAAQmL,SAASlU,IACvCA,IACFwF,EACI9C,EAAIsC,EAAWO,iBAAkBvF,EAAMwF,GACvCuG,GAAM/G,EAAWO,iBAAkBvF,OAI3CwT,EAAUC,MAAMvJ,KAAK,CACnB3E,iBAAkBP,EAAWO,iBAC7BC,cAAe4F,EAAcpG,EAAWO,sBA8ExC4O,EAAsB,CAC1BnU,EACAoU,EACAjV,EACA+I,KAEA,MAAME,EAAejG,EAAIkG,EAASrI,GAElC,GAAIoI,EAAO,CACT,MAAM9F,EAAeH,EACnB4E,EACA/G,EACAyB,EAAYtC,GAASgD,EAAI8B,EAAgBjE,GAAQb,GAGnDsC,EAAYa,IACX4F,GAAQA,EAAyBmM,gBAClCD,EACI1R,EACEqE,EACA/G,EACAoU,EAAuB9R,EAAeqL,GAAcvF,EAAME,KAE5DgM,EAActU,EAAMsC,GAExB0G,EAAOD,OAASjD,MAIdyO,EAAsB,CAC1BvU,EACAwU,EACA1F,EACA2F,EACAC,KAIA,IAAIC,GAAoB,EACpBC,GAAkB,EACtB,MAAMtL,EAA8D,CAClEtJ,QAGF,IAAK6I,EAASjE,SAAU,CACtB,IAAKkK,GAAe2F,EAAa,EAC3BpQ,EAAgBc,SAAWoO,EAAyBpO,WACtDyP,EAAkB5P,EAAWG,QAC7BH,EAAWG,QAAUmE,EAAOnE,QAAU0P,IACtCF,EAAoBC,IAAoBtL,EAAOnE,SAGjD,MAAM2P,EAAyBrK,EAC7BtI,EAAI8B,EAAgBjE,GACpBwU,GAGFI,IAAoBzS,EAAI6C,EAAWK,YAAarF,GAChD8U,EACI/I,GAAM/G,EAAWK,YAAarF,GAC9B0C,EAAIsC,EAAWK,YAAarF,GAAM,GACtCsJ,EAAOjE,YAAcL,EAAWK,YAChCsP,EACEA,IACEtQ,EAAgBgB,aAChBkO,EAAyBlO,cACzBuP,KAAqBE,EAG3B,GAAIhG,EAAa,CACf,MAAMiG,EAAyB5S,EAAI6C,EAAWM,cAAetF,GAExD+U,IACHrS,EAAIsC,EAAWM,cAAetF,EAAM8O,GACpCxF,EAAOhE,cAAgBN,EAAWM,cAClCqP,EACEA,IACEtQ,EAAgBiB,eAChBiO,EAAyBjO,gBACzByP,IAA2BjG,GAInC6F,GAAqBD,GAAgBlB,EAAUC,MAAMvJ,KAAKZ,GAG5D,OAAOqL,EAAoBrL,EAAS,CAAE,GAGlC0L,EAAsB,CAC1BhV,EACAyF,EACAqC,EACAL,KAMA,MAAMwN,EAAqB9S,EAAI6C,EAAWU,OAAQ1F,GAC5C4T,GACHvP,EAAgBoB,SAAW8N,EAAyB9N,UACrDhD,EAAUgD,IACVT,EAAWS,UAAYA,EAhOzB,IAAqBI,EA6OrB,GAXIgD,EAASqM,YAAcpN,GAlONjC,EAmOW,IAzHb,EAAC7F,EAAyB8H,KAC7CpF,EAAIsC,EAAWU,OAAQ1F,EAAM8H,GAC7B0L,EAAUC,MAAMvJ,KAAK,CACnBxE,OAAQV,EAAWU,UAsHiByP,CAAanV,EAAM8H,GAAvDsL,EAlODgC,IACCC,aAAa/B,GACbA,EAAQgC,WAAWzP,EAAUuP,IAiO7BhC,EAAmBvK,EAASqM,cAE5BG,aAAa/B,GACbF,EAAqB,KACrBtL,EACIpF,EAAIsC,EAAWU,OAAQ1F,EAAM8H,GAC7BiE,GAAM/G,EAAWU,OAAQ1F,KAI5B8H,GAAS2C,EAAUwK,EAAoBnN,GAASmN,KAChD7J,EAAc3D,IACfmM,EACA,CACA,MAAM2B,EAAmB,IACpB9N,KACCmM,GAAqBnR,EAAUgD,GAAW,CAAEA,WAAY,GAC5DC,OAAQV,EAAWU,OACnB1F,QAGFgF,EAAa,IACRA,KACAuQ,GAGL/B,EAAUC,MAAMvJ,KAAKqL,KAInBzB,EAAa3D,MAAOnQ,IACxBgU,EAAoBhU,GAAM,GAC1B,MAAMuC,QAAesG,EAASgL,SAC5B9M,EACA8B,EAAS2M,QdzaA,EACbrG,EACA9G,EACAsL,EACAtD,KAEA,MAAM7D,EAAiD,CAAE,EAEzD,IAAK,MAAMxM,KAAQmP,EAAa,CAC9B,MAAM/G,EAAejG,EAAIkG,EAASrI,GAElCoI,GAAS1F,EAAI8J,EAAQxM,EAAMoI,EAAME,IAGnC,MAAO,CACLqL,eACA5T,MAAO,IAAIoP,GACX3C,SACA6D,8BcwZEoF,CACEzV,GAAQkG,EAAO6C,MACfV,EACAQ,EAAS8K,aACT9K,EAASwH,4BAIb,OADA2D,EAAoBhU,GACbuC,GAoBHwR,EAA2B5D,MAC/B3D,EACAkJ,EACAF,EAEI,CACFG,OAAO,MAGT,IAAK,MAAM3V,KAAQwM,EAAQ,CACzB,MAAMpE,EAAQoE,EAAOxM,GAErB,GAAIoI,EAAO,CACT,MAAME,GAAEA,KAAOkM,GAAepM,EAE9B,GAAIE,EAAI,CACN,MAAMsN,EAAmB1P,EAAOkB,MAAMnH,IAAIqI,EAAGtI,MACvC6V,EACJzN,EAAME,QV7dFwN,EU6d8B1N,EAAgBE,OV3d1DwN,EAAejF,aAEdvF,EAAWwK,EAAejF,WACzBiF,EAAejF,SAAS3P,YAAYlB,OAAS4O,IAC9CrP,EAASuW,EAAejF,WACvB3M,OAAO4C,OAAOgP,EAAejF,UAAUkF,MACpCC,GACCA,EAAiB9U,YAAYlB,OAAS4O,OUsdlCiH,GAAqBxR,EAAgBkB,kBACvCyO,EAAoB,CAAChU,IAAO,GAG9B,MAAMiW,QAAmB/F,GACvB9H,EACAlC,EAAOtB,SACPmC,EACA2M,EACA7K,EAASwH,4BAA8BqF,EACvCE,GAOF,GAJIC,GAAqBxR,EAAgBkB,kBACvCyO,EAAoB,CAAChU,IAGnBiW,EAAW3N,EAAGtI,QAChBwV,EAAQG,OAAQ,EACZD,GACF,OAIHA,IACEvT,EAAI8T,EAAY3N,EAAGtI,MAChB4V,EACEjG,GACE3K,EAAWU,OACXuQ,EACA3N,EAAGtI,MAEL0C,EAAIsC,EAAWU,OAAQ4C,EAAGtI,KAAMiW,EAAW3N,EAAGtI,OAChD+L,GAAM/G,EAAWU,OAAQ4C,EAAGtI,QAGnCoL,EAAcoJ,UACNT,EACLS,EACAkB,EACAF,IVvgBG,IAACM,EU4gBZ,OAAON,EAAQG,OAiBXd,EAAwB,CAAC7U,EAAMU,KAClCmI,EAASjE,WACT5E,GAAQU,GAAQgC,EAAIqE,EAAa/G,EAAMU,IACvC+J,EAAUyL,IAAajS,IAEpB4C,EAAyC,CAC7C9G,EACAuC,EACA8D,IAEAH,EACElG,EACAmG,EACA,IACM8C,EAAOD,MACPhC,EACAtF,EAAYa,GACV2B,EACA+B,EAASjG,GACP,CAAEA,CAACA,GAAQuC,GACXA,GAEV8D,EACA9D,GAcEgS,EAAgB,CACpBtU,EACAb,EACA6N,EAA0B,CAAA,KAE1B,MAAM5E,EAAejG,EAAIkG,EAASrI,GAClC,IAAIwU,EAAsBrV,EAE1B,GAAIiJ,EAAO,CACT,MAAM0N,EAAiB1N,EAAME,GAEzBwN,KACDA,EAAelR,UACdlC,EAAIqE,EAAa/G,EAAMmN,GAAgBhO,EAAO2W,IAEhDtB,EACEjJ,GAAcuK,EAAe5N,MAAQ7I,EAAkBF,GACnD,GACAA,EAEFwM,GAAiBmK,EAAe5N,KAClC,IAAI4N,EAAe5N,IAAI8E,SAASkH,SAC7BiC,GACEA,EAAUC,SACT5B,EACAtJ,SAASiL,EAAUhX,SAEhB2W,EAAejI,KACpB9O,EAAgB+W,EAAe5N,KACjC4N,EAAejI,KAAKqG,SAASmC,IACtBA,EAAYhC,gBAAmBgC,EAAYzR,WAC1CpF,MAAMC,QAAQ+U,GAChB6B,EAAYxW,UAAY2U,EAAWuB,MAChCrV,GAAiBA,IAAS2V,EAAYlX,QAGzCkX,EAAYxW,QACV2U,IAAe6B,EAAYlX,SAAWqV,MAK9CsB,EAAejI,KAAKqG,SACjBoC,GACEA,EAASzW,QAAUyW,EAASnX,QAAUqV,IAGpCnJ,EAAYyK,EAAe5N,KACpC4N,EAAe5N,IAAI/I,MAAQ,IAE3B2W,EAAe5N,IAAI/I,MAAQqV,EAEtBsB,EAAe5N,IAAIjJ,MACtBuU,EAAUC,MAAMvJ,KAAK,CACnBlK,OACA8G,OAAQrG,EAAYsG,QAO7BiG,EAAQyH,aAAezH,EAAQuJ,cAC9BhC,EACEvU,EACAwU,EACAxH,EAAQuJ,YACRvJ,EAAQyH,aACR,GAGJzH,EAAQwJ,gBAAkBC,EAAQzW,IAG9B0W,EAAY,CAKhB1W,EACAb,EACA6N,KAEA,IAAK,MAAM2J,KAAYxX,EAAO,CAC5B,IAAKA,EAAMiC,eAAeuV,GACxB,OAEF,MAAMnC,EAAarV,EAAMwX,GACnBnQ,EAAYxG,EAAO,IAAM2W,EACzBvO,EAAQjG,EAAIkG,EAAS7B,IAE1BN,EAAOkB,MAAMnH,IAAID,IAChBT,EAASiV,IACRpM,IAAUA,EAAME,MAClBpJ,EAAasV,GACVkC,EAAUlQ,EAAWgO,EAAYxH,GACjCsH,EAAc9N,EAAWgO,EAAYxH,KAIvC4J,EAA0C,CAC9C5W,EACAb,EACA6N,EAAU,CAAA,KAEV,MAAM5E,EAAQjG,EAAIkG,EAASrI,GACrBsQ,EAAepK,EAAOkB,MAAMnH,IAAID,GAChC6W,EAAapW,EAAYtB,GAE/BuD,EAAIqE,EAAa/G,EAAM6W,GAEnBvG,GACFkD,EAAUpM,MAAM8C,KAAK,CACnBlK,OACA8G,OAAQrG,EAAYsG,MAInB1C,EAAgBc,SACfd,EAAgBgB,aAChBkO,EAAyBpO,SACzBoO,EAAyBlO,cAC3B2H,EAAQyH,aAERjB,EAAUC,MAAMvJ,KAAK,CACnBlK,OACAqF,YAAauH,GAAe3I,EAAgB8C,GAC5C5B,QAAS0P,EAAU7U,EAAM6W,OAI7BzO,GAAUA,EAAME,IAAOjJ,EAAkBwX,GAErCvC,EAActU,EAAM6W,EAAY7J,GADhC0J,EAAU1W,EAAM6W,EAAY7J,GAIlC6B,GAAU7O,EAAMkG,IAAWsN,EAAUC,MAAMvJ,KAAK,IAAKlF,IACrDwO,EAAUC,MAAMvJ,KAAK,CACnBlK,KAAMgJ,EAAOD,MAAQ/I,OAAO2B,EAC5BmF,OAAQrG,EAAYsG,MAIlBgB,EAA0BoI,MAAOxQ,IACrCqJ,EAAOD,OAAQ,EACf,MAAMnJ,EAASD,EAAMC,OACrB,IAAII,EAAeJ,EAAOI,KACtB8W,GAAsB,EAC1B,MAAM1O,EAAejG,EAAIkG,EAASrI,GAC5B+W,EAA8BvC,IAClCsC,EACEE,OAAO/T,MAAMuR,IACZtV,EAAasV,IAAevR,MAAMuR,EAAW1J,YAC9CL,EAAU+J,EAAYrS,EAAI4E,EAAa/G,EAAMwU,KAE3CyC,EAA6B5I,GAAmBxF,EAASyF,MACzD4I,EAA4B7I,GAChCxF,EAAS+J,gBAGX,GAAIxK,EAAO,CACT,IAAIN,EACArC,EACJ,MAAM+O,EAAa5U,EAAOX,KACtB0O,GAAcvF,EAAME,IACpB5I,EAAcC,GACZmP,EACJnP,EAAMV,OAASiE,GAAevD,EAAMV,OAASiE,EACzCiU,KC9uBInK,ED+uBQ5E,EAAME,IC9uBpBS,QACPiE,EAAQuD,UACPvD,EAAQ0D,KACR1D,EAAQ2D,KACR3D,EAAQwD,WACRxD,EAAQyD,WACRzD,EAAQ4D,SACR5D,EAAQ6D,WDwuBDhI,EAASgL,UACT1R,EAAI6C,EAAWU,OAAQ1F,IACvBoI,EAAME,GAAG8O,OElvBL,EACbtI,EACAjH,EACAoL,EACAL,EAIAtE,KAEIA,EAAKI,WAEGuE,GAAe3E,EAAKK,YACrB9G,GAAaiH,IACbmE,EAAcL,EAAepE,SAAWF,EAAKE,WAC9CM,IACCmE,EAAcL,EAAenE,WAAaH,EAAKG,aACjDK,GFkuBHuI,CACEvI,EACA3M,EAAI6C,EAAWM,cAAetF,GAC9BgF,EAAWiO,YACXiE,EACAD,GAEEK,EAAUzI,GAAU7O,EAAMkG,EAAQ4I,GAExCpM,EAAIqE,EAAa/G,EAAMwU,GAEnB1F,GACF1G,EAAME,GAAGL,QAAUG,EAAME,GAAGL,OAAOtI,GACnCyT,GAAsBA,EAAmB,IAChChL,EAAME,GAAGP,UAClBK,EAAME,GAAGP,SAASpI,GAGpB,MAAM8H,EAAa8M,EAAoBvU,EAAMwU,EAAY1F,GAEnD4F,GAAgBtJ,EAAc3D,IAAe6P,EASnD,IAPCxI,GACC0E,EAAUC,MAAMvJ,KAAK,CACnBlK,OACAf,KAAMU,EAAMV,KACZ6H,OAAQrG,EAAYsG,KAGpBoQ,EAWF,OAVI9S,EAAgBoB,SAAW8N,EAAyB9N,WAChC,WAAlBoD,EAASyF,KACPQ,GACFhJ,IAEQgJ,GACVhJ,KAKF4O,GACAlB,EAAUC,MAAMvJ,KAAK,CAAElK,UAAUsX,EAAU,CAAA,EAAK7P,IAMpD,IAFCqH,GAAewI,GAAW9D,EAAUC,MAAMvJ,KAAK,IAAKlF,IAEjD6D,EAASgL,SAAU,CACrB,MAAMnO,OAAEA,SAAiBoO,EAAW,CAAC9T,IAIrC,GAFA+W,EAA2BvC,GAEvBsC,EAAqB,CACvB,MAAMS,EAA4BjI,GAChCtK,EAAWU,OACX2C,EACArI,GAEIwX,EAAoBlI,GACxB5J,EACA2C,EACAkP,EAA0BvX,MAAQA,GAGpC8H,EAAQ0P,EAAkB1P,MAC1B9H,EAAOwX,EAAkBxX,KAEzByF,EAAU2F,EAAc1F,SAG1BsO,EAAoB,CAAChU,IAAO,GAC5B8H,SACQoI,GACJ9H,EACAlC,EAAOtB,SACPmC,EACA2M,EACA7K,EAASwH,4BAEXrQ,GACFgU,EAAoB,CAAChU,IAErB+W,EAA2BvC,GAEvBsC,IACEhP,EACFrC,GAAU,GAEVpB,EAAgBoB,SAChB8N,EAAyB9N,WAEzBA,QAAgBsO,EAAyB1L,GAAS,KAKpDyO,IACF1O,EAAME,GAAG8O,MACPX,EACErO,EAAME,GAAG8O,MAIbpC,EAAoBhV,EAAMyF,EAASqC,EAAOL,IC31BnC,IAACuF,GDg2BRyK,EAAc,CAACvP,EAAU5G,KAC7B,GAAIa,EAAI6C,EAAWU,OAAQpE,IAAQ4G,EAAIK,MAErC,OADAL,EAAIK,QACG,GAKLkO,EAAwCtG,MAAOnQ,EAAMgN,EAAU,CAAA,KACnE,IAAIvH,EACAiN,EACJ,MAAMgF,EAAa5N,EAAsB9J,GAEzC,GAAI6I,EAASgL,SAAU,CACrB,MAAMnO,OAhb0ByK,OAAOpQ,IACzC,MAAM2F,OAAEA,SAAiBoO,EAAW/T,GAEpC,GAAIA,EACF,IAAK,MAAMC,KAAQD,EAAO,CACxB,MAAM+H,EAAQ3F,EAAIuD,EAAQ1F,GAC1B8H,EACIpF,EAAIsC,EAAWU,OAAQ1F,EAAM8H,GAC7BiE,GAAM/G,EAAWU,OAAQ1F,QAG/BgF,EAAWU,OAASA,EAGtB,OAAOA,GAkagBiS,CACnBlW,EAAYzB,GAAQA,EAAO0X,GAG7BjS,EAAU2F,EAAc1F,GACxBgN,EAAmB1S,GACd0X,EAAW3I,MAAM/O,GAASmC,EAAIuD,EAAQ1F,KACvCyF,OACKzF,GACT0S,SACQkF,QAAQC,IACZH,EAAWnR,KAAI4J,MAAO3J,IACpB,MAAM4B,EAAQjG,EAAIkG,EAAS7B,GAC3B,aAAauN,EACX3L,GAASA,EAAME,GAAK,CAAE9B,CAACA,GAAY4B,GAAUA,QAInD2H,MAAMjO,UACL4Q,GAAqB1N,EAAWS,UAAYK,KAE/C4M,EAAmBjN,QAAgBsO,EAAyB1L,GAqB9D,OAlBAmL,EAAUC,MAAMvJ,KAAK,KACdlE,EAAShG,KACZqE,EAAgBoB,SAAW8N,EAAyB9N,UACpDA,IAAYT,EAAWS,QACrB,CAAA,EACA,CAAEzF,WACF6I,EAASgL,WAAa7T,EAAO,CAAEyF,WAAY,GAC/CC,OAAQV,EAAWU,SAGrBsH,EAAQ8K,cACLpF,GACDxD,GACE7G,EACAoP,EACAzX,EAAO0X,EAAaxR,EAAO6C,OAGxB2J,GAGHwD,EACJwB,IAIA,MAAM5Q,EAAS,IACTkC,EAAOD,MAAQhC,EAAc9C,GAGnC,OAAOxC,EAAYiW,GACf5Q,EACAd,EAAS0R,GACPvV,EAAI2E,EAAQ4Q,GACZA,EAAWnR,KAAKvG,GAASmC,EAAI2E,EAAQ9G,MAGvC+X,GAAoD,CACxD/X,EACA4D,KACI,CACJ+D,UAAWxF,GAAKyB,GAAaoB,GAAYU,OAAQ1F,GACjDmF,UAAWhD,GAAKyB,GAAaoB,GAAYK,YAAarF,GACtD8H,MAAO3F,GAAKyB,GAAaoB,GAAYU,OAAQ1F,GAC7CwF,eAAgBrD,EAAI6C,EAAWO,iBAAkBvF,GACjD6H,YAAa1F,GAAKyB,GAAaoB,GAAYM,cAAetF,KActDgY,GAA0C,CAAChY,EAAM8H,EAAOkF,KAC5D,MAAM9E,GAAO/F,EAAIkG,EAASrI,EAAM,CAAEsI,GAAI,KAAMA,IAAM,CAAE,GAAEJ,IAChD+P,EAAe9V,EAAI6C,EAAWU,OAAQ1F,IAAS,CAAE,GAG/CkI,IAAKgQ,EAAUxP,QAAEA,EAAOzJ,KAAEA,KAASkZ,GAAoBF,EAE/DvV,EAAIsC,EAAWU,OAAQ1F,EAAM,IACxBmY,KACArQ,EACHI,QAGFsL,EAAUC,MAAMvJ,KAAK,CACnBlK,OACA0F,OAAQV,EAAWU,OACnBD,SAAS,IAGXuH,GAAWA,EAAQ8K,aAAe5P,GAAOA,EAAIK,OAASL,EAAIK,SA4BtD5C,GAA2CjB,GAC/C8O,EAAUC,MAAMrJ,UAAU,CACxBF,KACEtG,IGn/BO,IACb5D,EACAoY,EACAvT,EAFA7E,EHy/B8B0E,EAAM1E,KGx/BpCoY,EHw/B0CxU,EAAU5D,KGv/BpD6E,EHu/B0DH,EAAMG,MGr/B/D7E,GACAoY,GACDpY,IAASoY,IACTtO,EAAsB9J,GAAM+O,MACzBsJ,GACCA,IACCxT,EACGwT,IAAgBD,EAChBC,EAAYpJ,WAAWmJ,IACvBA,EAAWnJ,WAAWoJ,QTPjB,EACbC,EAIAjU,EACAS,EACAf,KAEAe,EAAgBwT,GAChB,MAAMtY,KAAEA,KAAS4D,GAAc0U,EAE/B,OACElN,EAAcxH,IACdM,OAAOqF,KAAK3F,GAAWf,QAAUqB,OAAOqF,KAAKlF,GAAiBxB,QAC9DqB,OAAOqF,KAAK3F,GAAWmS,MACpBzU,GACC+C,EAAgB/C,OACdyC,GAAUZ,MMk+BVoV,CACE3U,EACCc,EAAMd,WAA+BS,EACtCmU,GACA9T,EAAM+T,eAGR/T,EAAMmB,SAAS,CACbiB,OAAQ,IAAKC,MACV/B,KACApB,OAIR0G,YAcCpB,GAA8C,CAAClJ,EAAMgN,EAAU,CAAA,KACnE,IAAK,MAAMxG,KAAaxG,EAAO8J,EAAsB9J,GAAQkG,EAAO6C,MAClE7C,EAAO6C,MAAM2P,OAAOlS,GACpBN,EAAOkB,MAAMsR,OAAOlS,GAEfwG,EAAQ2L,YACX5M,GAAM1D,EAAS7B,GACfuF,GAAMhF,EAAaP,KAGpBwG,EAAQ4L,WAAa7M,GAAM/G,EAAWU,OAAQc,IAC9CwG,EAAQ6L,WAAa9M,GAAM/G,EAAWK,YAAamB,IACnDwG,EAAQ8L,aAAe/M,GAAM/G,EAAWM,cAAekB,IACvDwG,EAAQ+L,kBACPhN,GAAM/G,EAAWO,iBAAkBiB,IACpCqC,EAAS3B,mBACP8F,EAAQgM,kBACTjN,GAAM9H,EAAgBuC,GAG1BgN,EAAUC,MAAMvJ,KAAK,CACnBpD,OAAQrG,EAAYsG,KAGtByM,EAAUC,MAAMvJ,KAAK,IAChBlF,KACEgI,EAAQ6L,UAAiB,CAAE1T,QAAS0P,KAAhB,CAAA,KAG1B7H,EAAQiM,aAAenT,KAGpBqD,GAAgE,EACpEvE,WACA5E,YAGGyC,EAAUmC,IAAaoE,EAAOD,OAC7BnE,GACFsB,EAAOtB,SAAS3E,IAAID,MAEpB4E,EAAWsB,EAAOtB,SAAS0B,IAAItG,GAAQkG,EAAOtB,SAAS8T,OAAO1Y,KAI5DuH,GAA0C,CAACvH,EAAMgN,EAAU,CAAA,KAC/D,IAAI5E,EAAQjG,EAAIkG,EAASrI,GACzB,MAAMkZ,EACJzW,EAAUuK,EAAQpI,WAAanC,EAAUoG,EAASjE,UAwBpD,OAtBAlC,EAAI2F,EAASrI,EAAM,IACboI,GAAS,CAAA,EACbE,GAAI,IACEF,GAASA,EAAME,GAAKF,EAAME,GAAK,CAAEJ,IAAK,CAAElI,SAC5CA,OACA+I,OAAO,KACJiE,KAGP9G,EAAO6C,MAAMzC,IAAItG,GAEboI,EACFe,GAAkB,CAChBvE,SAAUnC,EAAUuK,EAAQpI,UACxBoI,EAAQpI,SACRiE,EAASjE,SACb5E,SAGFmU,EAAoBnU,GAAM,EAAMgN,EAAQ7N,OAGnC,IACD+Z,EACA,CAAEtU,SAAUoI,EAAQpI,UAAYiE,EAASjE,UACzC,MACAiE,EAASsQ,YACT,CACE5I,WAAYvD,EAAQuD,SACpBG,IAAKxC,GAAalB,EAAQ0D,KAC1BC,IAAKzC,GAAalB,EAAQ2D,KAC1BF,UAAWvC,GAAqBlB,EAAQyD,WACxCD,UAAWtC,GAAalB,EAAQwD,WAChCI,QAAS1C,GAAalB,EAAQ4D,UAEhC,GACJ5Q,OACA+H,WACAE,OAAQF,EACRG,IAAMA,IACJ,GAAIA,EAAK,CACPX,GAASvH,EAAMgN,GACf5E,EAAQjG,EAAIkG,EAASrI,GAErB,MAAMoZ,EAAW3X,EAAYyG,EAAI/I,QAC7B+I,EAAImR,kBACDnR,EAAImR,iBAAiB,yBAAyB,IAEjDnR,EACEoR,EIvnCD,CAACpR,GACd0D,GAAa1D,IAAQnJ,EAAgBmJ,GJsnCL+I,CAAkBmI,GACpCvL,EAAOzF,EAAME,GAAGuF,MAAQ,GAE9B,GACEyL,EACIzL,EAAKkI,MAAM9I,GAAgBA,IAAWmM,IACtCA,IAAahR,EAAME,GAAGJ,IAE1B,OAGFxF,EAAI2F,EAASrI,EAAM,CACjBsI,GAAI,IACCF,EAAME,MACLgR,EACA,CACEzL,KAAM,IACDA,EAAKhM,OAAOgK,IACfuN,KACI5Z,MAAMC,QAAQ0C,EAAI8B,EAAgBjE,IAAS,CAAC,IAAM,IAExDkI,IAAK,CAAEjJ,KAAMma,EAASna,KAAMe,SAE9B,CAAEkI,IAAKkR,MAIfjF,EAAoBnU,GAAM,OAAO2B,EAAWyX,QAE5ChR,EAAQjG,EAAIkG,EAASrI,EAAM,CAAA,GAEvBoI,EAAME,KACRF,EAAME,GAAGS,OAAQ,IAGlBF,EAAS3B,kBAAoB8F,EAAQ9F,qBAClCpH,EAAmBoG,EAAOkB,MAAOpH,KAASgJ,EAAOC,SACnD/C,EAAOmN,QAAQ/M,IAAItG,MAMvBuZ,GAAc,IAClB1Q,EAASgK,kBACT3D,GAAsB7G,EAASoP,EAAavR,EAAO6C,OAyB/CyQ,GACJ,CAACC,EAASC,IAAcvJ,MAAOwJ,IAC7B,IAAIC,EACAD,IACFA,EAAEE,gBAAkBF,EAAEE,iBACrBF,EAA+BG,SAC7BH,EAA+BG,WAEpC,IAAIC,EACFtZ,EAAYsG,GAMd,GAJAyM,EAAUC,MAAMvJ,KAAK,CACnBgJ,cAAc,IAGZrK,EAASgL,SAAU,CACrB,MAAMnO,OAAEA,EAAMoB,OAAEA,SAAiBgN,IACjC9O,EAAWU,OAASA,EACpBqU,EAActZ,EAAYqG,cAEpBiN,EAAyB1L,GAGjC,GAAInC,EAAOtB,SAASoV,KAClB,IAAK,MAAMha,KAAQkG,EAAOtB,SACxBmH,GAAMgO,EAAa/Z,GAMvB,GAFA+L,GAAM/G,EAAWU,OAAQ,QAErB0F,EAAcpG,EAAWU,QAAS,CACpC8N,EAAUC,MAAMvJ,KAAK,CACnBxE,OAAQ,CAAE,IAEZ,UACQ+T,EAAQM,EAAmCJ,GACjD,MAAO7R,GACP8R,EAAe9R,QAGb4R,SACIA,EAAU,IAAK1U,EAAWU,QAAUiU,GAE5CJ,KACAjE,WAAWiE,IAUb,GAPA/F,EAAUC,MAAMvJ,KAAK,CACnB+I,aAAa,EACbC,cAAc,EACdC,mBAAoB/H,EAAcpG,EAAWU,UAAYkU,EACzD7G,YAAa/N,EAAW+N,YAAc,EACtCrN,OAAQV,EAAWU,SAEjBkU,EACF,MAAMA,GAoCNK,GAAqC,CACzC9T,EACA+T,EAAmB,CAAA,KAEnB,MAAMC,EAAgBhU,EAAa1F,EAAY0F,GAAclC,EACvDmW,EAAqB3Z,EAAY0Z,GACjCE,EAAqBjP,EAAcjF,GACnCW,EAASuT,EAAqBpW,EAAiBmW,EAMrD,GAJKF,EAAiBI,oBACpBrW,EAAiBkW,IAGdD,EAAiBK,WAAY,CAChC,GAAIL,EAAiBM,gBAAiB,CACpC,MAAMC,EAAgB,IAAI3Z,IAAI,IACzBoF,EAAO6C,SACP7E,OAAOqF,KAAKqD,GAAe3I,EAAgB8C,MAEhD,IAAK,MAAMP,KAAahH,MAAMyU,KAAKwG,GACjCtY,EAAI6C,EAAWK,YAAamB,GACxB9D,EAAIoE,EAAQN,EAAWrE,EAAI4E,EAAaP,IACxCoQ,EACEpQ,EACArE,EAAI2E,EAAQN,QAGf,CACL,GAAInG,GAASoB,EAAY0E,GACvB,IAAK,MAAMnG,KAAQkG,EAAO6C,MAAO,CAC/B,MAAMX,EAAQjG,EAAIkG,EAASrI,GAC3B,GAAIoI,GAASA,EAAME,GAAI,CACrB,MAAMwN,EAAiBtW,MAAMC,QAAQ2I,EAAME,GAAGuF,MAC1CzF,EAAME,GAAGuF,KAAK,GACdzF,EAAME,GAAGJ,IAEb,GAAIqD,GAAcuK,GAAiB,CACjC,MAAM4E,EAAO5E,EAAe6E,QAAQ,QACpC,GAAID,EAAM,CACRA,EAAKE,QACL,SAOV,IAAK,MAAMpU,KAAaN,EAAO6C,MAAO,CACpC,MAAM5J,EAAQgD,EAAI2E,EAAQN,EAAWrE,EAAI8B,EAAgBuC,IAEpD/E,EAAYtC,KACfuD,EAAIoE,EAAQN,EAAWrH,GACvByX,EACEpQ,EACArE,EAAI2E,EAAQN,MAMpBO,EAActG,EAAYqG,GAE1B0M,EAAUpM,MAAM8C,KAAK,CACnBpD,OAAQ,IAAKA,KAGf0M,EAAUC,MAAMvJ,KAAK,CACnBpD,OAAQ,IAAKA,KAIjBZ,EAAS,CACP6C,MAAOmR,EAAiBM,gBAAkBtU,EAAO6C,MAAQ,IAAIjI,IAC7DuS,QAAS,IAAIvS,IACbsG,MAAO,IAAItG,IACX8D,SAAU,IAAI9D,IACduF,MAAO,IAAIvF,IACX2F,UAAU,EACV8B,MAAO,IAGTS,EAAOD,OACJ1E,EAAgBoB,WACfyU,EAAiBjB,eACjBiB,EAAiBM,gBAErBxR,EAAO3C,QAAUwC,EAAS3B,iBAE1BsM,EAAUC,MAAMvJ,KAAK,CACnB6I,YAAamH,EAAiBW,gBAC1B7V,EAAW+N,YACX,EACJ5N,SAASkV,IAELH,EAAiBrB,UACf7T,EAAWG,WAET+U,EAAiBI,mBAChB7P,EAAUtE,EAAYlC,KAE/BgP,cAAaiH,EAAiBY,iBAC1B9V,EAAWiO,YAEf5N,YAAagV,EACT,CAAA,EACAH,EAAiBM,gBACfN,EAAiBI,mBAAqBvT,EACpC6F,GAAe3I,EAAgB8C,GAC/B/B,EAAWK,YACb6U,EAAiBI,mBAAqBnU,EACpCyG,GAAe3I,EAAgBkC,GAC/B+T,EAAiBrB,UACf7T,EAAWK,YACX,CAAE,EACZC,cAAe4U,EAAiBpB,YAC5B9T,EAAWM,cACX,CAAE,EACNI,OAAQwU,EAAiBa,WAAa/V,EAAWU,OAAS,CAAE,EAC5DyN,qBAAoB+G,EAAiBc,wBACjChW,EAAWmO,mBAEfD,cAAc,KAIZ0H,GAAoC,CAACzU,EAAY+T,IACrDD,GACE3O,EAAWnF,GACNA,EAAwBY,GACzBZ,EACJ+T,GAqBE1B,GACJjD,IAEAvQ,EAAa,IACRA,KACAuQ,IAaD5Q,GAAU,CACdd,QAAS,CACP0D,YACA2B,cACA6O,iBACAyB,gBACAxB,YACArS,cACAmO,aACAyF,eACA1S,YACAgO,YACA/O,YACAmV,eAtvC0C,CAC5Cjb,EACA8G,EAAS,GACToU,EACAC,EACAC,GAAkB,EAClBC,GAA6B,KAE7B,GAAIF,GAAQD,IAAWrS,EAASjE,SAAU,CAExC,GADAoE,EAAOC,QAAS,EACZoS,GAA8B7b,MAAMC,QAAQ0C,EAAIkG,EAASrI,IAAQ,CACnE,MAAM+Z,EAAcmB,EAAO/Y,EAAIkG,EAASrI,GAAOmb,EAAKG,KAAMH,EAAKI,MAC/DH,GAAmB1Y,EAAI2F,EAASrI,EAAM+Z,GAGxC,GACEsB,GACA7b,MAAMC,QAAQ0C,EAAI6C,EAAWU,OAAQ1F,IACrC,CACA,MAAM0F,EAASwV,EACb/Y,EAAI6C,EAAWU,OAAQ1F,GACvBmb,EAAKG,KACLH,EAAKI,MAEPH,GAAmB1Y,EAAIsC,EAAWU,OAAQ1F,EAAM0F,GKlPzC,EAAIwC,EAAQlI,MACxB4B,EAAQO,EAAI+F,EAAKlI,IAAO6C,QAAUkJ,GAAM7D,EAAKlI,ILkPxCwb,CAAgBxW,EAAWU,OAAQ1F,GAGrC,IACGqE,EAAgBiB,eACfiO,EAAyBjO,gBAC3B+V,GACA7b,MAAMC,QAAQ0C,EAAI6C,EAAWM,cAAetF,IAC5C,CACA,MAAMsF,EAAgB4V,EACpB/Y,EAAI6C,EAAWM,cAAetF,GAC9Bmb,EAAKG,KACLH,EAAKI,MAEPH,GAAmB1Y,EAAIsC,EAAWM,cAAetF,EAAMsF,IAGrDjB,EAAgBgB,aAAekO,EAAyBlO,eAC1DL,EAAWK,YAAcuH,GAAe3I,EAAgB8C,IAG1DyM,EAAUC,MAAMvJ,KAAK,CACnBlK,OACAmF,QAAS0P,EAAU7U,EAAM8G,GACzBzB,YAAaL,EAAWK,YACxBK,OAAQV,EAAWU,OACnBD,QAAST,EAAWS,eAGtB/C,EAAIqE,EAAa/G,EAAM8G,IAisCvBqC,qBACAsS,WAvrCgB/V,IAClBV,EAAWU,OAASA,EACpB8N,EAAUC,MAAMvJ,KAAK,CACnBxE,OAAQV,EAAWU,OACnBD,SAAS,KAorCTiW,eAz5BF1b,GAEA4B,EACEO,EACE6G,EAAOD,MAAQhC,EAAc9C,EAC7BjE,EACA6I,EAAS3B,iBAAmB/E,EAAI8B,EAAgBjE,EAAM,IAAM,KAo5B9Dia,UACA0B,oBA3BwB,IAC1BrQ,EAAWzC,EAAS7E,gBACnB6E,EAAS7E,gBAA6B4X,MAAM9U,IAC3C8T,GAAM9T,EAAQ+B,EAASgT,cACvBrI,EAAUC,MAAMvJ,KAAK,CACnB9E,WAAW,OAuBb4B,iBAr8BqB,KACvB,IAAK,MAAMhH,KAAQkG,EAAOmN,QAAS,CACjC,MAAMjL,EAAejG,EAAIkG,EAASrI,GAElCoI,IACGA,EAAME,GAAGuF,KACNzF,EAAME,GAAGuF,KAAKkC,OAAO7H,IAAS2D,GAAK3D,MAClC2D,GAAKzD,EAAME,GAAGJ,OACnBgB,GAAWlJ,GAGfkG,EAAOmN,QAAU,IAAIvS,KA27BnBgb,aAhTkBlX,IAChBnC,EAAUmC,KACZ4O,EAAUC,MAAMvJ,KAAK,CAAEtF,aACvBsK,GACE7G,GACA,CAACH,EAAKlI,KACJ,MAAMqP,EAAsBlN,EAAIkG,EAASrI,GACrCqP,IACFnH,EAAItD,SAAWyK,EAAa/G,GAAG1D,UAAYA,EAEvCpF,MAAMC,QAAQ4P,EAAa/G,GAAGuF,OAChCwB,EAAa/G,GAAGuF,KAAKqG,SAASnD,IAC5BA,EAASnM,SAAWyK,EAAa/G,GAAG1D,UAAYA,QAKxD,GACA,KA+RF4O,YACAnP,kBACA,WAAIgE,GACF,OAAOA,CACR,EACD,eAAItB,GACF,OAAOA,CACR,EACD,UAAIiC,GACF,OAAOA,CACR,EACD,UAAIA,CAAO7J,GACT6J,EAAS7J,CACV,EACD,kBAAI8E,GACF,OAAOA,CACR,EACD,UAAIiC,GACF,OAAOA,CACR,EACD,UAAIA,CAAO/G,GACT+G,EAAS/G,CACV,EACD,cAAI6F,GACF,OAAOA,CACR,EACD,YAAI6D,GACF,OAAOA,CACR,EACD,YAAIA,CAAS1J,GACX0J,EAAW,IACNA,KACA1J,EAEN,GAEHiL,UAnfiD1F,IACjDsE,EAAOD,OAAQ,EACfwK,EAA2B,IACtBA,KACA7O,EAAMd,WAEJ+B,GAAW,IACbjB,EACHd,UAAW2P,KA4ebkD,UACAlP,YACAiS,gBACAnT,MA3iBwC,CACxCrG,EAIAsC,IAEAgJ,EAAWtL,GACPwT,EAAUC,MAAMrJ,UAAU,CACxBF,KAAO6R,GACL/b,EACE6G,OAAUlF,EAAWW,GACrByZ,KAONlV,EACE7G,EACAsC,GACA,GAshBNsU,WACAV,YACA0E,SACAoB,WA1QkD,CAAChc,EAAMgN,EAAU,CAAA,KAC/D7K,EAAIkG,EAASrI,KACXyB,EAAYuL,EAAQ1K,cACtBsU,EAAS5W,EAAMS,EAAY0B,EAAI8B,EAAgBjE,MAE/C4W,EACE5W,EACAgN,EAAQ1K,cAEVI,EAAIuB,EAAgBjE,EAAMS,EAAYuM,EAAQ1K,gBAG3C0K,EAAQ8L,aACX/M,GAAM/G,EAAWM,cAAetF,GAG7BgN,EAAQ6L,YACX9M,GAAM/G,EAAWK,YAAarF,GAC9BgF,EAAWG,QAAU6H,EAAQ1K,aACzBuS,EAAU7U,EAAMS,EAAY0B,EAAI8B,EAAgBjE,KAChD6U,KAGD7H,EAAQ4L,YACX7M,GAAM/G,EAAWU,OAAQ1F,GACzBqE,EAAgBoB,SAAWK,KAG7B0N,EAAUC,MAAMvJ,KAAK,IAAKlF,MA+O5BiX,YAjlBqDjc,IACrDA,GACE8J,EAAsB9J,GAAMkU,SAASgI,GACnCnQ,GAAM/G,EAAWU,OAAQwW,KAG7B1I,EAAUC,MAAMvJ,KAAK,CACnBxE,OAAQ1F,EAAOgF,EAAWU,OAAS,CAAE,KA2kBvCwD,cACA8O,YACAmE,SAzG8C,CAACnc,EAAMgN,EAAU,CAAA,KAC/D,MAAM5E,EAAQjG,EAAIkG,EAASrI,GACrB8V,EAAiB1N,GAASA,EAAME,GAEtC,GAAIwN,EAAgB,CAClB,MAAMsD,EAAWtD,EAAejI,KAC5BiI,EAAejI,KAAK,GACpBiI,EAAe5N,IAEfkR,EAAS7Q,QACX6Q,EAAS7Q,QACTyE,EAAQoP,cACN9Q,EAAW8N,EAAS5Q,SACpB4Q,EAAS5Q,YA6FfuP,kBAGF,MAAO,IACFpT,GACH0X,YAAa1X,GAEjB,CMphDA,IAAA2X,GAAe,KACb,GAAsB,oBAAXC,QAA0BA,OAAOC,WAC1C,OAAOD,OAAOC,aAGhB,MAAMC,EACmB,oBAAhBC,YAA8Btd,KAAKud,MAA4B,IAApBD,YAAYC,MAEhE,MAAO,uCAAuC1a,QAAQ,SAAU2a,IAC9D,MAAMC,GAAqB,GAAhBC,KAAKC,SAAgBN,GAAK,GAAK,EAE1C,OAAa,KAALG,EAAWC,EAAS,EAAJA,EAAW,GAAKG,SAAS,QCRrDC,GAAe,CACbjd,EACA2C,EACAqK,EAAiC,CAAA,IAEjCA,EAAQ8K,aAAerW,EAAYuL,EAAQ8K,aACvC9K,EAAQkQ,WACR,GAAGld,KAAQyB,EAAYuL,EAAQmQ,YAAcxa,EAAQqK,EAAQmQ,cAC7D,GCTNC,GAAe,CAAI1c,EAAWvB,IAAwB,IACjDuB,KACAoJ,EAAsB3K,ICJ3Bke,GAAmBle,GACjBK,MAAMC,QAAQN,GAASA,EAAMoH,KAAI,cAAmB5E,ECO9B,SAAA2b,GACtB5c,EACAiC,EACAxD,GAEA,MAAO,IACFuB,EAAKyL,MAAM,EAAGxJ,MACdmH,EAAsB3K,MACtBuB,EAAKyL,MAAMxJ,GAElB,CChBA,IAAA4a,GAAe,CACb7c,EACAuT,EACAuJ,IAEKhe,MAAMC,QAAQiB,IAIfe,EAAYf,EAAK8c,MACnB9c,EAAK8c,QAAM7b,GAEbjB,EAAK+c,OAAOD,EAAI,EAAG9c,EAAK+c,OAAOxJ,EAAM,GAAG,IAEjCvT,GARE,GCNXgd,GAAe,CAAIhd,EAAWvB,IAAwB,IACjD2K,EAAsB3K,MACtB2K,EAAsBpJ,ICY3B,IAAAid,GAAe,CAAIjd,EAAWiC,IAC5BlB,EAAYkB,GACR,GAdN,SAA4BjC,EAAWkd,GACrC,IAAIC,EAAI,EACR,MAAMC,EAAO,IAAIpd,GAEjB,IAAK,MAAMiC,KAASib,EAClBE,EAAKL,OAAO9a,EAAQkb,EAAG,GACvBA,IAGF,OAAOjc,EAAQkc,GAAMjb,OAASib,EAAO,EACvC,CAKMC,CACErd,EACCoJ,EAAsBnH,GAAoBqb,MAAK,CAACC,EAAGC,IAAMD,EAAIC,KCrBtEC,GAAe,CAAIzd,EAAW0d,EAAgBC,MAC3C3d,EAAK0d,GAAS1d,EAAK2d,IAAW,CAAC3d,EAAK2d,GAAS3d,EAAK0d,KCDrDE,GAAe,CAAIvE,EAAkBpX,EAAexD,KAClD4a,EAAYpX,GAASxD,EACd4a,gBnDgDPrV,GAEAA,EAAM6Z,OAAOtX,EAAuDvC,WEtBtE,SAGEA,GACA,MAAMC,EAAUlB,KACT+a,EAASC,GAAcnb,EAAMyB,UAAS,IACvClB,QACJA,EAAUc,EAAQd,QAAO6a,SACzBA,EAAQC,SACRA,EAAQ1V,OACRA,EAAMiS,OACNA,EAASxR,EAAYkV,QACrBA,EAAOC,QACPA,EAAOC,QACPA,EAAOP,OACPA,EAAMQ,UACNA,EAASC,eACTA,KACGC,GACDva,EAEEwa,EAAS/O,MAAOxQ,IACpB,IAAIwf,GAAW,EACXlgB,EAAO,SAEL4E,EAAQ2V,cAAarJ,MAAOzP,IAChC,MAAM0e,EAAW,IAAIC,SACrB,IAAIC,EAAe,GAEnB,IACEA,EAAeC,KAAKC,UAAU9e,GAC9B,MAAM+e,GAAA,CAER,MAAMC,EAAoBtW,EAAQvF,EAAQkD,aAE1C,IAAK,MAAMzF,KAAOoe,EAChBN,EAASO,OAAOre,EAAKoe,EAAkBpe,IAazC,GAVIod,SACIA,EAAS,CACbhe,OACAf,QACAub,SACAkE,WACAE,iBAIArW,EACF,IACE,MAAM2W,EAAgC,CACpChB,GAAWA,EAAQ,gBACnBC,GACA9P,MAAM5P,GAAUA,GAASA,EAAM+L,SAAS,UAEpC2U,QAAiBC,MAAMC,OAAO9W,GAAS,CAC3CiS,SACA0D,QAAS,IACJA,KACCC,EAAU,CAAE,eAAgBA,GAAY,CAAA,GAE9CmB,KAAMJ,EAAgCN,EAAeF,IAIrDS,IACCb,GACIA,EAAea,EAASI,QACzBJ,EAASI,OAAS,KAAOJ,EAASI,QAAU,MAEhDd,GAAW,EACXL,GAAWA,EAAQ,CAAEe,aACrB5gB,EAAO8gB,OAAOF,EAASI,SAEvBlB,GAAaA,EAAU,CAAEc,aAE3B,MAAO/X,GACPqX,GAAW,EACXL,GAAWA,EAAQ,CAAEhX,aAtDrBjE,CAyDHlE,GAECwf,GAAYza,EAAMb,UACpBa,EAAMb,QAAQ2P,UAAUC,MAAMvJ,KAAK,CACjCiJ,oBAAoB,IAEtBzO,EAAMb,QAAQmU,SAAS,cAAe,CACpC/Y,WASN,OAJAqE,EAAMkB,WAAU,KACdia,GAAW,KACV,IAEIF,EACLjb,EAAA4c,cAAA5c,EAAA6c,SAAA,KACG5B,EAAO,CACNW,YAIJ5b,EAAA4c,cAAA,OAAA,CACEE,WAAY5B,EACZvV,OAAQA,EACRiS,OAAQA,EACR2D,QAASA,EACTH,SAAUQ,KACND,GAEHN,EAGP,iBV/DEja,IAEA,MAAMia,SAAEA,KAAaje,GAASgE,EAC9B,OACEpB,EAAA4c,cAAC7c,EAAgBgd,SAAQ,CAAClhB,MAAOuB,GAC9Bie,8F4DRD,SAOJja,GAOA,MAAMC,EAAUlB,KACVI,QACJA,EAAUc,EAAQd,QAAO7D,KACzBA,EAAIsgB,QACJA,EAAU,KAAIpZ,iBACdA,EAAgBM,MAChBA,GACE9C,GACG8H,EAAQ+T,GAAajd,EAAMyB,SAASlB,EAAQ6X,eAAe1b,IAC5DwgB,EAAMld,EAAM4B,OAChBrB,EAAQ6X,eAAe1b,GAAMuG,IAAI+V,KAE7BmE,EAAYnd,EAAM4B,OAAOsH,GACzBkU,EAAQpd,EAAM4B,OAAOlF,GACrB2gB,EAAYrd,EAAM4B,QAAO,GAE/Bwb,EAAM9a,QAAU5F,EAChBygB,EAAU7a,QAAU4G,EACpB3I,EAAQqC,OAAOkB,MAAMd,IAAItG,GAEzBwH,GACG3D,EAA2D0D,SAC1DvH,EACAwH,GAGJlD,GACE,IACET,EAAQ2P,UAAUpM,MAAMgD,UAAU,CAChCF,KAAM,EACJpD,SACA9G,KAAM4gB,MAKN,GAAIA,IAAmBF,EAAM9a,UAAYgb,EAAgB,CACvD,MAAM7G,EAAc5X,EAAI2E,EAAQ4Z,EAAM9a,SAClCpG,MAAMC,QAAQsa,KAChBwG,EAAUxG,GACVyG,EAAI5a,QAAUmU,EAAYxT,IAAI+V,SAInChS,aACL,CAACzG,IAGH,MAAMgd,EAAevd,EAAM0E,aAMvB8Y,IAEAH,EAAU/a,SAAU,EACpB/B,EAAQoX,eAAejb,EAAM8gB,KAE/B,CAACjd,EAAS7D,IAqRZ,OA5GAsD,EAAMkB,WAAU,KAQd,GAPAX,EAAQmF,OAAOC,QAAS,EAExB4F,GAAU7O,EAAM6D,EAAQqC,SACtBrC,EAAQ2P,UAAUC,MAAMvJ,KAAK,IACxBrG,EAAQmB,aAIb2b,EAAU/a,WACRyI,GAAmBxK,EAAQgF,SAASyF,MAAMC,YAC1C1K,EAAQmB,WAAWiO,eACpB5E,GAAmBxK,EAAQgF,SAAS+J,gBAAgBrE,WAErD,GAAI1K,EAAQgF,SAASgL,SACnBhQ,EAAQiQ,WAAW,CAAC9T,IAAO4b,MAAMrZ,IAC/B,MAAMuF,EAAQ3F,EAAII,EAAOmD,OAAQ1F,GAC3B+gB,EAAgB5e,EAAI0B,EAAQmB,WAAWU,OAAQ1F,IAGnD+gB,GACMjZ,GAASiZ,EAAc9hB,MACxB6I,IACEiZ,EAAc9hB,OAAS6I,EAAM7I,MAC5B8hB,EAAcrY,UAAYZ,EAAMY,SACpCZ,GAASA,EAAM7I,QAEnB6I,EACIpF,EAAImB,EAAQmB,WAAWU,OAAQ1F,EAAM8H,GACrCiE,GAAMlI,EAAQmB,WAAWU,OAAQ1F,GACrC6D,EAAQ2P,UAAUC,MAAMvJ,KAAK,CAC3BxE,OAAQ7B,EAAQmB,WAAWU,iBAI5B,CACL,MAAM0C,EAAejG,EAAI0B,EAAQwE,QAASrI,IAExCoI,IACAA,EAAME,IAEJ+F,GAAmBxK,EAAQgF,SAAS+J,gBAAgBrE,YACpDF,GAAmBxK,EAAQgF,SAASyF,MAAMC,YAG5C2B,GACE9H,EACAvE,EAAQqC,OAAOtB,SACff,EAAQkD,YACRlD,EAAQgF,SAAS8K,eAAiBxQ,EAClCU,EAAQgF,SAASwH,2BACjB,GACAuL,MACC9T,IACEsD,EAActD,IACfjE,EAAQ2P,UAAUC,MAAMvJ,KAAK,CAC3BxE,OAAQiK,GACN9L,EAAQmB,WAAWU,OACnBoC,EACA9H,OAQd6D,EAAQ2P,UAAUC,MAAMvJ,KAAK,CAC3BlK,OACA8G,OAAQrG,EAAYoD,EAAQkD,eAG9BlD,EAAQqC,OAAOqC,OACb2G,GAAsBrL,EAAQwE,SAAS,CAACH,EAAK5G,KAC3C,GACEuC,EAAQqC,OAAOqC,OACfjH,EAAI2N,WAAWpL,EAAQqC,OAAOqC,QAC9BL,EAAIK,MAGJ,OADAL,EAAIK,QACG,KAKb1E,EAAQqC,OAAOqC,MAAQ,GAEvB1E,EAAQiC,YACR6a,EAAU/a,SAAU,IACnB,CAAC4G,EAAQxM,EAAM6D,IAElBP,EAAMkB,WAAU,MACbrC,EAAI0B,EAAQkD,YAAa/G,IAAS6D,EAAQoX,eAAejb,GAEnD,KAQL6D,EAAQgF,SAAS3B,kBAAoBA,EACjCrD,EAAQqF,WAAWlJ,GARD,EAACA,EAAyBb,KAC9C,MAAMiJ,EAAejG,EAAI0B,EAAQwE,QAASrI,GACtCoI,GAASA,EAAME,KACjBF,EAAME,GAAGS,MAAQ5J,IAMjB2J,CAAc9I,GAAM,MAEzB,CAACA,EAAM6D,EAASyc,EAASpZ,IAErB,CACL8Z,KAAM1d,EAAM0E,aAlMD,CAACoW,EAAgBC,KAC5B,MAAMyC,EAA0Bjd,EAAQ6X,eAAe1b,GACvDme,GAAY2C,EAAyB1C,EAAQC,GAC7CF,GAAYqC,EAAI5a,QAASwY,EAAQC,GACjCwC,EAAaC,GACbP,EAAUO,GACVjd,EAAQoX,eACNjb,EACA8gB,EACA3C,GACA,CACE7C,KAAM8C,EACN7C,KAAM8C,IAER,KAoL4B,CAACwC,EAAc7gB,EAAM6D,IACnDod,KAAM3d,EAAM0E,aAjLD,CAACiM,EAAcuJ,KAC1B,MAAMsD,EAA0Bjd,EAAQ6X,eAAe1b,GACvDud,GAAYuD,EAAyB7M,EAAMuJ,GAC3CD,GAAYiD,EAAI5a,QAASqO,EAAMuJ,GAC/BqD,EAAaC,GACbP,EAAUO,GACVjd,EAAQoX,eACNjb,EACA8gB,EACAvD,GACA,CACEjC,KAAMrH,EACNsH,KAAMiC,IAER,KAmK4B,CAACqD,EAAc7gB,EAAM6D,IACnDqd,QAAS5d,EAAM0E,aA7PD,CACd7I,EAGA6N,KAEA,MAAMmU,EAAerX,EAAsBrJ,EAAYtB,IACjD2hB,EAA0BpD,GAC9B7Z,EAAQ6X,eAAe1b,GACvBmhB,GAEFtd,EAAQqC,OAAOqC,MAAQ0U,GAAkBjd,EAAM,EAAGgN,GAClDwT,EAAI5a,QAAU8X,GAAU8C,EAAI5a,QAASub,EAAa5a,IAAI+V,KACtDuE,EAAaC,GACbP,EAAUO,GACVjd,EAAQoX,eAAejb,EAAM8gB,EAAyBpD,GAAW,CAC/DpC,KAAM+B,GAAele,OA6Oa,CAAC0hB,EAAc7gB,EAAM6D,IACzD8b,OAAQrc,EAAM0E,aAtRD,CACb7I,EAGA6N,KAEA,MAAMoU,EAActX,EAAsBrJ,EAAYtB,IAChD2hB,EAA0B1D,GAC9BvZ,EAAQ6X,eAAe1b,GACvBohB,GAEFvd,EAAQqC,OAAOqC,MAAQ0U,GACrBjd,EACA8gB,EAAwBje,OAAS,EACjCmK,GAEFwT,EAAI5a,QAAUwX,GAASoD,EAAI5a,QAASwb,EAAY7a,IAAI+V,KACpDuE,EAAaC,GACbP,EAAUO,GACVjd,EAAQoX,eAAejb,EAAM8gB,EAAyB1D,GAAU,CAC9D9B,KAAM+B,GAAele,OAkQW,CAAC0hB,EAAc7gB,EAAM6D,IACvDwd,OAAQ/d,EAAM0E,aA3OArF,IACd,MAAMme,EAEAnD,GAAc9Z,EAAQ6X,eAAe1b,GAAO2C,GAClD6d,EAAI5a,QAAU+X,GAAc6C,EAAI5a,QAASjD,GACzCke,EAAaC,GACbP,EAAUO,IACTthB,MAAMC,QAAQ0C,EAAI0B,EAAQwE,QAASrI,KAClC0C,EAAImB,EAAQwE,QAASrI,OAAM2B,GAC7BkC,EAAQoX,eAAejb,EAAM8gB,EAAyBnD,GAAe,CACnErC,KAAM3Y,MAiO0B,CAACke,EAAc7gB,EAAM6D,IACvDyZ,OAAQha,EAAM0E,aA9ND,CACbrF,EACAxD,EAGA6N,KAEA,MAAMsU,EAAcxX,EAAsBrJ,EAAYtB,IAChD2hB,EAA0BS,GAC9B1d,EAAQ6X,eAAe1b,GACvB2C,EACA2e,GAEFzd,EAAQqC,OAAOqC,MAAQ0U,GAAkBjd,EAAM2C,EAAOqK,GACtDwT,EAAI5a,QAAU2b,GAASf,EAAI5a,QAASjD,EAAO2e,EAAY/a,IAAI+V,KAC3DuE,EAAaC,GACbP,EAAUO,GACVjd,EAAQoX,eAAejb,EAAM8gB,EAAyBS,GAAU,CAC9DjG,KAAM3Y,EACN4Y,KAAM8B,GAAele,OA2MW,CAAC0hB,EAAc7gB,EAAM6D,IACvD2d,OAAQle,EAAM0E,aApKD,CACbrF,EACAxD,KAEA,MAAMyH,EAAcnG,EAAYtB,GAC1B2hB,EAA0BxC,GAC9Bza,EAAQ6X,eAEN1b,GACF2C,EACAiE,GAEF4Z,EAAI5a,QAAU,IAAIkb,GAAyBva,KAAI,CAACkb,EAAM5D,IACnD4D,GAAQ5D,IAAMlb,EAAuB6d,EAAI5a,QAAQiY,GAA3BvB,OAEzBuE,EAAaC,GACbP,EAAU,IAAIO,IACdjd,EAAQoX,eACNjb,EACA8gB,EACAxC,GACA,CACEhD,KAAM3Y,EACN4Y,KAAM3U,IAER,GACA,KA0IgC,CAACia,EAAc7gB,EAAM6D,IACvD5B,QAASqB,EAAM0E,aAtIf7I,IAIA,MAAM2hB,EAA0BhX,EAAsBrJ,EAAYtB,IAClEqhB,EAAI5a,QAAUkb,EAAwBva,IAAI+V,IAC1CuE,EAAa,IAAIC,IACjBP,EAAU,IAAIO,IACdjd,EAAQoX,eACNjb,EACA,IAAI8gB,IACApgB,GAAeA,GACnB,IACA,GACA,KAwHkC,CAACmgB,EAAc7gB,EAAM6D,IACzD2I,OAAQlJ,EAAMyC,SACZ,IACEyG,EAAOjG,KAAI,CAAC6B,EAAOzF,KAAW,IACzByF,EACHkY,CAACA,GAAUE,EAAI5a,QAAQjD,IAAU2Z,UAErC,CAAC9P,EAAQ8T,IAGf,YCrZgB,SAKd5b,EAAkE,IAElE,MAAMgd,EAAepe,EAAM4B,YAEzBvD,GACIggB,EAAUre,EAAM4B,YAA4BvD,IAC3CiC,EAAWkB,GAAmBxB,EAAMyB,SAAkC,CAC3EI,SAAS,EACTK,cAAc,EACdJ,UAAWkG,EAAW5G,EAAMV,eAC5BiP,aAAa,EACbC,cAAc,EACdC,oBAAoB,EACpB1N,SAAS,EACTsN,YAAa,EACb1N,YAAa,CAAE,EACfC,cAAe,CAAE,EACjBC,iBAAkB,CAAE,EACpBG,OAAQhB,EAAMgB,QAAU,CAAE,EAC1Bd,SAAUF,EAAME,WAAY,EAC5BoO,SAAS,EACThP,cAAesH,EAAW5G,EAAMV,oBAC5BrC,EACA+C,EAAMV,gBAGZ,IAAK0d,EAAa9b,QAChB,GAAIlB,EAAM2X,YACRqF,EAAa9b,QAAU,IAClBlB,EAAM2X,YACTzY,aAGEc,EAAMV,gBAAkBsH,EAAW5G,EAAMV,gBAC3CU,EAAM2X,YAAYzB,MAAMlW,EAAMV,cAAeU,EAAMmX,kBAEhD,CACL,MAAMQ,YAAEA,KAAgB4C,GAASnM,GAAkBpO,GAEnDgd,EAAa9b,QAAU,IAClBqZ,EACHrb,aAKN,MAAMC,EAAU6d,EAAa9b,QAAQ/B,QAqFrC,OApFAA,EAAQgF,SAAWnE,EAEnBJ,GAA0B,KACxB,MAAMsd,EAAM/d,EAAQ8B,WAAW,CAC7B/B,UAAWC,EAAQQ,gBACnBwB,SAAU,IAAMf,EAAgB,IAAKjB,EAAQmB,aAC7CyT,cAAc,IAUhB,OAPA3T,GAAiBpE,IAAU,IACtBA,EACHsS,SAAS,MAGXnP,EAAQmB,WAAWgO,SAAU,EAEtB4O,IACN,CAAC/d,IAEJP,EAAMkB,WACJ,IAAMX,EAAQiY,aAAapX,EAAME,WACjC,CAACf,EAASa,EAAME,WAGlBtB,EAAMkB,WAAU,KACVE,EAAM4J,OACRzK,EAAQgF,SAASyF,KAAO5J,EAAM4J,MAE5B5J,EAAMkO,iBACR/O,EAAQgF,SAAS+J,eAAiBlO,EAAMkO,kBAEzC,CAAC/O,EAASa,EAAM4J,KAAM5J,EAAMkO,iBAE/BtP,EAAMkB,WAAU,KACVE,EAAMgB,SACR7B,EAAQ4X,WAAW/W,EAAMgB,QACzB7B,EAAQ0V,iBAET,CAAC1V,EAASa,EAAMgB,SAEnBpC,EAAMkB,WAAU,KACdE,EAAMwC,kBACJrD,EAAQ2P,UAAUC,MAAMvJ,KAAK,CAC3BpD,OAAQjD,EAAQgD,gBAEnB,CAAChD,EAASa,EAAMwC,mBAEnB5D,EAAMkB,WAAU,KACd,GAAIX,EAAQQ,gBAAgBc,QAAS,CACnC,MAAMA,EAAUtB,EAAQgR,YACpB1P,IAAYvB,EAAUuB,SACxBtB,EAAQ2P,UAAUC,MAAMvJ,KAAK,CAC3B/E,eAIL,CAACtB,EAASD,EAAUuB,UAEvB7B,EAAMkB,WAAU,KACVE,EAAMoC,SAAW2D,EAAU/F,EAAMoC,OAAQ6a,EAAQ/b,UACnD/B,EAAQoW,OAAOvV,EAAMoC,OAAQjD,EAAQgF,SAASgT,cAC9C8F,EAAQ/b,QAAUlB,EAAMoC,OACxBhC,GAAiB2O,IAAK,IAAWA,OAEjC5P,EAAQ8X,wBAET,CAAC9X,EAASa,EAAMoC,SAEnBxD,EAAMkB,WAAU,KACTX,EAAQmF,OAAOD,QAClBlF,EAAQiC,YACRjC,EAAQmF,OAAOD,OAAQ,GAGrBlF,EAAQmF,OAAO3C,QACjBxC,EAAQmF,OAAO3C,OAAQ,EACvBxC,EAAQ2P,UAAUC,MAAMvJ,KAAK,IAAKrG,EAAQmB,cAG5CnB,EAAQmD,sBAGV0a,EAAa9b,QAAQhC,UAAYD,EAAkBC,EAAWC,GAEvD6d,EAAa9b,OACtB"}