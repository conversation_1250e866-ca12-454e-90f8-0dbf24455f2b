{"version": 3, "file": "standard-schema.js", "sources": ["../src/standard-schema.ts"], "sourcesContent": ["import { toNestErrors, validateFieldsNatively } from '@hookform/resolvers';\nimport { StandardSchemaV1 } from '@standard-schema/spec';\nimport { getDotPath } from '@standard-schema/utils';\nimport { FieldError, FieldValues, Resolver } from 'react-hook-form';\n\nfunction parseErrorSchema(\n  issues: readonly StandardSchemaV1.Issue[],\n  validateAllFieldCriteria: boolean,\n) {\n  const errors: Record<string, FieldError> = {};\n\n  for (let i = 0; i < issues.length; i++) {\n    const error = issues[i];\n    const path = getDotPath(error);\n\n    if (path) {\n      if (!errors[path]) {\n        errors[path] = { message: error.message, type: '' };\n      }\n\n      if (validateAllFieldCriteria) {\n        const types = errors[path].types || {};\n\n        errors[path].types = {\n          ...types,\n          [Object.keys(types).length]: error.message,\n        };\n      }\n    }\n  }\n\n  return errors;\n}\n\nexport function standardSchemaResolver<\n  Input extends FieldValues,\n  Context,\n  Output,\n>(\n  schema: StandardSchemaV1<Input, Output>,\n  _schemaOptions?: never,\n  resolverOptions?: {\n    raw?: false;\n  },\n): Resolver<Input, Context, Output>;\n\nexport function standardSchemaResolver<\n  Input extends FieldValues,\n  Context,\n  Output,\n>(\n  schema: StandardSchemaV1<Input, Output>,\n  _schemaOptions: never | undefined,\n  resolverOptions: {\n    raw: true;\n  },\n): Resolver<Input, Context, Input>;\n\n/**\n * Creates a resolver for react-hook-form that validates data using a Standard Schema.\n *\n * @param {Schema} schema - The Standard Schema to validate against\n * @param {Object} resolverOptions - Options for the resolver\n * @param {boolean} [resolverOptions.raw=false] - Whether to return raw input values instead of parsed values\n * @returns {Resolver} A resolver function compatible with react-hook-form\n *\n * @example\n * ```ts\n * const schema = z.object({\n *   name: z.string().min(2),\n *   age: z.number().min(18)\n * });\n *\n * useForm({\n *   resolver: standardSchemaResolver(schema)\n * });\n * ```\n */\nexport function standardSchemaResolver<\n  Input extends FieldValues,\n  Context,\n  Output,\n>(\n  schema: StandardSchemaV1<Input, Output>,\n  _schemaOptions?: never,\n  resolverOptions: {\n    raw?: boolean;\n  } = {},\n): Resolver<Input, Context, Output | Input> {\n  return async (values, _, options) => {\n    let result = schema['~standard'].validate(values);\n    if (result instanceof Promise) {\n      result = await result;\n    }\n\n    if (result.issues) {\n      const errors = parseErrorSchema(\n        result.issues,\n        !options.shouldUseNativeValidation && options.criteriaMode === 'all',\n      );\n\n      return {\n        values: {},\n        errors: toNestErrors(errors, options),\n      };\n    }\n\n    options.shouldUseNativeValidation && validateFieldsNatively({}, options);\n\n    return {\n      values: resolverOptions.raw ? Object.assign({}, values) : result.value,\n      errors: {},\n    };\n  };\n}\n"], "names": ["schema", "_schemaOptions", "resolverOptions", "values", "_", "options", "_temp2", "result", "issues", "errors", "validateAllFieldCriteria", "i", "length", "error", "path", "getDotPath", "message", "type", "_extends2", "types", "_extends", "Object", "keys", "parseErrorSchema", "shouldUseNativeValidation", "criteriaMode", "toNestErrors", "validateFieldsNatively", "raw", "assign", "value", "validate", "_temp", "Promise", "resolve", "then", "_result", "e", "reject"], "mappings": "gUA8EM,SAKJA,EACAC,EACAC,GAIA,gBAJAA,IAAAA,EAEI,CAAE,GAEQC,SAAAA,EAAQC,EAAGC,GAAO,IAAIC,IAAAA,aAMlC,GAAIC,EAAOC,OAAQ,CACjB,IAAMC,EA3FZ,SACED,EACAE,GAIA,IAFA,IAAMD,EAAqC,GAElCE,EAAI,EAAGA,EAAIH,EAAOI,OAAQD,IAAK,CACtC,IAAME,EAAQL,EAAOG,GACfG,EAAOC,EAAAA,WAAWF,GAExB,GAAIC,IACGL,EAAOK,KACVL,EAAOK,GAAQ,CAAEE,QAASH,EAAMG,QAASC,KAAM,KAG7CP,GAA0B,CAAAQ,IAAAA,EACtBC,EAAQV,EAAOK,GAAMK,OAAS,GAEpCV,EAAOK,GAAMK,MAAKC,KACbD,IAAKD,EAAA,CAAA,GACPG,OAAOC,KAAKH,GAAOP,QAASC,EAAMG,QAAOE,GAE9C,CAEJ,CAEA,OAAOT,CACT,CAgEqBc,CACbhB,EAAOC,QACNH,EAAQmB,2BAAsD,QAAzBnB,EAAQoB,cAGhD,MAAO,CACLtB,OAAQ,CAAA,EACRM,OAAQiB,eAAajB,EAAQJ,GAEjC,CAIA,OAFAA,EAAQmB,2BAA6BG,EAAAA,uBAAuB,CAAA,EAAItB,GAEzD,CACLF,OAAQD,EAAgB0B,IAAMP,OAAOQ,OAAO,CAAA,EAAI1B,GAAUI,EAAOuB,MACjErB,OAAQ,CAAA,EACR,EAtBEF,EAASP,EAAO,aAAa+B,SAAS5B,GAAQ6B,gBAC9CzB,aAAkB0B,QAAO,OAAAA,QAAAC,QACZ3B,GAAM4B,KAAA,SAAAC,GAArB7B,EAAM6B,CAAgB,EAAAH,IAAAA,OAAAA,QAAAC,QAAAF,GAAAA,EAAAG,KAAAH,EAAAG,KAAA7B,GAAAA,IAqB1B,CAAC,MAAA+B,GAAAJ,OAAAA,QAAAK,OAAAD,EACH,CAAA,CAAA"}