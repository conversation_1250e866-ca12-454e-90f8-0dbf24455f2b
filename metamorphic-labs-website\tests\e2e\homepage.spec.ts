import { test, expect } from '@playwright/test';

test.describe('Homepage', () => {
  test('should load and display main content', async ({ page }) => {
    await page.goto('/');

    // Check if the page loads
    await expect(page).toHaveTitle(/Metamorphic Labs/);

    // Check for main heading
    await expect(page.getByText('Redefining Reality with')).toBeVisible();

    // Check for navigation
    await expect(page.getByRole('navigation')).toBeVisible();
    await expect(page.getByText('Home')).toBeVisible();
    await expect(page.getByText('About')).toBeVisible();
    await expect(page.getByText('Systems')).toBeVisible();
    await expect(page.getByText('Contact')).toBeVisible();

    // Check for CTA buttons
    await expect(page.getByText('Explore Catalyst')).toBeVisible();
    await expect(page.getByText('Visit Vault 024')).toBeVisible();
  });

  test('should navigate to about page', async ({ page }) => {
    await page.goto('/');
    
    await page.getByText('About').click();
    await expect(page).toHaveURL('/about');
    await expect(page.getByText('About Metamorphic Labs')).toBeVisible();
  });

  test('should navigate to systems page', async ({ page }) => {
    await page.goto('/');
    
    await page.getByText('Systems').click();
    await expect(page).toHaveURL('/systems');
    await expect(page.getByText('Systems & Environments')).toBeVisible();
  });

  test('should navigate to contact page', async ({ page }) => {
    await page.goto('/');
    
    await page.getByText('Contact').click();
    await expect(page).toHaveURL('/contact');
    await expect(page.getByText('Get in Touch')).toBeVisible();
  });

  test('should be responsive on mobile', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 });
    await page.goto('/');

    // Check if mobile menu button is visible
    await expect(page.getByRole('button', { name: 'Open main menu' })).toBeVisible();
    
    // Check if main content is still visible
    await expect(page.getByText('Redefining Reality with')).toBeVisible();
  });
});
