'use client';

import { motion } from 'framer-motion';
import { useInView } from 'framer-motion';
import { useRef } from 'react';

export function IntroSection() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  return (
    <section ref={ref} className="py-24 sm:py-32">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
          transition={{ duration: 0.8 }}
          className="mx-auto max-w-4xl text-center"
        >
          <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl lg:text-5xl mb-8">
            Pioneering the <span className="gradient-text">Future</span> of Intelligence
          </h2>
          
          <div className="text-lg leading-8 text-gray-300 space-y-6">
            <p>
              Metamorphic Labs pioneers next-generation AI systems, multi-agent orchestration, 
              generative creativity, and boundary-pushing software solutions. From deep prompt 
              ecosystems to decentralized art galleries—we build the future.
            </p>
            
            <p>
              Our cutting-edge platforms combine artificial intelligence, quantum computing principles, 
              and innovative software architecture to create solutions that were previously impossible. 
              We&apos;re not just building tools; we&apos;re crafting the foundation for tomorrow&apos;s 
              digital ecosystem.
            </p>
          </div>

          {/* Stats */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="mt-16 grid grid-cols-1 gap-8 sm:grid-cols-3"
          >
            <div className="text-center">
              <div className="text-3xl font-bold gradient-text mb-2">3</div>
              <div className="text-sm text-gray-400">Active Systems</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold gradient-text mb-2">∞</div>
              <div className="text-sm text-gray-400">Possibilities</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold gradient-text mb-2">24/7</div>
              <div className="text-sm text-gray-400">Innovation</div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}
