(()=>{var e={};e.id=974,e.ids=[974],e.modules={334:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(2688).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},508:(e,t,a)=>{"use strict";a.d(t,{IntroSection:()=>r});let r=(0,a(2907).registerClientReference)(function(){throw Error("Attempted to call IntroSection() from the server but IntroSection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\intro-section.tsx","IntroSection")},528:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>p,metadata:()=>m});var r=a(7413),s=a(8300),i=a(508),n=a(5882),o=a(8059);function l({data:e}){return(0,r.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify(e)}})}let c={"@context":"https://schema.org","@type":"Organization",name:"Metamorphic Labs",description:"Metamorphic Labs pioneers next-generation AI systems, multi-agent orchestration, generative creativity, and boundary-pushing software solutions.",url:"https://metamorphiclabs.ai",logo:"https://metamorphiclabs.ai/logo.png",foundingDate:"2024",industry:"Artificial Intelligence",address:{"@type":"PostalAddress",addressCountry:"US"},contactPoint:{"@type":"ContactPoint",email:"<EMAIL>",contactType:"customer service"},sameAs:["https://catalyst.metamorphiclabs.ai","https://vault024.metamorphiclabs.ai"],hasOfferCatalog:{"@type":"OfferCatalog",name:"AI Systems and Services",itemListElement:[{"@type":"Offer",itemOffered:{"@type":"Service",name:"Catalyst",description:"Advanced prompt engineering platform for optimizing AI interactions"}},{"@type":"Offer",itemOffered:{"@type":"Service",name:"Metamorphic Reactor",description:"Multi-agent orchestration system for complex AI workflows"}},{"@type":"Offer",itemOffered:{"@type":"Service",name:"Vault 024",description:"Decentralized art gallery and NFT platform"}}]}},d={"@context":"https://schema.org","@type":"WebSite",name:"Metamorphic Labs",url:"https://metamorphiclabs.ai",description:"Redefining Reality with AI, Quantum Systems & Intelligent Software",publisher:{"@type":"Organization",name:"Metamorphic Labs"},potentialAction:{"@type":"SearchAction",target:"https://metamorphiclabs.ai/search?q={search_term_string}","query-input":"required name=search_term_string"}},m={title:"Metamorphic Labs | Redefining Reality with AI & Quantum Systems",description:"Metamorphic Labs pioneers next-generation AI systems, multi-agent orchestration, generative creativity, and boundary-pushing software solutions. From deep prompt ecosystems to decentralized art galleries—we build the future."};function p(){return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(l,{data:c}),(0,r.jsx)(l,{data:d}),(0,r.jsxs)("div",{className:"bg-black text-white",children:[(0,r.jsx)(s.HeroSection,{}),(0,r.jsx)(i.IntroSection,{}),(0,r.jsx)(n.FeaturesGrid,{}),(0,r.jsx)(o.ProjectsPreview,{})]})]})}},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1110:(e,t,a)=>{"use strict";a.d(t,{HeroSection:()=>p});var r=a(687),s=a(5814),i=a.n(s),n=a(7786),o=a(9523),l=a(2733),c=a(6085),d=a(334),m=a(5583);function p(){return(0,r.jsxs)("section",{className:"relative min-h-screen flex items-center justify-center overflow-hidden",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-black via-gray-900 to-black"}),(0,r.jsxs)("div",{className:"absolute inset-0",children:[(0,r.jsx)("div",{className:"absolute top-1/4 left-1/4 w-64 h-64 bg-blue-500/10 rounded-full blur-3xl animate-pulse"}),(0,r.jsx)("div",{className:"absolute bottom-1/4 right-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-pulse delay-1000"})]}),(0,r.jsx)("div",{className:"relative z-10 mx-auto max-w-7xl px-6 py-24 sm:py-32 lg:px-8",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsx)(l.MetamorphicLogo,{className:"h-16 w-auto mx-auto mb-6",animated:!0})}),(0,r.jsxs)(n.P.h1,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.2},className:"text-4xl font-bold tracking-tight text-white sm:text-6xl lg:text-7xl",children:["Redefining Reality with"," ",(0,r.jsx)("span",{className:"gradient-text",children:"AI"}),","," ",(0,r.jsx)("span",{className:"gradient-text",children:"Quantum Systems"})," &"," ",(0,r.jsx)("span",{className:"gradient-text",children:"Intelligent Software"})]}),(0,r.jsx)(n.P.p,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.4},className:"mt-6 text-lg leading-8 text-gray-300 max-w-3xl mx-auto",children:"Metamorphic Labs pioneers next-generation AI systems, multi-agent orchestration, generative creativity, and boundary-pushing software solutions. From deep prompt ecosystems to decentralized art galleries—we build the future."}),(0,r.jsxs)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.6},className:"mt-10 flex flex-col sm:flex-row items-center justify-center gap-4",children:[(0,r.jsx)(o.$,{asChild:!0,variant:"gradient",size:"lg",className:"holographic-hover group",children:(0,r.jsxs)(i(),{href:"https://catalyst.metamorphiclabs.ai",target:"_blank",rel:"noopener noreferrer",className:"flex items-center gap-2",children:[(0,r.jsx)(c.A,{className:"h-5 w-5"}),"Explore Catalyst",(0,r.jsx)(d.A,{className:"h-4 w-4 transition-transform group-hover:translate-x-1"})]})}),(0,r.jsx)(o.$,{asChild:!0,variant:"outline",size:"lg",className:"border-gray-600 text-white hover:bg-gray-800 holographic-hover group",children:(0,r.jsxs)(i(),{href:"#metamorphic-reactor",className:"flex items-center gap-2",children:[(0,r.jsx)(m.A,{className:"h-5 w-5"}),"Learn About Metamorphic Reactor",(0,r.jsx)(d.A,{className:"h-4 w-4 transition-transform group-hover:translate-x-1"})]})}),(0,r.jsx)(o.$,{asChild:!0,className:"bg-black border border-yellow-400 text-yellow-400 hover:bg-yellow-400 hover:text-black holographic-hover group",size:"lg",children:(0,r.jsxs)(i(),{href:"https://vault024.metamorphiclabs.ai",target:"_blank",rel:"noopener noreferrer",className:"flex items-center gap-2",children:[(0,r.jsx)("span",{className:"text-xs",children:"✦"}),"Visit Vault 024",(0,r.jsx)(d.A,{className:"h-4 w-4 transition-transform group-hover:translate-x-1"})]})})]}),(0,r.jsx)(n.P.div,{initial:{opacity:0},animate:{opacity:1},transition:{duration:1,delay:1},className:"mt-16",children:(0,r.jsxs)("div",{className:"flex flex-col items-center",children:[(0,r.jsx)("p",{className:"text-sm text-gray-400 mb-4",children:"Discover our ecosystem"}),(0,r.jsx)(n.P.div,{animate:{y:[0,8,0]},transition:{duration:2,repeat:1/0},className:"w-6 h-10 border-2 border-gray-600 rounded-full flex justify-center",children:(0,r.jsx)("div",{className:"w-1 h-3 bg-gradient-to-b from-blue-400 to-purple-400 rounded-full mt-2"})})]})})]})})]})}},1342:(e,t,a)=>{"use strict";a.d(t,{IntroSection:()=>o});var r=a(687),s=a(7786),i=a(8265),n=a(3210);function o(){let e=(0,n.useRef)(null),t=(0,i.W)(e,{once:!0,margin:"-100px"});return(0,r.jsx)("section",{ref:e,className:"py-24 sm:py-32",children:(0,r.jsx)("div",{className:"mx-auto max-w-7xl px-6 lg:px-8",children:(0,r.jsxs)(s.P.div,{initial:{opacity:0,y:20},animate:t?{opacity:1,y:0}:{opacity:0,y:20},transition:{duration:.8},className:"mx-auto max-w-4xl text-center",children:[(0,r.jsxs)("h2",{className:"text-3xl font-bold tracking-tight text-white sm:text-4xl lg:text-5xl mb-8",children:["Pioneering the ",(0,r.jsx)("span",{className:"gradient-text",children:"Future"})," of Intelligence"]}),(0,r.jsxs)("div",{className:"text-lg leading-8 text-gray-300 space-y-6",children:[(0,r.jsx)("p",{children:"Metamorphic Labs pioneers next-generation AI systems, multi-agent orchestration, generative creativity, and boundary-pushing software solutions. From deep prompt ecosystems to decentralized art galleries—we build the future."}),(0,r.jsx)("p",{children:"Our cutting-edge platforms combine artificial intelligence, quantum computing principles, and innovative software architecture to create solutions that were previously impossible. We're not just building tools; we're crafting the foundation for tomorrow's digital ecosystem."})]}),(0,r.jsxs)(s.P.div,{initial:{opacity:0,y:20},animate:t?{opacity:1,y:0}:{opacity:0,y:20},transition:{duration:.8,delay:.2},className:"mt-16 grid grid-cols-1 gap-8 sm:grid-cols-3",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-3xl font-bold gradient-text mb-2",children:"3"}),(0,r.jsx)("div",{className:"text-sm text-gray-400",children:"Active Systems"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-3xl font-bold gradient-text mb-2",children:"∞"}),(0,r.jsx)("div",{className:"text-sm text-gray-400",children:"Possibilities"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-3xl font-bold gradient-text mb-2",children:"24/7"}),(0,r.jsx)("div",{className:"text-sm text-gray-400",children:"Innovation"})]})]})]})})})}},2274:(e,t,a)=>{Promise.resolve().then(a.bind(a,3056)),Promise.resolve().then(a.bind(a,1110)),Promise.resolve().then(a.bind(a,1342)),Promise.resolve().then(a.bind(a,7162))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3056:(e,t,a)=>{"use strict";a.d(t,{FeaturesGrid:()=>h});var r=a(687),s=a(7786),i=a(8265),n=a(3210),o=a(2688);let l=(0,o.A)("brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]]),c=(0,o.A)("network",[["rect",{x:"16",y:"16",width:"6",height:"6",rx:"1",key:"4q2zg0"}],["rect",{x:"2",y:"16",width:"6",height:"6",rx:"1",key:"8cvhb9"}],["rect",{x:"9",y:"2",width:"6",height:"6",rx:"1",key:"1egb70"}],["path",{d:"M5 16v-3a1 1 0 0 1 1-1h12a1 1 0 0 1 1 1v3",key:"1jsf9p"}],["path",{d:"M12 12V8",key:"2874zd"}]]);var d=a(8971),m=a(5583);let p=[{name:"Metamorphic AI Ecosystem",description:"Comprehensive AI platform integrating multiple models and agents for complex problem-solving and creative tasks.",icon:l},{name:"Multi-AI Orchestration",description:"Advanced orchestration system enabling seamless collaboration between different AI models and agents.",icon:c},{name:"Generative Art + NFTs",description:"Cutting-edge platform for creating, showcasing, and trading AI-generated art and digital collectibles.",icon:d.A},{name:"Prompt Engineering",description:"Sophisticated tools and methodologies for optimizing AI interactions and developing prompt ecosystems.",icon:m.A}];function h(){let e=(0,n.useRef)(null),t=(0,i.W)(e,{once:!0,margin:"-100px"});return(0,r.jsx)("section",{ref:e,className:"py-24 sm:py-32 bg-gray-900/20",children:(0,r.jsxs)("div",{className:"mx-auto max-w-7xl px-6 lg:px-8",children:[(0,r.jsxs)(s.P.div,{initial:{opacity:0,y:20},animate:t?{opacity:1,y:0}:{opacity:0,y:20},transition:{duration:.8},className:"mx-auto max-w-2xl text-center mb-16",children:[(0,r.jsxs)("h2",{className:"text-3xl font-bold tracking-tight text-white sm:text-4xl lg:text-5xl",children:["Our ",(0,r.jsx)("span",{className:"gradient-text",children:"Core Capabilities"})]}),(0,r.jsx)("p",{className:"mt-6 text-lg leading-8 text-gray-300",children:"Discover the technologies and methodologies that power our innovative solutions"})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4",children:p.map((e,a)=>(0,r.jsx)(s.P.div,{initial:{opacity:0,y:20},animate:t?{opacity:1,y:0}:{opacity:0,y:20},transition:{duration:.8,delay:.1*a},className:"group relative",children:(0,r.jsxs)("div",{className:"holographic-hover rounded-2xl bg-gray-900 p-8 border border-gray-800 h-full",children:[(0,r.jsx)("div",{className:"flex items-center justify-center w-12 h-12 rounded-lg bg-gradient-to-br from-blue-500 to-purple-600 mb-6",children:(0,r.jsx)(e.icon,{className:"h-6 w-6 text-white"})}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-white mb-4 group-hover:gradient-text transition-all duration-300",children:e.name}),(0,r.jsx)("p",{className:"text-gray-300 leading-relaxed",children:e.description}),(0,r.jsx)("div",{className:"absolute inset-0 rounded-2xl bg-gradient-to-br from-blue-500/5 to-purple-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"})]})},e.name))}),(0,r.jsx)(s.P.div,{initial:{opacity:0,y:20},animate:t?{opacity:1,y:0}:{opacity:0,y:20},transition:{duration:.8,delay:.6},className:"mt-16 text-center",children:(0,r.jsxs)("div",{className:"rounded-2xl bg-gradient-to-r from-blue-900/20 to-purple-900/20 p-8 border border-gray-800",children:[(0,r.jsx)("h3",{className:"text-2xl font-bold text-white mb-4",children:"Integrated Innovation"}),(0,r.jsx)("p",{className:"text-gray-300 max-w-3xl mx-auto",children:"Our features work together seamlessly, creating a powerful ecosystem where AI orchestration, prompt engineering, and creative generation combine to deliver unprecedented capabilities. Each component enhances the others, resulting in solutions that are greater than the sum of their parts."})]})})]})})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4493:(e,t,a)=>{"use strict";a.d(t,{Wu:()=>n,Zp:()=>i});var r=a(687);a(3210);var s=a(4780);function i({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function n({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",e),...t})}},5583:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(2688).A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},5882:(e,t,a)=>{"use strict";a.d(t,{FeaturesGrid:()=>r});let r=(0,a(2907).registerClientReference)(function(){throw Error("Attempted to call FeaturesGrid() from the server but FeaturesGrid is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\features-grid.tsx","FeaturesGrid")},6085:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(2688).A)("sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]])},6093:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>p,tree:()=>c});var r=a(5239),s=a(8088),i=a(8170),n=a.n(i),o=a(893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);a.d(t,l);let c={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,528)),"C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\app\\page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,1075)),"C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,4413)),"C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\app\\page.tsx"],m={require:a,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},6741:(e,t,a)=>{Promise.resolve().then(a.bind(a,5882)),Promise.resolve().then(a.bind(a,8300)),Promise.resolve().then(a.bind(a,508)),Promise.resolve().then(a.bind(a,8059))},7162:(e,t,a)=>{"use strict";a.d(t,{ProjectsPreview:()=>y});var r=a(687),s=a(5814),i=a.n(s),n=a(7786),o=a(8265),l=a(3210),c=a(9523),d=a(4493),m=a(6085),p=a(5583),h=a(8971);let x=(0,a(2688).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]);var u=a(334);let g=[{name:"Catalyst",description:"Advanced prompt engineering platform for optimizing AI interactions and developing sophisticated prompt ecosystems.",status:"Active",url:"https://catalyst.metamorphiclabs.ai",icon:m.A,gradient:!0,features:["Prompt Optimization","AI Model Integration","Performance Analytics"]},{name:"Metamorphic Reactor",description:"Multi-agent orchestration system enabling complex AI workflows and intelligent automation across diverse domains.",status:"Development",url:"#metamorphic-reactor",icon:p.A,gradient:!0,features:["Multi-Agent Coordination","Workflow Automation","Intelligent Routing"]},{name:"Vault 024",description:"Decentralized art gallery and NFT platform showcasing generative creativity and blockchain-based digital assets.",status:"Active",url:"https://vault024.metamorphiclabs.ai",icon:h.A,vault:!0,features:["Generative Art","NFT Marketplace","Blockchain Integration"]}];function y(){let e=(0,l.useRef)(null),t=(0,o.W)(e,{once:!0,margin:"-100px"});return(0,r.jsx)("section",{ref:e,className:"py-24 sm:py-32",children:(0,r.jsxs)("div",{className:"mx-auto max-w-7xl px-6 lg:px-8",children:[(0,r.jsxs)(n.P.div,{initial:{opacity:0,y:20},animate:t?{opacity:1,y:0}:{opacity:0,y:20},transition:{duration:.8},className:"mx-auto max-w-2xl text-center mb-16",children:[(0,r.jsxs)("h2",{className:"text-3xl font-bold tracking-tight text-white sm:text-4xl lg:text-5xl",children:["Our ",(0,r.jsx)("span",{className:"gradient-text",children:"Systems"})," in Action"]}),(0,r.jsx)("p",{className:"mt-6 text-lg leading-8 text-gray-300",children:"Explore our cutting-edge platforms that are reshaping the landscape of AI and creative technology"})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 gap-8 lg:grid-cols-3",children:g.map((e,a)=>(0,r.jsx)(n.P.div,{initial:{opacity:0,y:20},animate:t?{opacity:1,y:0}:{opacity:0,y:20},transition:{duration:.8,delay:.2*a},children:(0,r.jsx)(d.Zp,{className:`holographic-hover h-full ${e.vault?"vault-card":"bg-gray-900 border-gray-800"}`,children:(0,r.jsxs)(d.Wu,{className:"p-8 h-full flex flex-col",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsx)("div",{className:`p-3 rounded-lg ${e.vault?"bg-black border border-yellow-400":"bg-gray-800"}`,children:(0,r.jsx)(e.icon,{className:`h-6 w-6 ${e.vault?"text-yellow-400":e.gradient?"text-blue-400":"text-white"}`})}),(0,r.jsx)("span",{className:`px-3 py-1 rounded-full text-xs font-medium ${"Active"===e.status?e.vault?"bg-yellow-400 text-black":"bg-green-500 text-white":"bg-gray-600 text-gray-300"}`,children:e.status})]}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h3",{className:`text-2xl font-bold mb-4 ${e.vault?"text-yellow-400":"text-white"}`,children:e.name}),(0,r.jsx)("p",{className:`text-base leading-relaxed mb-6 ${e.vault?"text-yellow-400/90":"text-gray-300"}`,children:e.description}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("h4",{className:`text-sm font-semibold mb-3 ${e.vault?"text-yellow-400":"text-white"}`,children:"Key Features"}),(0,r.jsx)("ul",{className:"space-y-2",children:e.features.map(t=>(0,r.jsxs)("li",{className:`text-sm flex items-center gap-2 ${e.vault?"text-yellow-400/80":"text-gray-400"}`,children:[(0,r.jsx)("div",{className:`w-1.5 h-1.5 rounded-full ${e.vault?"bg-yellow-400":"bg-blue-400"}`}),t]},t))})]})]}),(0,r.jsx)(c.$,{asChild:!0,className:`w-full font-semibold group ${e.vault?"bg-yellow-400 text-black hover:bg-yellow-300":e.gradient?"gradient-metamorphic text-white":"bg-blue-600 hover:bg-blue-700 text-white"}`,children:(0,r.jsxs)(i(),{href:e.url,target:e.url.startsWith("http")?"_blank":void 0,rel:e.url.startsWith("http")?"noopener noreferrer":void 0,className:"flex items-center justify-center gap-2",children:["Active"===e.status?"Access System":"Learn More",e.url.startsWith("http")?(0,r.jsx)(x,{className:"h-4 w-4 transition-transform group-hover:translate-x-1"}):(0,r.jsx)(u.A,{className:"h-4 w-4 transition-transform group-hover:translate-x-1"})]})})]})})},e.name))}),(0,r.jsx)(n.P.div,{initial:{opacity:0,y:20},animate:t?{opacity:1,y:0}:{opacity:0,y:20},transition:{duration:.8,delay:.8},className:"mt-16 text-center",children:(0,r.jsx)(c.$,{asChild:!0,variant:"outline",size:"lg",className:"border-gray-600 text-white hover:bg-gray-800",children:(0,r.jsxs)(i(),{href:"/systems",className:"flex items-center gap-2",children:["View All Systems",(0,r.jsx)(u.A,{className:"h-4 w-4"})]})})})]})})}},8059:(e,t,a)=>{"use strict";a.d(t,{ProjectsPreview:()=>r});let r=(0,a(2907).registerClientReference)(function(){throw Error("Attempted to call ProjectsPreview() from the server but ProjectsPreview is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\projects-preview.tsx","ProjectsPreview")},8265:(e,t,a)=>{"use strict";a.d(t,{W:()=>n});var r=a(3210),s=a(9292);let i={some:0,all:1};function n(e,{root:t,margin:a,amount:o,once:l=!1,initial:c=!1}={}){let[d,m]=(0,r.useState)(c);return(0,r.useEffect)(()=>{if(!e.current||l&&d)return;let r={root:t&&t.current||void 0,margin:a,amount:o};return function(e,t,{root:a,margin:r,amount:n="some"}={}){let o=(0,s.K)(e),l=new WeakMap,c=new IntersectionObserver(e=>{e.forEach(e=>{let a=l.get(e.target);if(!!a!==e.isIntersecting)if(e.isIntersecting){let a=t(e.target,e);"function"==typeof a?l.set(e.target,a):c.unobserve(e.target)}else"function"==typeof a&&(a(e),l.delete(e.target))})},{root:a,rootMargin:r,threshold:"number"==typeof n?n:i[n]});return o.forEach(e=>c.observe(e)),()=>c.disconnect()}(e.current,()=>(m(!0),l?void 0:()=>m(!1)),r)},[t,e,a,l,o]),d}},8300:(e,t,a)=>{"use strict";a.d(t,{HeroSection:()=>r});let r=(0,a(2907).registerClientReference)(function(){throw Error("Attempted to call HeroSection() from the server but HeroSection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\hero-section.tsx","HeroSection")},8971:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(2688).A)("palette",[["path",{d:"M12 22a1 1 0 0 1 0-20 10 9 0 0 1 10 9 5 5 0 0 1-5 5h-2.25a1.75 1.75 0 0 0-1.4 2.8l.3.4a1.75 1.75 0 0 1-1.4 2.8z",key:"e79jfc"}],["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}]])},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")}};var t=require("../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[447,559,84],()=>a(6093));module.exports=r})();