import { render, screen } from '@testing-library/react';
import { MetamorphicLogo } from '@/components/metamorphic-logo';

describe('MetamorphicLogo', () => {
  it('renders the logo with company name', () => {
    render(<MetamorphicLogo />);
    
    expect(screen.getByText('METAMORPHIC')).toBeInTheDocument();
    expect(screen.getByText('LABS')).toBeInTheDocument();
  });

  it('applies custom className', () => {
    const { container } = render(<MetamorphicLogo className="custom-class" />);
    const svg = container.querySelector('svg');
    
    expect(svg).toHaveClass('custom-class');
  });

  it('renders without animation by default', () => {
    const { container } = render(<MetamorphicLogo />);
    const svg = container.querySelector('svg');
    
    expect(svg).toBeInTheDocument();
  });

  it('renders with animation when animated prop is true', () => {
    const { container } = render(<MetamorphicLogo animated />);
    const svg = container.querySelector('svg');
    
    expect(svg).toBeInTheDocument();
  });
});
