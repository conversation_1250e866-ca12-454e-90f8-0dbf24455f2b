(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[345],{285:(e,t,a)=>{"use strict";a.d(t,{$:()=>d});var r=a(5155);a(2115);var i=a(4624),s=a(2085),n=a(9434);let o=(0,s.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline",gradient:"gradient-metamorphic text-white shadow-xs hover:opacity-90 transition-opacity"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:t,variant:a,size:s,asChild:d=!1,...c}=e,l=d?i.DX:"button";return(0,r.jsx)(l,{"data-slot":"button",className:(0,n.cn)(o({variant:a,size:s,className:t})),...c})}},1906:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>h});var r=a(5155),i=a(6874),s=a.n(i),n=a(230),o=a(285),d=a(9946);let c=(0,d.A)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]]),l=(0,d.A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);function h(){return(0,r.jsx)("div",{className:"bg-black text-white min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center px-6",children:[(0,r.jsxs)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:[(0,r.jsx)("h1",{className:"text-9xl font-bold gradient-text mb-4",children:"404"}),(0,r.jsx)("h2",{className:"text-3xl font-bold text-white mb-4",children:"Page Not Found"}),(0,r.jsx)("p",{className:"text-lg text-gray-300 mb-8 max-w-md mx-auto",children:"The page you're looking for seems to have vanished into the quantum realm. Let's get you back to familiar territory."}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,r.jsx)(o.$,{asChild:!0,className:"gradient-metamorphic",children:(0,r.jsxs)(s(),{href:"/",className:"flex items-center gap-2",children:[(0,r.jsx)(c,{className:"h-4 w-4"}),"Back to Home"]})}),(0,r.jsx)(o.$,{asChild:!0,variant:"outline",className:"border-gray-600 text-white hover:bg-gray-800",children:(0,r.jsxs)(s(),{href:"/contact",className:"flex items-center gap-2",children:[(0,r.jsx)(l,{className:"h-4 w-4"}),"Contact Support"]})})]})]}),(0,r.jsx)(n.P.div,{className:"mt-16",initial:{opacity:0},animate:{opacity:1},transition:{delay:.3,duration:.6},children:(0,r.jsx)("p",{className:"text-sm text-gray-400",children:"Error Code: METAMORPHIC_404_QUANTUM_DISPLACEMENT"})})]})})}},4725:(e,t,a)=>{Promise.resolve().then(a.bind(a,1906))},9434:(e,t,a)=>{"use strict";a.d(t,{cn:()=>s});var r=a(2596),i=a(9688);function s(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,i.QP)((0,r.$)(t))}}},e=>{var t=t=>e(e.s=t);e.O(0,[497,874,230,441,684,358],()=>t(4725)),_N_E=e.O()}]);