import type { Metadata } from 'next';
import { ContactForm } from '@/components/contact-form';

export const metadata: Metadata = {
  title: 'Contact Us | Metamorphic Labs',
  description: 'Get in touch with Metamorphic Labs. Whether you have questions about our AI systems, want to collaborate, or need support, we\'re here to help.',
};

export default function ContactPage() {
  return (
    <div className="bg-black text-white">
      <div className="mx-auto max-w-7xl px-6 py-24 sm:py-32 lg:px-8">
        <div className="mx-auto max-w-2xl lg:mx-0">
          <h1 className="text-4xl font-bold tracking-tight text-white sm:text-6xl">
            Get in <span className="gradient-text">Touch</span>
          </h1>
          <p className="mt-6 text-lg leading-8 text-gray-300">
            Ready to explore the future of AI and quantum systems? We&apos;d love to hear from you.
          </p>
        </div>

        <div className="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-x-8 gap-y-16 lg:mx-0 lg:mt-10 lg:max-w-none lg:grid-cols-2">
          {/* Contact Information */}
          <div>
            <h2 className="text-2xl font-bold text-white mb-8">Contact Information</h2>
            
            <div className="space-y-6">
              <div className="rounded-lg bg-gray-900 p-6 border border-gray-800">
                <h3 className="text-lg font-semibold text-white mb-2">General Inquiries</h3>
                <p className="text-gray-300"><EMAIL></p>
              </div>
              
              <div className="rounded-lg bg-gray-900 p-6 border border-gray-800">
                <h3 className="text-lg font-semibold text-white mb-2">Technical Support</h3>
                <p className="text-gray-300"><EMAIL></p>
              </div>
              
              <div className="rounded-lg bg-gray-900 p-6 border border-gray-800">
                <h3 className="text-lg font-semibold text-white mb-2">Partnerships</h3>
                <p className="text-gray-300"><EMAIL></p>
              </div>
            </div>

            <div className="mt-8">
              <h3 className="text-lg font-semibold text-white mb-4">Our Systems</h3>
              <div className="space-y-3">
                <a 
                  href="https://catalyst.metamorphiclabs.ai" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="block text-blue-400 hover:text-blue-300 transition-colors"
                >
                  → Catalyst - Prompt Engineering Platform
                </a>
                <a 
                  href="#metamorphic-reactor" 
                  className="block text-blue-400 hover:text-blue-300 transition-colors"
                >
                  → Metamorphic Reactor - Multi-AI Orchestration
                </a>
                <a 
                  href="https://vault024.metamorphiclabs.ai" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="block text-yellow-400 hover:text-yellow-300 transition-colors"
                >
                  → Vault 024 - Generative Art & NFTs
                </a>
              </div>
            </div>
          </div>

          {/* Contact Form */}
          <div>
            <h2 className="text-2xl font-bold text-white mb-8">Send us a Message</h2>
            <ContactForm />
          </div>
        </div>
      </div>
    </div>
  );
}
