[{"name": "generate-buildid", "duration": 611, "timestamp": 175949573574, "id": 4, "parentId": 1, "tags": {}, "startTime": 1751159100852, "traceId": "a6464f95f5d1c759"}, {"name": "load-custom-routes", "duration": 1504, "timestamp": 175949574456, "id": 5, "parentId": 1, "tags": {}, "startTime": 1751159100853, "traceId": "a6464f95f5d1c759"}, {"name": "create-dist-dir", "duration": 1275, "timestamp": 175949880046, "id": 6, "parentId": 1, "tags": {}, "startTime": 1751159101158, "traceId": "a6464f95f5d1c759"}, {"name": "create-pages-mapping", "duration": 834, "timestamp": 175950110288, "id": 7, "parentId": 1, "tags": {}, "startTime": 1751159101389, "traceId": "a6464f95f5d1c759"}, {"name": "collect-app-paths", "duration": 7590, "timestamp": 175950111349, "id": 8, "parentId": 1, "tags": {}, "startTime": 1751159101390, "traceId": "a6464f95f5d1c759"}, {"name": "create-app-mapping", "duration": 222197, "timestamp": 175950119233, "id": 9, "parentId": 1, "tags": {}, "startTime": 1751159101398, "traceId": "a6464f95f5d1c759"}, {"name": "public-dir-conflict-check", "duration": 3091, "timestamp": 175950343129, "id": 10, "parentId": 1, "tags": {}, "startTime": 1751159101621, "traceId": "a6464f95f5d1c759"}, {"name": "generate-routes-manifest", "duration": 11476, "timestamp": 175950347346, "id": 11, "parentId": 1, "tags": {}, "startTime": 1751159101626, "traceId": "a6464f95f5d1c759"}, {"name": "create-entrypoints", "duration": 125227, "timestamp": 175954219766, "id": 15, "parentId": 13, "tags": {}, "startTime": 1751159105498, "traceId": "a6464f95f5d1c759"}, {"name": "generate-webpack-config", "duration": 1339632, "timestamp": 175954345593, "id": 16, "parentId": 14, "tags": {}, "startTime": 1751159105624, "traceId": "a6464f95f5d1c759"}, {"name": "next-trace-entrypoint-plugin", "duration": 5893, "timestamp": 175956128619, "id": 18, "parentId": 17, "tags": {}, "startTime": 1751159107407, "traceId": "a6464f95f5d1c759"}, {"name": "build-module", "duration": 169006, "timestamp": 175956779078, "id": 30, "parentId": 20, "tags": {"name": "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\node_modules\\next\\dist\\build\\webpack\\loaders\\next-app-loader\\index.js?page=%2Fsitemap.xml%2Froute&name=app%2Fsitemap.xml%2Froute&pagePath=private-next-app-dir%2Fsitemap.ts&appDir=C%3A%5CUsers%5Credpanda%5CDesktop%5CNew%20folder%20(9)%5Cmetamorphic-labs-website%5Csrc%5Capp&appPaths=%2Fsitemap&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!", "layer": "rsc"}, "startTime": 1751159108058, "traceId": "a6464f95f5d1c759"}, {"name": "add-entry", "duration": 1216707, "timestamp": 175956142213, "id": 21, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Ffavicon.ico%2Froute&name=app%2Ffavicon.ico%2Froute&pagePath=private-next-app-dir%2Ffavicon.ico&appDir=C%3A%5CUsers%5Credpanda%5CDesktop%5CNew%20folder%20(9)%5Cmetamorphic-labs-website%5Csrc%5Capp&appPaths=%2Ffavicon.ico&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751159107421, "traceId": "a6464f95f5d1c759"}, {"name": "add-entry", "duration": 1305052, "timestamp": 175956142366, "id": 23, "parentId": 19, "tags": {"request": "next/dist/pages/_app"}, "startTime": 1751159107421, "traceId": "a6464f95f5d1c759"}, {"name": "add-entry", "duration": 1304547, "timestamp": 175956142948, "id": 29, "parentId": 19, "tags": {"request": "next/dist/pages/_document"}, "startTime": 1751159107421, "traceId": "a6464f95f5d1c759"}, {"name": "add-entry", "duration": 1305211, "timestamp": 175956142313, "id": 22, "parentId": 19, "tags": {"request": "next-route-loader?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=next%2Fdist%2Fpages%2F_error&absoluteAppPath=next%2Fdist%2Fpages%2F_app&absoluteDocumentPath=next%2Fdist%2Fpages%2F_document&middlewareConfigBase64=e30%3D!"}, "startTime": 1751159107421, "traceId": "a6464f95f5d1c759"}, {"name": "build-module-ts", "duration": 91568, "timestamp": 175957430026, "id": 32, "parentId": 31, "tags": {"name": "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\app\\sitemap.ts", "layer": "rsc"}, "startTime": 1751159108709, "traceId": "a6464f95f5d1c759"}, {"name": "build-module-tsx", "duration": 93835, "timestamp": 175957441476, "id": 33, "parentId": 17, "tags": {"name": "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\app\\layout.tsx", "layer": "rsc"}, "startTime": 1751159108720, "traceId": "a6464f95f5d1c759"}, {"name": "build-module-tsx", "duration": 97364, "timestamp": 175957442486, "id": 34, "parentId": 17, "tags": {"name": "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\app\\page.tsx", "layer": "rsc"}, "startTime": 1751159108721, "traceId": "a6464f95f5d1c759"}, {"name": "build-module", "duration": 555528, "timestamp": 175956998210, "id": 31, "parentId": 30, "tags": {"name": "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\node_modules\\next\\dist\\build\\webpack\\loaders\\next-metadata-route-loader.js?filePath=C%3A%5CUsers%5Credpanda%5CDesktop%5CNew%20folder%20(9)%5Cmetamorphic-labs-website%5Csrc%5Capp%5Csitemap.ts&isDynamicRouteExtension=1!?__next_metadata_route__", "layer": "rsc"}, "startTime": 1751159108277, "traceId": "a6464f95f5d1c759"}, {"name": "build-module-tsx", "duration": 12495, "timestamp": 175957680566, "id": 36, "parentId": 34, "tags": {"name": "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\hero-section.tsx", "layer": "rsc"}, "startTime": 1751159108959, "traceId": "a6464f95f5d1c759"}, {"name": "build-module-tsx", "duration": 16414, "timestamp": 175957678334, "id": 35, "parentId": 33, "tags": {"name": "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\navigation.tsx", "layer": "rsc"}, "startTime": 1751159108957, "traceId": "a6464f95f5d1c759"}, {"name": "build-module-tsx", "duration": 18640, "timestamp": 175957686780, "id": 38, "parentId": 17, "tags": {"name": "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\metamorphic-logo.tsx", "layer": "rsc"}, "startTime": 1751159108965, "traceId": "a6464f95f5d1c759"}, {"name": "build-module-tsx", "duration": 25198, "timestamp": 175957685824, "id": 37, "parentId": 34, "tags": {"name": "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\structured-data.tsx", "layer": "rsc"}, "startTime": 1751159108964, "traceId": "a6464f95f5d1c759"}, {"name": "add-entry", "duration": 1574131, "timestamp": 175956142408, "id": 24, "parentId": 19, "tags": {"request": "next-app-loader?page=%2F_not-found%2Fpage&name=app%2F_not-found%2Fpage&pagePath=private-next-app-dir%2Fnot-found.tsx&appDir=C%3A%5CUsers%5Credpanda%5CDesktop%5CNew%20folder%20(9)%5Cmetamorphic-labs-website%5Csrc%5Capp&appPaths=%2Fnot-found&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751159107421, "traceId": "a6464f95f5d1c759"}, {"name": "add-entry", "duration": 1574120, "timestamp": 175956142439, "id": 25, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fpage&name=app%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Credpanda%5CDesktop%5CNew%20folder%20(9)%5Cmetamorphic-labs-website%5Csrc%5Capp&appPaths=%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751159107421, "traceId": "a6464f95f5d1c759"}, {"name": "add-entry", "duration": 1574100, "timestamp": 175956142467, "id": 26, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fcontact%2Fpage&name=app%2Fcontact%2Fpage&pagePath=private-next-app-dir%2Fcontact%2Fpage.tsx&appDir=C%3A%5CUsers%5Credpanda%5CDesktop%5CNew%20folder%20(9)%5Cmetamorphic-labs-website%5Csrc%5Capp&appPaths=%2Fcontact%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751159107421, "traceId": "a6464f95f5d1c759"}, {"name": "add-entry", "duration": 1574075, "timestamp": 175956142496, "id": 27, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fabout%2Fpage&name=app%2Fabout%2Fpage&pagePath=private-next-app-dir%2Fabout%2Fpage.tsx&appDir=C%3A%5CUsers%5Credpanda%5CDesktop%5CNew%20folder%20(9)%5Cmetamorphic-labs-website%5Csrc%5Capp&appPaths=%2Fabout%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751159107421, "traceId": "a6464f95f5d1c759"}, {"name": "add-entry", "duration": 1582788, "timestamp": 175956142524, "id": 28, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fsystems%2Fpage&name=app%2Fsystems%2Fpage&pagePath=private-next-app-dir%2Fsystems%2Fpage.tsx&appDir=C%3A%5CUsers%5Credpanda%5CDesktop%5CNew%20folder%20(9)%5Cmetamorphic-labs-website%5Csrc%5Capp&appPaths=%2Fsystems%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751159107421, "traceId": "a6464f95f5d1c759"}, {"name": "build-module-js", "duration": 32315, "timestamp": 175957718400, "id": 39, "parentId": 31, "tags": {"name": "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\node_modules\\next\\dist\\build\\webpack\\loaders\\metadata\\resolve-route-data.js", "layer": "rsc"}, "startTime": 1751159108997, "traceId": "a6464f95f5d1c759"}, {"name": "add-entry", "duration": 1626220, "timestamp": 175956140153, "id": 20, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fsitemap.xml%2Froute&name=app%2Fsitemap.xml%2Froute&pagePath=private-next-app-dir%2Fsitemap.ts&appDir=C%3A%5CUsers%5Credpanda%5CDesktop%5CNew%20folder%20(9)%5Cmetamorphic-labs-website%5Csrc%5Capp&appPaths=%2Fsitemap&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751159107419, "traceId": "a6464f95f5d1c759"}, {"name": "build-module", "duration": 9695, "timestamp": 175957951466, "id": 88, "parentId": 17, "tags": {"name": "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\node_modules\\next\\dist\\build\\webpack\\loaders\\next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Credpanda%5C%5CDesktop%5C%5CNew%20folder%20(9)%5C%5Cmetamorphic-labs-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Credpanda%5C%5CDesktop%5C%5CNew%20folder%20(9)%5C%5Cmetamorphic-labs-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Credpanda%5C%5CDesktop%5C%5CNew%20folder%20(9)%5C%5Cmetamorphic-labs-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-jetbrains-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Credpanda%5C%5CDesktop%5C%5CNew%20folder%20(9)%5C%5Cmetamorphic-labs-website%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Credpanda%5C%5CDesktop%5C%5CNew%20folder%20(9)%5C%5Cmetamorphic-labs-website%5C%5Csrc%5C%5Ccomponents%5C%5Cmetamorphic-logo.tsx%22%2C%22ids%22%3A%5B%22MetamorphicLogo%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Credpanda%5C%5CDesktop%5C%5CNew%20folder%20(9)%5C%5Cmetamorphic-labs-website%5C%5Csrc%5C%5Ccomponents%5C%5Cnavigation.tsx%22%2C%22ids%22%3A%5B%22Navigation%22%5D%7D&server=true!", "layer": "ssr"}, "startTime": 1751159109230, "traceId": "a6464f95f5d1c759"}, {"name": "build-module", "duration": 2920, "timestamp": 175957961220, "id": 89, "parentId": 17, "tags": {"name": "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\node_modules\\next\\dist\\build\\webpack\\loaders\\next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Credpanda%5C%5CDesktop%5C%5CNew%20folder%20(9)%5C%5Cmetamorphic-labs-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Credpanda%5C%5CDesktop%5C%5CNew%20folder%20(9)%5C%5Cmetamorphic-labs-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Credpanda%5C%5CDesktop%5C%5CNew%20folder%20(9)%5C%5Cmetamorphic-labs-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-jetbrains-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Credpanda%5C%5CDesktop%5C%5CNew%20folder%20(9)%5C%5Cmetamorphic-labs-website%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Credpanda%5C%5CDesktop%5C%5CNew%20folder%20(9)%5C%5Cmetamorphic-labs-website%5C%5Csrc%5C%5Ccomponents%5C%5Cmetamorphic-logo.tsx%22%2C%22ids%22%3A%5B%22MetamorphicLogo%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Credpanda%5C%5CDesktop%5C%5CNew%20folder%20(9)%5C%5Cmetamorphic-labs-website%5C%5Csrc%5C%5Ccomponents%5C%5Cnavigation.tsx%22%2C%22ids%22%3A%5B%22Navigation%22%5D%7D&server=true!", "layer": "rsc"}, "startTime": 1751159109240, "traceId": "a6464f95f5d1c759"}, {"name": "build-module-tsx", "duration": 86807, "timestamp": 175958007038, "id": 91, "parentId": 88, "tags": {"name": "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\metamorphic-logo.tsx", "layer": "ssr"}, "startTime": 1751159109286, "traceId": "a6464f95f5d1c759"}, {"name": "build-module-tsx", "duration": 100666, "timestamp": 175958001758, "id": 90, "parentId": 17, "tags": {"name": "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\hero-section.tsx", "layer": "ssr"}, "startTime": 1751159109280, "traceId": "a6464f95f5d1c759"}, {"name": "build-module-tsx", "duration": 104425, "timestamp": 175958007553, "id": 92, "parentId": 88, "tags": {"name": "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\navigation.tsx", "layer": "ssr"}, "startTime": 1751159109286, "traceId": "a6464f95f5d1c759"}, {"name": "make", "duration": 2901389, "timestamp": 175956139395, "id": 19, "parentId": 17, "tags": {}, "startTime": 1751159107418, "traceId": "a6464f95f5d1c759"}, {"name": "get-entries", "duration": 10850, "timestamp": 175959046998, "id": 94, "parentId": 93, "tags": {}, "startTime": 1751159110326, "traceId": "a6464f95f5d1c759"}, {"name": "node-file-trace-plugin", "duration": 337185, "timestamp": 175959068867, "id": 95, "parentId": 93, "tags": {"traceEntryCount": "18"}, "startTime": 1751159110347, "traceId": "a6464f95f5d1c759"}, {"name": "collect-traced-files", "duration": 3455, "timestamp": 175959406102, "id": 96, "parentId": 93, "tags": {}, "startTime": 1751159110685, "traceId": "a6464f95f5d1c759"}, {"name": "finish-modules", "duration": 363922, "timestamp": 175959045656, "id": 93, "parentId": 18, "tags": {}, "startTime": 1751159110324, "traceId": "a6464f95f5d1c759"}, {"name": "chunk-graph", "duration": 63515, "timestamp": 175959572166, "id": 98, "parentId": 97, "tags": {}, "startTime": 1751159110851, "traceId": "a6464f95f5d1c759"}, {"name": "optimize-modules", "duration": 243, "timestamp": 175959636374, "id": 100, "parentId": 97, "tags": {}, "startTime": 1751159110915, "traceId": "a6464f95f5d1c759"}, {"name": "optimize-chunks", "duration": 69317, "timestamp": 175959637155, "id": 101, "parentId": 97, "tags": {}, "startTime": 1751159110916, "traceId": "a6464f95f5d1c759"}, {"name": "optimize-tree", "duration": 75, "timestamp": 175959706653, "id": 102, "parentId": 97, "tags": {}, "startTime": 1751159110985, "traceId": "a6464f95f5d1c759"}, {"name": "optimize-chunk-modules", "duration": 94931, "timestamp": 175959706983, "id": 103, "parentId": 97, "tags": {}, "startTime": 1751159110986, "traceId": "a6464f95f5d1c759"}, {"name": "optimize", "duration": 166123, "timestamp": 175959636137, "id": 99, "parentId": 97, "tags": {}, "startTime": 1751159110915, "traceId": "a6464f95f5d1c759"}, {"name": "module-hash", "duration": 67668, "timestamp": 175959869067, "id": 104, "parentId": 97, "tags": {}, "startTime": 1751159111148, "traceId": "a6464f95f5d1c759"}, {"name": "code-generation", "duration": 216932, "timestamp": 175959936884, "id": 105, "parentId": 97, "tags": {}, "startTime": 1751159111215, "traceId": "a6464f95f5d1c759"}, {"name": "hash", "duration": 30559, "timestamp": 175960173530, "id": 106, "parentId": 97, "tags": {}, "startTime": 1751159111452, "traceId": "a6464f95f5d1c759"}, {"name": "code-generation-jobs", "duration": 1176, "timestamp": 175960204080, "id": 107, "parentId": 97, "tags": {}, "startTime": 1751159111483, "traceId": "a6464f95f5d1c759"}, {"name": "module-assets", "duration": 1443, "timestamp": 175960204900, "id": 108, "parentId": 97, "tags": {}, "startTime": 1751159111483, "traceId": "a6464f95f5d1c759"}, {"name": "create-chunk-assets", "duration": 22054, "timestamp": 175960206387, "id": 109, "parentId": 97, "tags": {}, "startTime": 1751159111485, "traceId": "a6464f95f5d1c759"}, {"name": "minify-js", "duration": 80221, "timestamp": 175960333510, "id": 114, "parentId": 110, "tags": {"name": "../pages/_app.js", "cache": "HIT"}, "startTime": 1751159111612, "traceId": "a6464f95f5d1c759"}, {"name": "minify-js", "duration": 53055, "timestamp": 175960360709, "id": 120, "parentId": 110, "tags": {"name": "../pages/_document.js", "cache": "HIT"}, "startTime": 1751159111639, "traceId": "a6464f95f5d1c759"}, {"name": "minify-js", "duration": 52875, "timestamp": 175960360898, "id": 121, "parentId": 110, "tags": {"name": "../webpack-runtime.js", "cache": "HIT"}, "startTime": 1751159111639, "traceId": "a6464f95f5d1c759"}, {"name": "minify-js", "duration": 52837, "timestamp": 175960360943, "id": 122, "parentId": 110, "tags": {"name": "447.js", "cache": "HIT"}, "startTime": 1751159111639, "traceId": "a6464f95f5d1c759"}, {"name": "minify-js", "duration": 9492, "timestamp": 175960404318, "id": 124, "parentId": 110, "tags": {"name": "548.js", "cache": "HIT"}, "startTime": 1751159111683, "traceId": "a6464f95f5d1c759"}, {"name": "minify-js", "duration": 383655, "timestamp": 175960325223, "id": 112, "parentId": 110, "tags": {"name": "../app/favicon.ico/route.js", "cache": "MISS"}, "startTime": 1751159111604, "traceId": "a6464f95f5d1c759"}, {"name": "minify-js", "duration": 375176, "timestamp": 175960333915, "id": 115, "parentId": 110, "tags": {"name": "../app/_not-found/page.js", "cache": "MISS"}, "startTime": 1751159111612, "traceId": "a6464f95f5d1c759"}, {"name": "minify-js", "duration": 410109, "timestamp": 175960299310, "id": 111, "parentId": 110, "tags": {"name": "../app/sitemap.xml/route.js", "cache": "MISS"}, "startTime": 1751159111578, "traceId": "a6464f95f5d1c759"}, {"name": "minify-js", "duration": 388504, "timestamp": 175960328710, "id": 113, "parentId": 110, "tags": {"name": "../pages/_error.js", "cache": "MISS"}, "startTime": 1751159111607, "traceId": "a6464f95f5d1c759"}, {"name": "minify-js", "duration": 375865, "timestamp": 175960341867, "id": 117, "parentId": 110, "tags": {"name": "../app/contact/page.js", "cache": "MISS"}, "startTime": 1751159111620, "traceId": "a6464f95f5d1c759"}, {"name": "minify-js", "duration": 373286, "timestamp": 175960344744, "id": 118, "parentId": 110, "tags": {"name": "../app/about/page.js", "cache": "MISS"}, "startTime": 1751159111623, "traceId": "a6464f95f5d1c759"}, {"name": "minify-js", "duration": 383490, "timestamp": 175960335897, "id": 116, "parentId": 110, "tags": {"name": "../app/page.js", "cache": "MISS"}, "startTime": 1751159111614, "traceId": "a6464f95f5d1c759"}, {"name": "minify-js", "duration": 367840, "timestamp": 175960408344, "id": 126, "parentId": 110, "tags": {"name": "84.js", "cache": "MISS"}, "startTime": 1751159111687, "traceId": "a6464f95f5d1c759"}, {"name": "minify-js", "duration": 444057, "timestamp": 175960347941, "id": 119, "parentId": 110, "tags": {"name": "../app/systems/page.js", "cache": "MISS"}, "startTime": 1751159111626, "traceId": "a6464f95f5d1c759"}, {"name": "minify-js", "duration": 625492, "timestamp": 175960404434, "id": 125, "parentId": 110, "tags": {"name": "580.js", "cache": "MISS"}, "startTime": 1751159111683, "traceId": "a6464f95f5d1c759"}, {"name": "minify-js", "duration": 1393582, "timestamp": 175960360990, "id": 123, "parentId": 110, "tags": {"name": "559.js", "cache": "MISS"}, "startTime": 1751159111640, "traceId": "a6464f95f5d1c759"}, {"name": "minify-webpack-plugin-optimize", "duration": 1503389, "timestamp": 175960251221, "id": 110, "parentId": 17, "tags": {"compilationName": "server", "mangle": "true"}, "startTime": 1751159111530, "traceId": "a6464f95f5d1c759"}, {"name": "css-minimizer-plugin", "duration": 275, "timestamp": 175961754908, "id": 127, "parentId": 17, "tags": {}, "startTime": 1751159113033, "traceId": "a6464f95f5d1c759"}, {"name": "create-trace-assets", "duration": 6427, "timestamp": 175961755585, "id": 128, "parentId": 18, "tags": {}, "startTime": 1751159113034, "traceId": "a6464f95f5d1c759"}, {"name": "seal", "duration": 2293057, "timestamp": 175959501433, "id": 97, "parentId": 17, "tags": {}, "startTime": 1751159110780, "traceId": "a6464f95f5d1c759"}, {"name": "webpack-compilation", "duration": 5824129, "timestamp": 175956122505, "id": 17, "parentId": 14, "tags": {"name": "server"}, "startTime": 1751159107401, "traceId": "a6464f95f5d1c759"}, {"name": "emit", "duration": 347481, "timestamp": 175961950066, "id": 129, "parentId": 14, "tags": {}, "startTime": 1751159113229, "traceId": "a6464f95f5d1c759"}, {"name": "webpack-close", "duration": 2214812, "timestamp": 175962348982, "id": 130, "parentId": 14, "tags": {"name": "server"}, "startTime": 1751159113628, "traceId": "a6464f95f5d1c759"}, {"name": "webpack-generate-error-stats", "duration": 8935, "timestamp": 175964564008, "id": 131, "parentId": 130, "tags": {}, "startTime": 1751159115843, "traceId": "a6464f95f5d1c759"}, {"name": "run-webpack-compiler", "duration": 10354002, "timestamp": 175954219659, "id": 14, "parentId": 13, "tags": {}, "startTime": 1751159105498, "traceId": "a6464f95f5d1c759"}, {"name": "format-webpack-messages", "duration": 191, "timestamp": 175964573678, "id": 132, "parentId": 13, "tags": {}, "startTime": 1751159115852, "traceId": "a6464f95f5d1c759"}, {"name": "worker-main-server", "duration": 10355519, "timestamp": 175954218829, "id": 13, "parentId": 1, "tags": {}, "startTime": 1751159105497, "traceId": "a6464f95f5d1c759"}, {"name": "create-entrypoints", "duration": 62337, "timestamp": 175967464047, "id": 136, "parentId": 134, "tags": {}, "startTime": 1751159118744, "traceId": "a6464f95f5d1c759"}, {"name": "generate-webpack-config", "duration": 1282925, "timestamp": 175967526953, "id": 137, "parentId": 135, "tags": {}, "startTime": 1751159118806, "traceId": "a6464f95f5d1c759"}, {"name": "make", "duration": 1742, "timestamp": 175969225825, "id": 139, "parentId": 138, "tags": {}, "startTime": 1751159120505, "traceId": "a6464f95f5d1c759"}, {"name": "chunk-graph", "duration": 1752, "timestamp": 175969235814, "id": 141, "parentId": 140, "tags": {}, "startTime": 1751159120515, "traceId": "a6464f95f5d1c759"}, {"name": "optimize-modules", "duration": 83, "timestamp": 175969237917, "id": 143, "parentId": 140, "tags": {}, "startTime": 1751159120517, "traceId": "a6464f95f5d1c759"}, {"name": "optimize-chunks", "duration": 660, "timestamp": 175969238282, "id": 144, "parentId": 140, "tags": {}, "startTime": 1751159120518, "traceId": "a6464f95f5d1c759"}, {"name": "optimize-tree", "duration": 103, "timestamp": 175969239217, "id": 145, "parentId": 140, "tags": {}, "startTime": 1751159120519, "traceId": "a6464f95f5d1c759"}, {"name": "optimize-chunk-modules", "duration": 834, "timestamp": 175969240187, "id": 146, "parentId": 140, "tags": {}, "startTime": 1751159120520, "traceId": "a6464f95f5d1c759"}, {"name": "optimize", "duration": 3805, "timestamp": 175969237748, "id": 142, "parentId": 140, "tags": {}, "startTime": 1751159120517, "traceId": "a6464f95f5d1c759"}, {"name": "module-hash", "duration": 238, "timestamp": 175969243681, "id": 147, "parentId": 140, "tags": {}, "startTime": 1751159120523, "traceId": "a6464f95f5d1c759"}, {"name": "code-generation", "duration": 354, "timestamp": 175969244020, "id": 148, "parentId": 140, "tags": {}, "startTime": 1751159120524, "traceId": "a6464f95f5d1c759"}, {"name": "hash", "duration": 941, "timestamp": 175969244965, "id": 149, "parentId": 140, "tags": {}, "startTime": 1751159120524, "traceId": "a6464f95f5d1c759"}, {"name": "code-generation-jobs", "duration": 167, "timestamp": 175969245898, "id": 150, "parentId": 140, "tags": {}, "startTime": 1751159120525, "traceId": "a6464f95f5d1c759"}, {"name": "module-assets", "duration": 205, "timestamp": 175969245996, "id": 151, "parentId": 140, "tags": {}, "startTime": 1751159120525, "traceId": "a6464f95f5d1c759"}, {"name": "create-chunk-assets", "duration": 670, "timestamp": 175969246223, "id": 152, "parentId": 140, "tags": {}, "startTime": 1751159120526, "traceId": "a6464f95f5d1c759"}, {"name": "minify-js", "duration": 1474, "timestamp": 175969299272, "id": 154, "parentId": 153, "tags": {"name": "interception-route-rewrite-manifest.js", "cache": "HIT"}, "startTime": 1751159120579, "traceId": "a6464f95f5d1c759"}, {"name": "minify-webpack-plugin-optimize", "duration": 16707, "timestamp": 175969284987, "id": 153, "parentId": 138, "tags": {"compilationName": "edge-server", "mangle": "true"}, "startTime": 1751159120565, "traceId": "a6464f95f5d1c759"}, {"name": "css-minimizer-plugin", "duration": 778, "timestamp": 175969303254, "id": 155, "parentId": 138, "tags": {}, "startTime": 1751159120583, "traceId": "a6464f95f5d1c759"}, {"name": "seal", "duration": 85609, "timestamp": 175969234723, "id": 140, "parentId": 138, "tags": {}, "startTime": 1751159120514, "traceId": "a6464f95f5d1c759"}, {"name": "webpack-compilation", "duration": 107165, "timestamp": 175969214206, "id": 138, "parentId": 135, "tags": {"name": "edge-server"}, "startTime": 1751159120494, "traceId": "a6464f95f5d1c759"}, {"name": "emit", "duration": 13164, "timestamp": 175969323152, "id": 156, "parentId": 135, "tags": {}, "startTime": 1751159120603, "traceId": "a6464f95f5d1c759"}]