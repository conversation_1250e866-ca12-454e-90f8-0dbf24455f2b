'use client';

import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface MetamorphicLogoProps {
  className?: string;
  animated?: boolean;
}

export function MetamorphicLogo({ className, animated = false }: MetamorphicLogoProps) {
  return (
    <motion.svg
      className={cn('text-white', className)}
      viewBox="0 0 200 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      initial={animated ? { opacity: 0, scale: 0.8 } : undefined}
      animate={animated ? { opacity: 1, scale: 1 } : undefined}
      transition={animated ? { duration: 0.8, ease: "easeOut" } : undefined}
    >
      <defs>
        <linearGradient id="gradient-stroke" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" stopColor="#3B82F6">
            {animated && (
              <animate
                attributeName="stop-color"
                values="#3B82F6;#8B5CF6;#9333EA;#3B82F6"
                dur="3s"
                repeatCount="indefinite"
              />
            )}
          </stop>
          <stop offset="50%" stopColor="#8B5CF6" />
          <stop offset="100%" stopColor="#9333EA">
            {animated && (
              <animate
                attributeName="stop-color"
                values="#9333EA;#3B82F6;#8B5CF6;#9333EA"
                dur="3s"
                repeatCount="indefinite"
              />
            )}
          </stop>
        </linearGradient>
      </defs>

      {/* Geometric logo shape */}
      <motion.g
        initial={animated ? { opacity: 0 } : undefined}
        animate={animated ? { opacity: 1 } : undefined}
        transition={animated ? { delay: 0.3, duration: 0.6 } : undefined}
      >
        <motion.path
          d="M10 20 L25 5 L40 20 L25 35 Z"
          stroke="url(#gradient-stroke)"
          strokeWidth="2"
          fill="none"
          initial={animated ? { pathLength: 0 } : undefined}
          animate={animated ? { pathLength: 1 } : undefined}
          transition={animated ? { delay: 0.5, duration: 1.5, ease: "easeInOut" } : undefined}
        />
        <motion.circle
          cx="25"
          cy="20"
          r="3"
          fill="url(#gradient-stroke)"
          initial={animated ? { scale: 0 } : undefined}
          animate={animated ? { scale: 1 } : undefined}
          transition={animated ? { delay: 1.8, duration: 0.4, ease: "easeOut" } : undefined}
        />
      </motion.g>

      {/* Company name */}
      <motion.text
        x="55"
        y="16"
        className="fill-current text-sm font-bold tracking-wide"
        style={{ fontFamily: 'Inter, sans-serif' }}
        initial={animated ? { opacity: 0, x: -10 } : undefined}
        animate={animated ? { opacity: 1, x: 0 } : undefined}
        transition={animated ? { delay: 0.8, duration: 0.6 } : undefined}
      >
        METAMORPHIC
      </motion.text>
      <motion.text
        x="55"
        y="30"
        className="fill-current text-xs font-medium tracking-wider opacity-80"
        style={{ fontFamily: 'Inter, sans-serif' }}
        initial={animated ? { opacity: 0, x: -10 } : undefined}
        animate={animated ? { opacity: 1, x: 0 } : undefined}
        transition={animated ? { delay: 1.0, duration: 0.6 } : undefined}
      >
        LABS
      </motion.text>
    </motion.svg>
  );
}
