import { cn } from '@/lib/utils';

interface MetamorphicLogoProps {
  className?: string;
}

export function MetamorphicLogo({ className }: MetamorphicLogoProps) {
  return (
    <svg
      className={cn('text-white', className)}
      viewBox="0 0 200 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <defs>
        <linearGradient id="gradient-stroke" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" stopColor="#3B82F6" />
          <stop offset="50%" stopColor="#8B5CF6" />
          <stop offset="100%" stopColor="#9333EA" />
        </linearGradient>
      </defs>
      
      {/* Geometric logo shape */}
      <g className="gradient-stroke">
        <path
          d="M10 20 L25 5 L40 20 L25 35 Z"
          stroke="url(#gradient-stroke)"
          strokeWidth="2"
          fill="none"
          className="animate-pulse"
        />
        <circle
          cx="25"
          cy="20"
          r="3"
          fill="url(#gradient-stroke)"
        />
      </g>
      
      {/* Company name */}
      <text
        x="55"
        y="16"
        className="fill-current text-sm font-bold tracking-wide"
        style={{ fontFamily: 'Inter, sans-serif' }}
      >
        METAMORPHIC
      </text>
      <text
        x="55"
        y="30"
        className="fill-current text-xs font-medium tracking-wider opacity-80"
        style={{ fontFamily: 'Inter, sans-serif' }}
      >
        LABS
      </text>
    </svg>
  );
}
