'use client';

import Image from 'next/image';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface MetamorphicLogoProps {
  className?: string;
  animated?: boolean;
}

export function MetamorphicLogo({ className, animated = false }: MetamorphicLogoProps) {
  return (
    <motion.div
      className={cn('flex items-center', className)}
      initial={animated ? { opacity: 0, scale: 0.8 } : undefined}
      animate={animated ? { opacity: 1, scale: 1 } : undefined}
      transition={animated ? { duration: 0.8, ease: "easeOut" } : undefined}
    >
      <Image
        src="/metamorphic-labs-logo.png"
        alt="Metamorphic Labs"
        width={200}
        height={40}
        className="h-auto w-auto"
        priority
      />
    </motion.div>
  );
}
