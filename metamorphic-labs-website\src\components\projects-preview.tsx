'use client';

import Link from 'next/link';
import { motion } from 'framer-motion';
import { useInView } from 'framer-motion';
import { useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { ExternalLink, ArrowRight, Sparkles, Zap, Palette } from 'lucide-react';

const projects = [
  {
    name: 'Catalyst',
    description: 'Advanced prompt engineering platform for optimizing AI interactions and developing sophisticated prompt ecosystems.',
    status: 'Active',
    url: 'https://catalyst.metamorphiclabs.ai',
    icon: Sparkles,
    gradient: true,
    features: ['Prompt Optimization', 'AI Model Integration', 'Performance Analytics'],
  },
  {
    name: 'Metamorphic Reactor',
    description: 'Multi-agent orchestration system enabling complex AI workflows and intelligent automation across diverse domains.',
    status: 'Development',
    url: '#metamorphic-reactor',
    icon: Zap,
    gradient: true,
    features: ['Multi-Agent Coordination', 'Workflow Automation', 'Intelligent Routing'],
  },
  {
    name: 'Vault 024',
    description: 'Decentralized art gallery and NFT platform showcasing generative creativity and blockchain-based digital assets.',
    status: 'Active',
    url: 'https://vault024.metamorphiclabs.ai',
    icon: Palette,
    vault: true,
    features: ['Generative Art', 'NFT Marketplace', 'Blockchain Integration'],
  },
];

export function ProjectsPreview() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  return (
    <section ref={ref} className="py-24 sm:py-32">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
          transition={{ duration: 0.8 }}
          className="mx-auto max-w-2xl text-center mb-16"
        >
          <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl lg:text-5xl">
            Our <span className="gradient-text">Systems</span> in Action
          </h2>
          <p className="mt-6 text-lg leading-8 text-gray-300">
            Explore our cutting-edge platforms that are reshaping the landscape of AI and creative technology
          </p>
        </motion.div>

        <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
          {projects.map((project, index) => (
            <motion.div
              key={project.name}
              initial={{ opacity: 0, y: 20 }}
              animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
              transition={{ duration: 0.8, delay: index * 0.2 }}
            >
              <Card className={`holographic-hover h-full ${
                project.vault 
                  ? 'vault-card' 
                  : 'bg-gray-900 border-gray-800'
              }`}>
                <CardContent className="p-8 h-full flex flex-col">
                  {/* Header */}
                  <div className="flex items-center justify-between mb-6">
                    <div className={`p-3 rounded-lg ${
                      project.vault 
                        ? 'bg-black border border-yellow-400' 
                        : 'bg-gray-800'
                    }`}>
                      <project.icon className={`h-6 w-6 ${
                        project.vault 
                          ? 'text-yellow-400' 
                          : project.gradient 
                            ? 'text-blue-400' 
                            : 'text-white'
                      }`} />
                    </div>
                    <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                      project.status === 'Active'
                        ? project.vault
                          ? 'bg-yellow-400 text-black'
                          : 'bg-green-500 text-white'
                        : 'bg-gray-600 text-gray-300'
                    }`}>
                      {project.status}
                    </span>
                  </div>

                  {/* Content */}
                  <div className="flex-1">
                    <h3 className={`text-2xl font-bold mb-4 ${
                      project.vault ? 'text-yellow-400' : 'text-white'
                    }`}>
                      {project.name}
                    </h3>
                    
                    <p className={`text-base leading-relaxed mb-6 ${
                      project.vault ? 'text-yellow-400/90' : 'text-gray-300'
                    }`}>
                      {project.description}
                    </p>

                    {/* Features */}
                    <div className="mb-6">
                      <h4 className={`text-sm font-semibold mb-3 ${
                        project.vault ? 'text-yellow-400' : 'text-white'
                      }`}>
                        Key Features
                      </h4>
                      <ul className="space-y-2">
                        {project.features.map((feature) => (
                          <li key={feature} className={`text-sm flex items-center gap-2 ${
                            project.vault ? 'text-yellow-400/80' : 'text-gray-400'
                          }`}>
                            <div className={`w-1.5 h-1.5 rounded-full ${
                              project.vault ? 'bg-yellow-400' : 'bg-blue-400'
                            }`} />
                            {feature}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>

                  {/* CTA */}
                  <Button
                    asChild
                    className={`w-full font-semibold group ${
                      project.vault 
                        ? 'bg-yellow-400 text-black hover:bg-yellow-300' 
                        : project.gradient 
                          ? 'gradient-metamorphic text-white'
                          : 'bg-blue-600 hover:bg-blue-700 text-white'
                    }`}
                  >
                    <Link 
                      href={project.url}
                      target={project.url.startsWith('http') ? '_blank' : undefined}
                      rel={project.url.startsWith('http') ? 'noopener noreferrer' : undefined}
                      className="flex items-center justify-center gap-2"
                    >
                      {project.status === 'Active' ? 'Access System' : 'Learn More'}
                      {project.url.startsWith('http') ? (
                        <ExternalLink className="h-4 w-4 transition-transform group-hover:translate-x-1" />
                      ) : (
                        <ArrowRight className="h-4 w-4 transition-transform group-hover:translate-x-1" />
                      )}
                    </Link>
                  </Button>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Call to action */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
          transition={{ duration: 0.8, delay: 0.8 }}
          className="mt-16 text-center"
        >
          <Button
            asChild
            variant="outline"
            size="lg"
            className="border-gray-600 text-white hover:bg-gray-800"
          >
            <Link href="/systems" className="flex items-center gap-2">
              View All Systems
              <ArrowRight className="h-4 w-4" />
            </Link>
          </Button>
        </motion.div>
      </div>
    </section>
  );
}
