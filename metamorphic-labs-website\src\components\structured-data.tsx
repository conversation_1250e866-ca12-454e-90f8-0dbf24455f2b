interface StructuredDataProps {
  data: object;
}

export function StructuredData({ data }: StructuredDataProps) {
  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(data) }}
    />
  );
}

export const organizationSchema = {
  "@context": "https://schema.org",
  "@type": "Organization",
  "name": "Metamorphic Labs",
  "description": "Metamorphic Labs pioneers next-generation AI systems, multi-agent orchestration, generative creativity, and boundary-pushing software solutions.",
  "url": "https://metamorphiclabs.ai",
  "logo": "https://metamorphiclabs.ai/logo.png",
  "foundingDate": "2024",
  "industry": "Artificial Intelligence",
  "address": {
    "@type": "PostalAddress",
    "addressCountry": "US"
  },
  "contactPoint": {
    "@type": "ContactPoint",
    "email": "<EMAIL>",
    "contactType": "customer service"
  },
  "sameAs": [
    "https://catalyst.metamorphiclabs.ai",
    "https://vault024.metamorphiclabs.ai"
  ],
  "hasOfferCatalog": {
    "@type": "OfferCatalog",
    "name": "AI Systems and Services",
    "itemListElement": [
      {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "Catalyst",
          "description": "Advanced prompt engineering platform for optimizing AI interactions"
        }
      },
      {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "Metamorphic Reactor",
          "description": "Multi-agent orchestration system for complex AI workflows"
        }
      },
      {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "Vault 024",
          "description": "Decentralized art gallery and NFT platform"
        }
      }
    ]
  }
};

export const websiteSchema = {
  "@context": "https://schema.org",
  "@type": "WebSite",
  "name": "Metamorphic Labs",
  "url": "https://metamorphiclabs.ai",
  "description": "Redefining Reality with AI, Quantum Systems & Intelligent Software",
  "publisher": {
    "@type": "Organization",
    "name": "Metamorphic Labs"
  },
  "potentialAction": {
    "@type": "SearchAction",
    "target": "https://metamorphiclabs.ai/search?q={search_term_string}",
    "query-input": "required name=search_term_string"
  }
};
