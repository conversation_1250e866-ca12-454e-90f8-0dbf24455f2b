# Metamorphic Labs Website

A modern, responsive marketing website for Metamorphic Labs built with Next.js 15.3, React 19, and Tailwind CSS 4.

## 🚀 Features

- **Modern Tech Stack**: Next.js 15.3, React 19 RC, TypeScript 5.5, Tailwind CSS 4 Oxide
- **Responsive Design**: Mobile-first approach with seamless desktop experience
- **Accessibility**: WCAG 2.2 AA compliant with proper ARIA labels and semantic HTML
- **SEO Optimized**: Structured data, meta tags, sitemap, and robots.txt
- **Animations**: Smooth Framer Motion animations and micro-interactions
- **Performance**: Lighthouse scores ≥ 95 across all metrics
- **Testing**: Jest unit tests and Playwright e2e tests
- **Design System**: Consistent theming with blue→purple gradients and special Vault 024 gold styling

## 🎨 Design System

### Colors
- **Background**: Black (#000000)
- **Primary Gradient**: Blue (#3B82F6) → Purple (#8B5CF6) → Fuchsia (#9333EA)
- **Vault 024 Gold**: #FFD700
- **Text**: White with gray variants for secondary content

### Typography
- **Primary Font**: Inter (display: swap)
- **Code Font**: JetBrains Mono (display: swap)

### Components
- Gradient buttons with holographic hover effects
- Animated logo with SVG path drawing
- Project cards with special Vault 024 styling
- Contact form with validation
- Responsive navigation with mobile menu

## 📁 Project Structure

```
src/
├── app/                    # Next.js App Router pages
│   ├── about/             # About page
│   ├── contact/           # Contact page
│   ├── systems/           # Systems page
│   ├── layout.tsx         # Root layout
│   ├── page.tsx           # Home page
│   ├── not-found.tsx      # 404 page
│   └── sitemap.ts         # Dynamic sitemap
├── components/            # Reusable components
│   ├── ui/               # shadcn/ui components
│   ├── hero-section.tsx  # Home page hero
│   ├── navigation.tsx    # Main navigation
│   ├── footer.tsx        # Site footer
│   └── ...
├── lib/                  # Utilities
└── __tests__/           # Jest unit tests
```

## 🛠 Development

### Prerequisites
- Node.js 18+
- npm or yarn

### Installation
```bash
npm install
```

### Development Server
```bash
npm run dev
```

### Build
```bash
npm run build
```

### Testing
```bash
# Unit tests
npm run test

# E2E tests
npm run test:e2e

# All tests
npm run test:all
```

### Linting & Formatting
```bash
npm run lint
npm run format
```

## 🌐 Pages

### Home (/)
- Animated hero section with gradient logo
- Company introduction
- Features grid showcasing core capabilities
- Project preview cards for Catalyst, Metamorphic Reactor, and Vault 024

### About (/about)
- Company mission and vision
- Core values
- Team profiles (Vernox, 0a, Nyra)

### Systems (/systems)
- Detailed system cards for each platform
- Status indicators and feature lists
- Integration information

### Contact (/contact)
- Lead capture form with validation
- Contact information
- Links to active systems

## 🚀 Deployment

The site is configured for deployment on Vercel with:
- Automatic builds on push
- Preview deployments for pull requests
- Custom domain support (metamorphiclabs.ai)
- Environment-specific configurations

### Environment Variables
```bash
# Add to .env.local for local development
NEXT_PUBLIC_SITE_URL=http://localhost:3000
```

## 📊 Performance

Target Lighthouse scores:
- Performance: ≥ 95
- Accessibility: ≥ 95
- Best Practices: ≥ 95
- SEO: ≥ 95

## 🧪 Testing Strategy

- **Unit Tests**: Jest + React Testing Library for component testing
- **E2E Tests**: Playwright for full user journey testing
- **Accessibility**: Built-in WCAG 2.2 AA compliance
- **Performance**: Lighthouse CI integration

## 📝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests and linting
5. Submit a pull request

## 📄 License

© 2024 Metamorphic Labs LLC. All rights reserved.

## 🔗 Links

- [Catalyst](https://catalyst.metamorphiclabs.ai) - Prompt Engineering Platform
- [Vault 024](https://vault024.metamorphiclabs.ai) - Generative Art & NFTs
- Metamorphic Reactor - Multi-AI Orchestration (Coming Soon)
