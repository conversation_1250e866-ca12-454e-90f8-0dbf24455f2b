(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{285:(e,t,a)=>{"use strict";a.d(t,{$:()=>o});var i=a(5155);a(2115);var s=a(4624),r=a(2085),n=a(9434);let l=(0,r.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline",gradient:"gradient-metamorphic text-white shadow-xs hover:opacity-90 transition-opacity"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:a,size:r,asChild:o=!1,...c}=e,d=o?s.DX:"button";return(0,i.jsx)(d,{"data-slot":"button",className:(0,n.cn)(l({variant:a,size:r,className:t})),...c})}},1011:(e,t,a)=>{"use strict";a.d(t,{ProjectsPreview:()=>y});var i=a(5155),s=a(6874),r=a.n(s),n=a(230),l=a(6604),o=a(2115),c=a(285),d=a(6695),x=a(3311),m=a(1539),h=a(3127);let p=(0,a(9946).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]);var u=a(2138);let g=[{name:"Catalyst",description:"Advanced prompt engineering platform for optimizing AI interactions and developing sophisticated prompt ecosystems.",status:"Active",url:"https://catalyst.metamorphiclabs.ai",icon:x.A,gradient:!0,features:["Prompt Optimization","AI Model Integration","Performance Analytics"]},{name:"Metamorphic Reactor",description:"Multi-agent orchestration system enabling complex AI workflows and intelligent automation across diverse domains.",status:"Development",url:"#metamorphic-reactor",icon:m.A,gradient:!0,features:["Multi-Agent Coordination","Workflow Automation","Intelligent Routing"]},{name:"Vault 024",description:"Decentralized art gallery and NFT platform showcasing generative creativity and blockchain-based digital assets.",status:"Active",url:"https://vault024.metamorphiclabs.ai",icon:h.A,vault:!0,features:["Generative Art","NFT Marketplace","Blockchain Integration"]}];function y(){let e=(0,o.useRef)(null),t=(0,l.W)(e,{once:!0,margin:"-100px"});return(0,i.jsx)("section",{ref:e,className:"py-24 sm:py-32",children:(0,i.jsxs)("div",{className:"mx-auto max-w-7xl px-6 lg:px-8",children:[(0,i.jsxs)(n.P.div,{initial:{opacity:0,y:20},animate:t?{opacity:1,y:0}:{opacity:0,y:20},transition:{duration:.8},className:"mx-auto max-w-2xl text-center mb-16",children:[(0,i.jsxs)("h2",{className:"text-3xl font-bold tracking-tight text-white sm:text-4xl lg:text-5xl",children:["Our ",(0,i.jsx)("span",{className:"gradient-text",children:"Systems"})," in Action"]}),(0,i.jsx)("p",{className:"mt-6 text-lg leading-8 text-gray-300",children:"Explore our cutting-edge platforms that are reshaping the landscape of AI and creative technology"})]}),(0,i.jsx)("div",{className:"grid grid-cols-1 gap-8 lg:grid-cols-3",children:g.map((e,a)=>(0,i.jsx)(n.P.div,{initial:{opacity:0,y:20},animate:t?{opacity:1,y:0}:{opacity:0,y:20},transition:{duration:.8,delay:.2*a},children:(0,i.jsx)(d.Zp,{className:"holographic-hover h-full ".concat(e.vault?"vault-card":"bg-gray-900 border-gray-800"),children:(0,i.jsxs)(d.Wu,{className:"p-8 h-full flex flex-col",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,i.jsx)("div",{className:"p-3 rounded-lg ".concat(e.vault?"bg-black border border-yellow-400":"bg-gray-800"),children:(0,i.jsx)(e.icon,{className:"h-6 w-6 ".concat(e.vault?"text-yellow-400":e.gradient?"text-blue-400":"text-white")})}),(0,i.jsx)("span",{className:"px-3 py-1 rounded-full text-xs font-medium ".concat("Active"===e.status?e.vault?"bg-yellow-400 text-black":"bg-green-500 text-white":"bg-gray-600 text-gray-300"),children:e.status})]}),(0,i.jsxs)("div",{className:"flex-1",children:[(0,i.jsx)("h3",{className:"text-2xl font-bold mb-4 ".concat(e.vault?"text-yellow-400":"text-white"),children:e.name}),(0,i.jsx)("p",{className:"text-base leading-relaxed mb-6 ".concat(e.vault?"text-yellow-400/90":"text-gray-300"),children:e.description}),(0,i.jsxs)("div",{className:"mb-6",children:[(0,i.jsx)("h4",{className:"text-sm font-semibold mb-3 ".concat(e.vault?"text-yellow-400":"text-white"),children:"Key Features"}),(0,i.jsx)("ul",{className:"space-y-2",children:e.features.map(t=>(0,i.jsxs)("li",{className:"text-sm flex items-center gap-2 ".concat(e.vault?"text-yellow-400/80":"text-gray-400"),children:[(0,i.jsx)("div",{className:"w-1.5 h-1.5 rounded-full ".concat(e.vault?"bg-yellow-400":"bg-blue-400")}),t]},t))})]})]}),(0,i.jsx)(c.$,{asChild:!0,className:"w-full font-semibold group ".concat(e.vault?"bg-yellow-400 text-black hover:bg-yellow-300":e.gradient?"gradient-metamorphic text-white":"bg-blue-600 hover:bg-blue-700 text-white"),children:(0,i.jsxs)(r(),{href:e.url,target:e.url.startsWith("http")?"_blank":void 0,rel:e.url.startsWith("http")?"noopener noreferrer":void 0,className:"flex items-center justify-center gap-2",children:["Active"===e.status?"Access System":"Learn More",e.url.startsWith("http")?(0,i.jsx)(p,{className:"h-4 w-4 transition-transform group-hover:translate-x-1"}):(0,i.jsx)(u.A,{className:"h-4 w-4 transition-transform group-hover:translate-x-1"})]})})]})})},e.name))}),(0,i.jsx)(n.P.div,{initial:{opacity:0,y:20},animate:t?{opacity:1,y:0}:{opacity:0,y:20},transition:{duration:.8,delay:.8},className:"mt-16 text-center",children:(0,i.jsx)(c.$,{asChild:!0,variant:"outline",size:"lg",className:"border-gray-600 text-white hover:bg-gray-800",children:(0,i.jsxs)(r(),{href:"/systems",className:"flex items-center gap-2",children:["View All Systems",(0,i.jsx)(u.A,{className:"h-4 w-4"})]})})})]})})}},1539:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});let i=(0,a(9946).A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},2138:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});let i=(0,a(9946).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},2489:(e,t,a)=>{"use strict";a.d(t,{MetamorphicLogo:()=>n});var i=a(5155),s=a(230),r=a(9434);function n(e){let{className:t,animated:a=!1}=e;return(0,i.jsxs)(s.P.svg,{className:(0,r.cn)("text-white",t),viewBox:"0 0 200 40",fill:"none",xmlns:"http://www.w3.org/2000/svg",initial:!!a&&{opacity:0,scale:.8},animate:!!a&&{opacity:1,scale:1},transition:!!a&&{duration:.8,ease:"easeOut"},children:[(0,i.jsx)("defs",{children:(0,i.jsxs)("linearGradient",{id:"gradient-stroke",x1:"0%",y1:"0%",x2:"100%",y2:"0%",children:[(0,i.jsx)("stop",{offset:"0%",stopColor:"#3B82F6",children:a&&(0,i.jsx)("animate",{attributeName:"stop-color",values:"#3B82F6;#8B5CF6;#9333EA;#3B82F6",dur:"3s",repeatCount:"indefinite"})}),(0,i.jsx)("stop",{offset:"50%",stopColor:"#8B5CF6"}),(0,i.jsx)("stop",{offset:"100%",stopColor:"#9333EA",children:a&&(0,i.jsx)("animate",{attributeName:"stop-color",values:"#9333EA;#3B82F6;#8B5CF6;#9333EA",dur:"3s",repeatCount:"indefinite"})})]})}),(0,i.jsxs)(s.P.g,{initial:!!a&&{opacity:0},animate:!!a&&{opacity:1},transition:!!a&&{delay:.3,duration:.6},children:[(0,i.jsx)(s.P.path,{d:"M10 20 L25 5 L40 20 L25 35 Z",stroke:"url(#gradient-stroke)",strokeWidth:"2",fill:"none",initial:!!a&&{pathLength:0},animate:!!a&&{pathLength:1},transition:!!a&&{delay:.5,duration:1.5,ease:"easeInOut"}}),(0,i.jsx)(s.P.circle,{cx:"25",cy:"20",r:"3",fill:"url(#gradient-stroke)",initial:!!a&&{scale:0},animate:!!a&&{scale:1},transition:!!a&&{delay:1.8,duration:.4,ease:"easeOut"}})]}),(0,i.jsx)(s.P.text,{x:"55",y:"16",className:"fill-current text-sm font-bold tracking-wide",style:{fontFamily:"Inter, sans-serif"},initial:!!a&&{opacity:0,x:-10},animate:!!a&&{opacity:1,x:0},transition:!!a&&{delay:.8,duration:.6},children:"METAMORPHIC"}),(0,i.jsx)(s.P.text,{x:"55",y:"30",className:"fill-current text-xs font-medium tracking-wider opacity-80",style:{fontFamily:"Inter, sans-serif"},initial:!!a&&{opacity:0,x:-10},animate:!!a&&{opacity:1,x:0},transition:!!a&&{delay:1,duration:.6},children:"LABS"})]})}},3127:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});let i=(0,a(9946).A)("palette",[["path",{d:"M12 22a1 1 0 0 1 0-20 10 9 0 0 1 10 9 5 5 0 0 1-5 5h-2.25a1.75 1.75 0 0 0-1.4 2.8l.3.4a1.75 1.75 0 0 1-1.4 2.8z",key:"e79jfc"}],["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}]])},3311:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});let i=(0,a(9946).A)("sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]])},4204:(e,t,a)=>{"use strict";a.d(t,{IntroSection:()=>l});var i=a(5155),s=a(230),r=a(6604),n=a(2115);function l(){let e=(0,n.useRef)(null),t=(0,r.W)(e,{once:!0,margin:"-100px"});return(0,i.jsx)("section",{ref:e,className:"py-24 sm:py-32",children:(0,i.jsx)("div",{className:"mx-auto max-w-7xl px-6 lg:px-8",children:(0,i.jsxs)(s.P.div,{initial:{opacity:0,y:20},animate:t?{opacity:1,y:0}:{opacity:0,y:20},transition:{duration:.8},className:"mx-auto max-w-4xl text-center",children:[(0,i.jsxs)("h2",{className:"text-3xl font-bold tracking-tight text-white sm:text-4xl lg:text-5xl mb-8",children:["Pioneering the ",(0,i.jsx)("span",{className:"gradient-text",children:"Future"})," of Intelligence"]}),(0,i.jsxs)("div",{className:"text-lg leading-8 text-gray-300 space-y-6",children:[(0,i.jsx)("p",{children:"Metamorphic Labs pioneers next-generation AI systems, multi-agent orchestration, generative creativity, and boundary-pushing software solutions. From deep prompt ecosystems to decentralized art galleries—we build the future."}),(0,i.jsx)("p",{children:"Our cutting-edge platforms combine artificial intelligence, quantum computing principles, and innovative software architecture to create solutions that were previously impossible. We're not just building tools; we're crafting the foundation for tomorrow's digital ecosystem."})]}),(0,i.jsxs)(s.P.div,{initial:{opacity:0,y:20},animate:t?{opacity:1,y:0}:{opacity:0,y:20},transition:{duration:.8,delay:.2},className:"mt-16 grid grid-cols-1 gap-8 sm:grid-cols-3",children:[(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"text-3xl font-bold gradient-text mb-2",children:"3"}),(0,i.jsx)("div",{className:"text-sm text-gray-400",children:"Active Systems"})]}),(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"text-3xl font-bold gradient-text mb-2",children:"∞"}),(0,i.jsx)("div",{className:"text-sm text-gray-400",children:"Possibilities"})]}),(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"text-3xl font-bold gradient-text mb-2",children:"24/7"}),(0,i.jsx)("div",{className:"text-sm text-gray-400",children:"Innovation"})]})]})]})})})}},4622:(e,t,a)=>{"use strict";a.d(t,{HeroSection:()=>m});var i=a(5155),s=a(6874),r=a.n(s),n=a(230),l=a(285),o=a(2489),c=a(3311),d=a(2138),x=a(1539);function m(){return(0,i.jsxs)("section",{className:"relative min-h-screen flex items-center justify-center overflow-hidden",children:[(0,i.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-black via-gray-900 to-black"}),(0,i.jsxs)("div",{className:"absolute inset-0",children:[(0,i.jsx)("div",{className:"absolute top-1/4 left-1/4 w-64 h-64 bg-blue-500/10 rounded-full blur-3xl animate-pulse"}),(0,i.jsx)("div",{className:"absolute bottom-1/4 right-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-pulse delay-1000"})]}),(0,i.jsx)("div",{className:"relative z-10 mx-auto max-w-7xl px-6 py-24 sm:py-32 lg:px-8",children:(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"mb-8",children:(0,i.jsx)(o.MetamorphicLogo,{className:"h-16 w-auto mx-auto mb-6",animated:!0})}),(0,i.jsxs)(n.P.h1,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.2},className:"text-4xl font-bold tracking-tight text-white sm:text-6xl lg:text-7xl",children:["Redefining Reality with"," ",(0,i.jsx)("span",{className:"gradient-text",children:"AI"}),","," ",(0,i.jsx)("span",{className:"gradient-text",children:"Quantum Systems"})," &"," ",(0,i.jsx)("span",{className:"gradient-text",children:"Intelligent Software"})]}),(0,i.jsx)(n.P.p,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.4},className:"mt-6 text-lg leading-8 text-gray-300 max-w-3xl mx-auto",children:"Metamorphic Labs pioneers next-generation AI systems, multi-agent orchestration, generative creativity, and boundary-pushing software solutions. From deep prompt ecosystems to decentralized art galleries—we build the future."}),(0,i.jsxs)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.6},className:"mt-10 flex flex-col sm:flex-row items-center justify-center gap-4",children:[(0,i.jsx)(l.$,{asChild:!0,variant:"gradient",size:"lg",className:"holographic-hover group",children:(0,i.jsxs)(r(),{href:"https://catalyst.metamorphiclabs.ai",target:"_blank",rel:"noopener noreferrer",className:"flex items-center gap-2",children:[(0,i.jsx)(c.A,{className:"h-5 w-5"}),"Explore Catalyst",(0,i.jsx)(d.A,{className:"h-4 w-4 transition-transform group-hover:translate-x-1"})]})}),(0,i.jsx)(l.$,{asChild:!0,variant:"outline",size:"lg",className:"border-gray-600 text-white hover:bg-gray-800 holographic-hover group",children:(0,i.jsxs)(r(),{href:"#metamorphic-reactor",className:"flex items-center gap-2",children:[(0,i.jsx)(x.A,{className:"h-5 w-5"}),"Learn About Metamorphic Reactor",(0,i.jsx)(d.A,{className:"h-4 w-4 transition-transform group-hover:translate-x-1"})]})}),(0,i.jsx)(l.$,{asChild:!0,className:"bg-black border border-yellow-400 text-yellow-400 hover:bg-yellow-400 hover:text-black holographic-hover group",size:"lg",children:(0,i.jsxs)(r(),{href:"https://vault024.metamorphiclabs.ai",target:"_blank",rel:"noopener noreferrer",className:"flex items-center gap-2",children:[(0,i.jsx)("span",{className:"text-xs",children:"✦"}),"Visit Vault 024",(0,i.jsx)(d.A,{className:"h-4 w-4 transition-transform group-hover:translate-x-1"})]})})]}),(0,i.jsx)(n.P.div,{initial:{opacity:0},animate:{opacity:1},transition:{duration:1,delay:1},className:"mt-16",children:(0,i.jsxs)("div",{className:"flex flex-col items-center",children:[(0,i.jsx)("p",{className:"text-sm text-gray-400 mb-4",children:"Discover our ecosystem"}),(0,i.jsx)(n.P.div,{animate:{y:[0,8,0]},transition:{duration:2,repeat:1/0},className:"w-6 h-10 border-2 border-gray-600 rounded-full flex justify-center",children:(0,i.jsx)("div",{className:"w-1 h-3 bg-gradient-to-b from-blue-400 to-purple-400 rounded-full mt-2"})})]})})]})})]})}},6604:(e,t,a)=>{"use strict";a.d(t,{W:()=>n});var i=a(2115),s=a(2198);let r={some:0,all:1};function n(e,{root:t,margin:a,amount:l,once:o=!1,initial:c=!1}={}){let[d,x]=(0,i.useState)(c);return(0,i.useEffect)(()=>{if(!e.current||o&&d)return;let i={root:t&&t.current||void 0,margin:a,amount:l};return function(e,t,{root:a,margin:i,amount:n="some"}={}){let l=(0,s.K)(e),o=new WeakMap,c=new IntersectionObserver(e=>{e.forEach(e=>{let a=o.get(e.target);if(!!a!==e.isIntersecting)if(e.isIntersecting){let a=t(e.target,e);"function"==typeof a?o.set(e.target,a):c.unobserve(e.target)}else"function"==typeof a&&(a(e),o.delete(e.target))})},{root:a,rootMargin:i,threshold:"number"==typeof n?n:r[n]});return l.forEach(e=>c.observe(e)),()=>c.disconnect()}(e.current,()=>(x(!0),o?void 0:()=>x(!1)),i)},[t,e,a,o,l]),d}},6695:(e,t,a)=>{"use strict";a.d(t,{Wu:()=>n,Zp:()=>r});var i=a(5155);a(2115);var s=a(9434);function r(e){let{className:t,...a}=e;return(0,i.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...a})}function n(e){let{className:t,...a}=e;return(0,i.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",t),...a})}},6888:(e,t,a)=>{"use strict";a.d(t,{FeaturesGrid:()=>h});var i=a(5155),s=a(230),r=a(6604),n=a(2115),l=a(9946);let o=(0,l.A)("brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]]),c=(0,l.A)("network",[["rect",{x:"16",y:"16",width:"6",height:"6",rx:"1",key:"4q2zg0"}],["rect",{x:"2",y:"16",width:"6",height:"6",rx:"1",key:"8cvhb9"}],["rect",{x:"9",y:"2",width:"6",height:"6",rx:"1",key:"1egb70"}],["path",{d:"M5 16v-3a1 1 0 0 1 1-1h12a1 1 0 0 1 1 1v3",key:"1jsf9p"}],["path",{d:"M12 12V8",key:"2874zd"}]]);var d=a(3127),x=a(1539);let m=[{name:"Metamorphic AI Ecosystem",description:"Comprehensive AI platform integrating multiple models and agents for complex problem-solving and creative tasks.",icon:o},{name:"Multi-AI Orchestration",description:"Advanced orchestration system enabling seamless collaboration between different AI models and agents.",icon:c},{name:"Generative Art + NFTs",description:"Cutting-edge platform for creating, showcasing, and trading AI-generated art and digital collectibles.",icon:d.A},{name:"Prompt Engineering",description:"Sophisticated tools and methodologies for optimizing AI interactions and developing prompt ecosystems.",icon:x.A}];function h(){let e=(0,n.useRef)(null),t=(0,r.W)(e,{once:!0,margin:"-100px"});return(0,i.jsx)("section",{ref:e,className:"py-24 sm:py-32 bg-gray-900/20",children:(0,i.jsxs)("div",{className:"mx-auto max-w-7xl px-6 lg:px-8",children:[(0,i.jsxs)(s.P.div,{initial:{opacity:0,y:20},animate:t?{opacity:1,y:0}:{opacity:0,y:20},transition:{duration:.8},className:"mx-auto max-w-2xl text-center mb-16",children:[(0,i.jsxs)("h2",{className:"text-3xl font-bold tracking-tight text-white sm:text-4xl lg:text-5xl",children:["Our ",(0,i.jsx)("span",{className:"gradient-text",children:"Core Capabilities"})]}),(0,i.jsx)("p",{className:"mt-6 text-lg leading-8 text-gray-300",children:"Discover the technologies and methodologies that power our innovative solutions"})]}),(0,i.jsx)("div",{className:"grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4",children:m.map((e,a)=>(0,i.jsx)(s.P.div,{initial:{opacity:0,y:20},animate:t?{opacity:1,y:0}:{opacity:0,y:20},transition:{duration:.8,delay:.1*a},className:"group relative",children:(0,i.jsxs)("div",{className:"holographic-hover rounded-2xl bg-gray-900 p-8 border border-gray-800 h-full",children:[(0,i.jsx)("div",{className:"flex items-center justify-center w-12 h-12 rounded-lg bg-gradient-to-br from-blue-500 to-purple-600 mb-6",children:(0,i.jsx)(e.icon,{className:"h-6 w-6 text-white"})}),(0,i.jsx)("h3",{className:"text-xl font-semibold text-white mb-4 group-hover:gradient-text transition-all duration-300",children:e.name}),(0,i.jsx)("p",{className:"text-gray-300 leading-relaxed",children:e.description}),(0,i.jsx)("div",{className:"absolute inset-0 rounded-2xl bg-gradient-to-br from-blue-500/5 to-purple-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"})]})},e.name))}),(0,i.jsx)(s.P.div,{initial:{opacity:0,y:20},animate:t?{opacity:1,y:0}:{opacity:0,y:20},transition:{duration:.8,delay:.6},className:"mt-16 text-center",children:(0,i.jsxs)("div",{className:"rounded-2xl bg-gradient-to-r from-blue-900/20 to-purple-900/20 p-8 border border-gray-800",children:[(0,i.jsx)("h3",{className:"text-2xl font-bold text-white mb-4",children:"Integrated Innovation"}),(0,i.jsx)("p",{className:"text-gray-300 max-w-3xl mx-auto",children:"Our features work together seamlessly, creating a powerful ecosystem where AI orchestration, prompt engineering, and creative generation combine to deliver unprecedented capabilities. Each component enhances the others, resulting in solutions that are greater than the sum of their parts."})]})})]})})}},7971:(e,t,a)=>{Promise.resolve().then(a.bind(a,6888)),Promise.resolve().then(a.bind(a,4622)),Promise.resolve().then(a.bind(a,4204)),Promise.resolve().then(a.bind(a,1011))},9434:(e,t,a)=>{"use strict";a.d(t,{cn:()=>r});var i=a(2596),s=a(9688);function r(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,s.QP)((0,i.$)(t))}}},e=>{var t=t=>e(e.s=t);e.O(0,[497,874,230,441,684,358],()=>t(7971)),_N_E=e.O()}]);