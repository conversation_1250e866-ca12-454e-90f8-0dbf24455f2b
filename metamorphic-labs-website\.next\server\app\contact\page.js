(()=>{var e={};e.id=977,e.ids=[977],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3839:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n,metadata:()=>i});var a=r(7413),s=r(9561);let i={title:"Contact Us | Metamorphic Labs",description:"Get in touch with Metamorphic Labs. Whether you have questions about our AI systems, want to collaborate, or need support, we're here to help."};function n(){return(0,a.jsx)("div",{className:"bg-black text-white",children:(0,a.jsxs)("div",{className:"mx-auto max-w-7xl px-6 py-24 sm:py-32 lg:px-8",children:[(0,a.jsxs)("div",{className:"mx-auto max-w-2xl lg:mx-0",children:[(0,a.jsxs)("h1",{className:"text-4xl font-bold tracking-tight text-white sm:text-6xl",children:["Get in ",(0,a.jsx)("span",{className:"gradient-text",children:"Touch"})]}),(0,a.jsx)("p",{className:"mt-6 text-lg leading-8 text-gray-300",children:"Ready to explore the future of AI and quantum systems? We'd love to hear from you."})]}),(0,a.jsxs)("div",{className:"mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-x-8 gap-y-16 lg:mx-0 lg:mt-10 lg:max-w-none lg:grid-cols-2",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-white mb-8",children:"Contact Information"}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"rounded-lg bg-gray-900 p-6 border border-gray-800",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:"General Inquiries"}),(0,a.jsx)("p",{className:"text-gray-300",children:"<EMAIL>"})]}),(0,a.jsxs)("div",{className:"rounded-lg bg-gray-900 p-6 border border-gray-800",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:"Technical Support"}),(0,a.jsx)("p",{className:"text-gray-300",children:"<EMAIL>"})]}),(0,a.jsxs)("div",{className:"rounded-lg bg-gray-900 p-6 border border-gray-800",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:"Partnerships"}),(0,a.jsx)("p",{className:"text-gray-300",children:"<EMAIL>"})]})]}),(0,a.jsxs)("div",{className:"mt-8",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Our Systems"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("a",{href:"https://catalyst.metamorphiclabs.ai",target:"_blank",rel:"noopener noreferrer",className:"block text-blue-400 hover:text-blue-300 transition-colors",children:"→ Catalyst - Prompt Engineering Platform"}),(0,a.jsx)("a",{href:"#metamorphic-reactor",className:"block text-blue-400 hover:text-blue-300 transition-colors",children:"→ Metamorphic Reactor - Multi-AI Orchestration"}),(0,a.jsx)("a",{href:"https://vault024.metamorphiclabs.ai",target:"_blank",rel:"noopener noreferrer",className:"block text-yellow-400 hover:text-yellow-300 transition-colors",children:"→ Vault 024 - Generative Art & NFTs"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-white mb-8",children:"Send us a Message"}),(0,a.jsx)(s.ContactForm,{})]})]})]})})}},3873:e=>{"use strict";e.exports=require("path")},4493:(e,t,r)=>{"use strict";r.d(t,{Wu:()=>n,Zp:()=>i});var a=r(687);r(3210);var s=r(4780);function i({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function n({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",e),...t})}},5069:(e,t,r)=>{Promise.resolve().then(r.bind(r,7780))},5253:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>p,tree:()=>d});var a=r(5239),s=r(8088),i=r(8170),n=r.n(i),o=r(893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["contact",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,3839)),"C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\app\\contact\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,1075)),"C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,4413)),"C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\app\\contact\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/contact/page",pathname:"/contact",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},7780:(e,t,r)=>{"use strict";r.d(t,{ContactForm:()=>x});var a=r(687),s=r(3210),i=r(9523),n=r(4780);function o({className:e,type:t,...r}){return(0,a.jsx)("input",{type:t,"data-slot":"input",className:(0,n.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...r})}function l({className:e,...t}){return(0,a.jsx)("textarea",{"data-slot":"textarea",className:(0,n.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),...t})}var d=r(4493),c=r(2688);let m=(0,c.A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),p=(0,c.A)("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]]),u=["General Inquiry","Catalyst - Prompt Engineering","Metamorphic Reactor - Multi-AI Orchestration","Vault 024 - Generative Art & NFTs","Partnership Opportunities","Technical Support","Investment & Funding","Other"];function x(){let[e,t]=(0,s.useState)({name:"",email:"",interest:"",message:""}),[r,n]=(0,s.useState)(!1),[c,x]=(0,s.useState)(!1),h=async e=>{e.preventDefault(),n(!0),await new Promise(e=>setTimeout(e,2e3)),n(!1),x(!0),setTimeout(()=>{x(!1),t({name:"",email:"",interest:"",message:""})},3e3)},b=e=>{t(t=>({...t,[e.target.name]:e.target.value}))};return c?(0,a.jsx)(d.Zp,{className:"bg-gray-900 border-gray-800",children:(0,a.jsxs)(d.Wu,{className:"p-8 text-center",children:[(0,a.jsx)(m,{className:"h-16 w-16 text-green-500 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-white mb-2",children:"Message Sent!"}),(0,a.jsx)("p",{className:"text-gray-300",children:"Thank you for reaching out. We'll get back to you within 24 hours."})]})}):(0,a.jsx)(d.Zp,{className:"bg-gray-900 border-gray-800",children:(0,a.jsx)(d.Wu,{className:"p-8",children:(0,a.jsxs)("form",{onSubmit:h,className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-white mb-2",children:"Name *"}),(0,a.jsx)(o,{id:"name",name:"name",type:"text",required:!0,value:e.name,onChange:b,className:"bg-gray-800 border-gray-700 text-white placeholder-gray-400 focus:border-blue-500",placeholder:"Your full name"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-white mb-2",children:"Email *"}),(0,a.jsx)(o,{id:"email",name:"email",type:"email",required:!0,value:e.email,onChange:b,className:"bg-gray-800 border-gray-700 text-white placeholder-gray-400 focus:border-blue-500",placeholder:"<EMAIL>"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"interest",className:"block text-sm font-medium text-white mb-2",children:"Area of Interest"}),(0,a.jsxs)("select",{id:"interest",name:"interest",value:e.interest,onChange:b,className:"w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-md text-white focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"",children:"Select an option"}),u.map(e=>(0,a.jsx)("option",{value:e,children:e},e))]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"message",className:"block text-sm font-medium text-white mb-2",children:"Message *"}),(0,a.jsx)(l,{id:"message",name:"message",required:!0,value:e.message,onChange:b,rows:5,className:"bg-gray-800 border-gray-700 text-white placeholder-gray-400 focus:border-blue-500 resize-none",placeholder:"Tell us about your project, questions, or how we can help..."})]}),(0,a.jsx)(i.$,{type:"submit",disabled:r,className:"w-full gradient-metamorphic text-white font-semibold py-3 disabled:opacity-50",children:r?(0,a.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}),"Sending..."]}):(0,a.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,a.jsx)(p,{className:"h-4 w-4"}),"Send Message"]})})]})})})}},7805:(e,t,r)=>{Promise.resolve().then(r.bind(r,9561))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")},9561:(e,t,r)=>{"use strict";r.d(t,{ContactForm:()=>a});let a=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call ContactForm() from the server but ContactForm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\contact-form.tsx","ContactForm")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[447,559,84],()=>r(5253));module.exports=a})();