import{toNestErrors as e,validateFieldsNatively as r}from"@hookform/resolvers";import{appendErrors as t}from"react-hook-form";var n=function(n,o,a){return void 0===o&&(o={abortEarly:!1}),void 0===a&&(a={}),function(i,s,u){try{var c=function(){return l.error?{values:{},errors:e((n=l.error,o=!u.shouldUseNativeValidation&&"all"===u.criteriaMode,n.details.length?n.details.reduce(function(e,r){var n=r.path.join(".");if(e[n]||(e[n]={message:r.message,type:r.type}),o){var a=e[n].types,i=a&&a[r.type];e[n]=t(n,o,e,r.type,i?[].concat(i,r.message):r.message)}return e},{}):{}),u)}:(u.shouldUseNativeValidation&&r({},u),{errors:{},values:l.value});var n,o},v=Object.assign({},o,{context:s}),l={},f=function(){if("sync"===a.mode)l=n.validate(i,v);else{var e=function(e,r){try{var t=e()}catch(e){return r(e)}return t&&t.then?t.then(void 0,r):t}(function(){return Promise.resolve(n.validateAsync(i,v)).then(function(e){l.value=e})},function(e){l.error=e});if(e&&e.then)return e.then(function(){})}}();return Promise.resolve(f&&f.then?f.then(c):c())}catch(e){return Promise.reject(e)}}};export{n as joiResolver};
//# sourceMappingURL=joi.module.js.map
