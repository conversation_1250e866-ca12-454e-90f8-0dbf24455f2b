exports.id=84,exports.ids=[84],exports.modules={283:(e,t,s)=>{"use strict";s.d(t,{MetamorphicLogo:()=>i});let i=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call Metamorphic<PERSON>ogo() from the server but Metamorphic<PERSON>ogo is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\metamorphic-logo.tsx","MetamorphicLogo")},440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var i=s(1658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,i.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},940:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,4536,23)),Promise.resolve().then(s.bind(s,283)),Promise.resolve().then(s.bind(s,4544))},1075:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>u,metadata:()=>g,viewport:()=>p});var i=s(7413),a=s(5230),r=s.n(a),n=s(4537),o=s.n(n);s(1135);var l=s(4544),d=s(4536),c=s.n(d),m=s(283);let h={main:[{name:"Home",href:"/"},{name:"About",href:"/about"},{name:"Systems",href:"/systems"},{name:"Contact",href:"/contact"}],systems:[{name:"Catalyst",href:"https://catalyst.metamorphiclabs.ai"},{name:"Metamorphic Reactor",href:"#metamorphic-reactor"},{name:"Vault 024",href:"https://vault024.metamorphiclabs.ai"}],legal:[{name:"Privacy Policy",href:"/privacy"},{name:"Terms of Service",href:"/terms"}]};function x(){return(0,i.jsxs)("footer",{className:"bg-black border-t border-gray-800","aria-labelledby":"footer-heading",children:[(0,i.jsx)("h2",{id:"footer-heading",className:"sr-only",children:"Footer"}),(0,i.jsxs)("div",{className:"mx-auto max-w-7xl px-6 pb-8 pt-16 sm:pt-24 lg:px-8 lg:pt-32",children:[(0,i.jsxs)("div",{className:"xl:grid xl:grid-cols-3 xl:gap-8",children:[(0,i.jsxs)("div",{className:"space-y-8",children:[(0,i.jsx)(m.MetamorphicLogo,{className:"h-8 w-auto"}),(0,i.jsx)("p",{className:"text-sm leading-6 text-gray-300",children:"Redefining Reality with AI, Quantum Systems & Intelligent Software"}),(0,i.jsx)("p",{className:"text-xs leading-5 text-gray-400",children:"Metamorphic Labs pioneers next-generation AI systems, multi-agent orchestration, generative creativity, and boundary-pushing software solutions."})]}),(0,i.jsxs)("div",{className:"mt-16 grid grid-cols-2 gap-8 xl:col-span-2 xl:mt-0",children:[(0,i.jsxs)("div",{className:"md:grid md:grid-cols-2 md:gap-8",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-sm font-semibold leading-6 text-white",children:"Navigation"}),(0,i.jsx)("ul",{role:"list",className:"mt-6 space-y-4",children:h.main.map(e=>(0,i.jsx)("li",{children:(0,i.jsx)(c(),{href:e.href,className:"text-sm leading-6 text-gray-300 hover:text-white transition-colors",children:e.name})},e.name))})]}),(0,i.jsxs)("div",{className:"mt-10 md:mt-0",children:[(0,i.jsx)("h3",{className:"text-sm font-semibold leading-6 text-white",children:"Systems"}),(0,i.jsx)("ul",{role:"list",className:"mt-6 space-y-4",children:h.systems.map(e=>(0,i.jsx)("li",{children:(0,i.jsx)(c(),{href:e.href,className:"text-sm leading-6 text-gray-300 hover:text-white transition-colors",target:e.href.startsWith("http")?"_blank":void 0,rel:e.href.startsWith("http")?"noopener noreferrer":void 0,children:e.name})},e.name))})]})]}),(0,i.jsx)("div",{className:"md:grid md:grid-cols-1 md:gap-8",children:(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-sm font-semibold leading-6 text-white",children:"Legal"}),(0,i.jsx)("ul",{role:"list",className:"mt-6 space-y-4",children:h.legal.map(e=>(0,i.jsx)("li",{children:(0,i.jsx)(c(),{href:e.href,className:"text-sm leading-6 text-gray-300 hover:text-white transition-colors",children:e.name})},e.name))})]})})]})]}),(0,i.jsx)("div",{className:"mt-16 border-t border-gray-800 pt-8 sm:mt-20 lg:mt-24",children:(0,i.jsxs)("div",{className:"flex flex-col items-center justify-between sm:flex-row",children:[(0,i.jsxs)("p",{className:"text-xs leading-5 text-gray-400",children:["\xa9 ",new Date().getFullYear()," Metamorphic Labs LLC. All rights reserved."]}),(0,i.jsx)("p",{className:"mt-4 text-xs leading-5 text-gray-400 sm:mt-0",children:"Built with Next.js, React 19, and Tailwind CSS"})]})})]})]})}let p={width:"device-width",initialScale:1,maximumScale:1},g={title:"Metamorphic Labs | Redefining Reality with AI & Quantum Systems",description:"Metamorphic Labs pioneers next-generation AI systems, multi-agent orchestration, generative creativity, and boundary-pushing software solutions. From deep prompt ecosystems to decentralized art galleries—we build the future.",keywords:["AI","Quantum Systems","Multi-Agent Orchestration","Generative Art","NFTs","Prompt Engineering","Software Development"],authors:[{name:"Metamorphic Labs"}],creator:"Metamorphic Labs",publisher:"Metamorphic Labs",openGraph:{title:"Metamorphic Labs | Redefining Reality with AI & Quantum Systems",description:"Metamorphic Labs pioneers next-generation AI systems, multi-agent orchestration, generative creativity, and boundary-pushing software solutions.",url:"https://metamorphiclabs.ai",siteName:"Metamorphic Labs",type:"website",locale:"en_US"},twitter:{card:"summary_large_image",title:"Metamorphic Labs | Redefining Reality with AI & Quantum Systems",description:"Metamorphic Labs pioneers next-generation AI systems, multi-agent orchestration, generative creativity, and boundary-pushing software solutions."},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}}};function u({children:e}){return(0,i.jsx)("html",{lang:"en",className:"dark",children:(0,i.jsx)("body",{className:`${r().variable} ${o().variable} font-sans antialiased bg-black text-white min-h-screen`,children:(0,i.jsxs)("div",{className:"flex flex-col min-h-screen",children:[(0,i.jsx)(l.Navigation,{}),(0,i.jsx)("main",{className:"flex-1",children:e}),(0,i.jsx)(x,{})]})})})}},1135:()=>{},1357:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6346,23)),Promise.resolve().then(s.t.bind(s,7924,23)),Promise.resolve().then(s.t.bind(s,5656,23)),Promise.resolve().then(s.t.bind(s,99,23)),Promise.resolve().then(s.t.bind(s,8243,23)),Promise.resolve().then(s.t.bind(s,8827,23)),Promise.resolve().then(s.t.bind(s,2763,23)),Promise.resolve().then(s.t.bind(s,7173,23))},2733:(e,t,s)=>{"use strict";s.d(t,{MetamorphicLogo:()=>n});var i=s(687),a=s(7786),r=s(4780);function n({className:e,animated:t=!1}){return(0,i.jsxs)(a.P.svg,{className:(0,r.cn)("text-white",e),viewBox:"0 0 200 40",fill:"none",xmlns:"http://www.w3.org/2000/svg",initial:t?{opacity:0,scale:.8}:void 0,animate:t?{opacity:1,scale:1}:void 0,transition:t?{duration:.8,ease:"easeOut"}:void 0,children:[(0,i.jsx)("defs",{children:(0,i.jsxs)("linearGradient",{id:"gradient-stroke",x1:"0%",y1:"0%",x2:"100%",y2:"0%",children:[(0,i.jsx)("stop",{offset:"0%",stopColor:"#3B82F6",children:t&&(0,i.jsx)("animate",{attributeName:"stop-color",values:"#3B82F6;#8B5CF6;#9333EA;#3B82F6",dur:"3s",repeatCount:"indefinite"})}),(0,i.jsx)("stop",{offset:"50%",stopColor:"#8B5CF6"}),(0,i.jsx)("stop",{offset:"100%",stopColor:"#9333EA",children:t&&(0,i.jsx)("animate",{attributeName:"stop-color",values:"#9333EA;#3B82F6;#8B5CF6;#9333EA",dur:"3s",repeatCount:"indefinite"})})]})}),(0,i.jsxs)(a.P.g,{initial:t?{opacity:0}:void 0,animate:t?{opacity:1}:void 0,transition:t?{delay:.3,duration:.6}:void 0,children:[(0,i.jsx)(a.P.path,{d:"M10 20 L25 5 L40 20 L25 35 Z",stroke:"url(#gradient-stroke)",strokeWidth:"2",fill:"none",initial:t?{pathLength:0}:void 0,animate:t?{pathLength:1}:void 0,transition:t?{delay:.5,duration:1.5,ease:"easeInOut"}:void 0}),(0,i.jsx)(a.P.circle,{cx:"25",cy:"20",r:"3",fill:"url(#gradient-stroke)",initial:t?{scale:0}:void 0,animate:t?{scale:1}:void 0,transition:t?{delay:1.8,duration:.4,ease:"easeOut"}:void 0})]}),(0,i.jsx)(a.P.text,{x:"55",y:"16",className:"fill-current text-sm font-bold tracking-wide",style:{fontFamily:"Inter, sans-serif"},initial:t?{opacity:0,x:-10}:void 0,animate:t?{opacity:1,x:0}:void 0,transition:t?{delay:.8,duration:.6}:void 0,children:"METAMORPHIC"}),(0,i.jsx)(a.P.text,{x:"55",y:"30",className:"fill-current text-xs font-medium tracking-wider opacity-80",style:{fontFamily:"Inter, sans-serif"},initial:t?{opacity:0,x:-10}:void 0,animate:t?{opacity:1,x:0}:void 0,transition:t?{delay:1,duration:.6}:void 0,children:"LABS"})]})}},3299:(e,t,s)=>{Promise.resolve().then(s.bind(s,4413))},4246:(e,t,s)=>{"use strict";s.d(t,{Navigation:()=>x});var i=s(687),a=s(5814),r=s.n(a),n=s(6189),o=s(3210),l=s(2941),d=s(1860),c=s(9523),m=s(2733);let h=[{name:"Home",href:"/"},{name:"About",href:"/about"},{name:"Systems",href:"/systems"},{name:"Contact",href:"/contact"}];function x(){let e=(0,n.usePathname)(),[t,s]=(0,o.useState)(!1);return(0,i.jsxs)("header",{className:"sticky top-0 z-50 w-full border-b border-gray-800 bg-black/80 backdrop-blur-md",children:[(0,i.jsxs)("nav",{className:"mx-auto flex max-w-7xl items-center justify-between p-6 lg:px-8","aria-label":"Main navigation",children:[(0,i.jsx)("div",{className:"flex lg:flex-1",children:(0,i.jsxs)(r(),{href:"/",className:"-m-1.5 p-1.5","aria-label":"Metamorphic Labs home",children:[(0,i.jsx)("span",{className:"sr-only",children:"Metamorphic Labs"}),(0,i.jsx)(m.MetamorphicLogo,{className:"h-8 w-auto"})]})}),(0,i.jsx)("div",{className:"flex lg:hidden",children:(0,i.jsxs)(c.$,{variant:"ghost",className:"-m-2.5 inline-flex items-center justify-center rounded-md p-2.5 text-white",onClick:()=>s(!0),children:[(0,i.jsx)("span",{className:"sr-only",children:"Open main menu"}),(0,i.jsx)(l.A,{className:"h-6 w-6","aria-hidden":"true"})]})}),(0,i.jsx)("div",{className:"hidden lg:flex lg:gap-x-12",children:h.map(t=>(0,i.jsx)(r(),{href:t.href,className:`text-sm font-semibold leading-6 transition-colors hover:text-blue-400 ${e===t.href?"text-blue-400":"text-white"}`,children:t.name},t.name))}),(0,i.jsx)("div",{className:"hidden lg:flex lg:flex-1 lg:justify-end",children:(0,i.jsx)(c.$,{asChild:!0,className:"gradient-metamorphic text-white font-semibold",children:(0,i.jsx)(r(),{href:"/contact",children:"Get Started"})})})]}),t&&(0,i.jsxs)("div",{className:"lg:hidden",children:[(0,i.jsx)("div",{className:"fixed inset-0 z-50"}),(0,i.jsxs)("div",{className:"fixed inset-y-0 right-0 z-50 w-full overflow-y-auto bg-black px-6 py-6 sm:max-w-sm sm:ring-1 sm:ring-gray-800",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)(r(),{href:"/",className:"-m-1.5 p-1.5",children:[(0,i.jsx)("span",{className:"sr-only",children:"Metamorphic Labs"}),(0,i.jsx)(m.MetamorphicLogo,{className:"h-8 w-auto"})]}),(0,i.jsxs)(c.$,{variant:"ghost",className:"-m-2.5 rounded-md p-2.5 text-white",onClick:()=>s(!1),children:[(0,i.jsx)("span",{className:"sr-only",children:"Close menu"}),(0,i.jsx)(d.A,{className:"h-6 w-6","aria-hidden":"true"})]})]}),(0,i.jsx)("div",{className:"mt-6 flow-root",children:(0,i.jsxs)("div",{className:"-my-6 divide-y divide-gray-800",children:[(0,i.jsx)("div",{className:"space-y-2 py-6",children:h.map(t=>(0,i.jsx)(r(),{href:t.href,className:`-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 transition-colors hover:bg-gray-900 ${e===t.href?"text-blue-400":"text-white"}`,onClick:()=>s(!1),children:t.name},t.name))}),(0,i.jsx)("div",{className:"py-6",children:(0,i.jsx)(c.$,{asChild:!0,className:"gradient-metamorphic w-full text-white font-semibold",onClick:()=>s(!1),children:(0,i.jsx)(r(),{href:"/contact",children:"Get Started"})})})]})})]})]})]})}},4413:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});let i=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder (9)\\\\metamorphic-labs-website\\\\src\\\\app\\\\not-found.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\app\\not-found.tsx","default")},4544:(e,t,s)=>{"use strict";s.d(t,{Navigation:()=>i});let i=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call Navigation() from the server but Navigation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\navigation.tsx","Navigation")},4780:(e,t,s)=>{"use strict";s.d(t,{cn:()=>r});var i=s(9384),a=s(2348);function r(...e){return(0,a.QP)((0,i.$)(e))}},7347:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c});var i=s(687),a=s(5814),r=s.n(a),n=s(7786),o=s(9523),l=s(2192),d=s(8559);function c(){return(0,i.jsx)("div",{className:"bg-black text-white min-h-screen flex items-center justify-center",children:(0,i.jsxs)("div",{className:"text-center px-6",children:[(0,i.jsxs)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:[(0,i.jsx)("h1",{className:"text-9xl font-bold gradient-text mb-4",children:"404"}),(0,i.jsx)("h2",{className:"text-3xl font-bold text-white mb-4",children:"Page Not Found"}),(0,i.jsx)("p",{className:"text-lg text-gray-300 mb-8 max-w-md mx-auto",children:"The page you're looking for seems to have vanished into the quantum realm. Let's get you back to familiar territory."}),(0,i.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,i.jsx)(o.$,{asChild:!0,className:"gradient-metamorphic",children:(0,i.jsxs)(r(),{href:"/",className:"flex items-center gap-2",children:[(0,i.jsx)(l.A,{className:"h-4 w-4"}),"Back to Home"]})}),(0,i.jsx)(o.$,{asChild:!0,variant:"outline",className:"border-gray-600 text-white hover:bg-gray-800",children:(0,i.jsxs)(r(),{href:"/contact",className:"flex items-center gap-2",children:[(0,i.jsx)(d.A,{className:"h-4 w-4"}),"Contact Support"]})})]})]}),(0,i.jsx)(n.P.div,{className:"mt-16",initial:{opacity:0},animate:{opacity:1},transition:{delay:.3,duration:.6},children:(0,i.jsx)("p",{className:"text-sm text-gray-400",children:"Error Code: METAMORPHIC_404_QUANTUM_DISPLACEMENT"})})]})})}},7371:(e,t,s)=>{Promise.resolve().then(s.bind(s,7347))},7389:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6444,23)),Promise.resolve().then(s.t.bind(s,6042,23)),Promise.resolve().then(s.t.bind(s,8170,23)),Promise.resolve().then(s.t.bind(s,9477,23)),Promise.resolve().then(s.t.bind(s,9345,23)),Promise.resolve().then(s.t.bind(s,2089,23)),Promise.resolve().then(s.t.bind(s,6577,23)),Promise.resolve().then(s.t.bind(s,1307,23))},9084:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,5814,23)),Promise.resolve().then(s.bind(s,2733)),Promise.resolve().then(s.bind(s,4246))},9523:(e,t,s)=>{"use strict";s.d(t,{$:()=>l});var i=s(687);s(3210);var a=s(1391),r=s(4224),n=s(4780);let o=(0,r.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline",gradient:"gradient-metamorphic text-white shadow-xs hover:opacity-90 transition-opacity"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:t,size:s,asChild:r=!1,...l}){let d=r?a.DX:"button";return(0,i.jsx)(d,{"data-slot":"button",className:(0,n.cn)(o({variant:t,size:s,className:e})),...l})}}};