(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{285:(e,t,a)=>{"use strict";a.d(t,{$:()=>l});var i=a(5155);a(2115);var s=a(4624),r=a(2085),n=a(9434);let o=(0,r.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline",gradient:"gradient-metamorphic text-white shadow-xs hover:opacity-90 transition-opacity"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:t,variant:a,size:r,asChild:l=!1,...d}=e,c=l?s.DX:"button";return(0,i.jsx)(c,{"data-slot":"button",className:(0,n.cn)(o({variant:a,size:r,className:t})),...d})}},347:()=>{},1357:(e,t,a)=>{"use strict";a.d(t,{Navigation:()=>v});var i=a(5155),s=a(6874),r=a.n(s),n=a(8999),o=a(2115),l=a(9946);let d=(0,l.A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]]),c=(0,l.A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);var h=a(285),m=a(2489);let x=[{name:"Home",href:"/"},{name:"About",href:"/about"},{name:"Systems",href:"/systems"},{name:"Contact",href:"/contact"}];function v(){let e=(0,n.usePathname)(),[t,a]=(0,o.useState)(!1);return(0,i.jsxs)("header",{className:"sticky top-0 z-50 w-full border-b border-gray-800 bg-black/80 backdrop-blur-md",children:[(0,i.jsxs)("nav",{className:"mx-auto flex max-w-7xl items-center justify-between p-6 lg:px-8","aria-label":"Main navigation",children:[(0,i.jsx)("div",{className:"flex lg:flex-1",children:(0,i.jsxs)(r(),{href:"/",className:"-m-1.5 p-1.5","aria-label":"Metamorphic Labs home",children:[(0,i.jsx)("span",{className:"sr-only",children:"Metamorphic Labs"}),(0,i.jsx)(m.MetamorphicLogo,{className:"h-8 w-auto"})]})}),(0,i.jsx)("div",{className:"flex lg:hidden",children:(0,i.jsxs)(h.$,{variant:"ghost",className:"-m-2.5 inline-flex items-center justify-center rounded-md p-2.5 text-white",onClick:()=>a(!0),children:[(0,i.jsx)("span",{className:"sr-only",children:"Open main menu"}),(0,i.jsx)(d,{className:"h-6 w-6","aria-hidden":"true"})]})}),(0,i.jsx)("div",{className:"hidden lg:flex lg:gap-x-12",children:x.map(t=>(0,i.jsx)(r(),{href:t.href,className:"text-sm font-semibold leading-6 transition-colors hover:text-blue-400 ".concat(e===t.href?"text-blue-400":"text-white"),children:t.name},t.name))}),(0,i.jsx)("div",{className:"hidden lg:flex lg:flex-1 lg:justify-end",children:(0,i.jsx)(h.$,{asChild:!0,className:"gradient-metamorphic text-white font-semibold",children:(0,i.jsx)(r(),{href:"/contact",children:"Get Started"})})})]}),t&&(0,i.jsxs)("div",{className:"lg:hidden",children:[(0,i.jsx)("div",{className:"fixed inset-0 z-50"}),(0,i.jsxs)("div",{className:"fixed inset-y-0 right-0 z-50 w-full overflow-y-auto bg-black px-6 py-6 sm:max-w-sm sm:ring-1 sm:ring-gray-800",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)(r(),{href:"/",className:"-m-1.5 p-1.5",children:[(0,i.jsx)("span",{className:"sr-only",children:"Metamorphic Labs"}),(0,i.jsx)(m.MetamorphicLogo,{className:"h-8 w-auto"})]}),(0,i.jsxs)(h.$,{variant:"ghost",className:"-m-2.5 rounded-md p-2.5 text-white",onClick:()=>a(!1),children:[(0,i.jsx)("span",{className:"sr-only",children:"Close menu"}),(0,i.jsx)(c,{className:"h-6 w-6","aria-hidden":"true"})]})]}),(0,i.jsx)("div",{className:"mt-6 flow-root",children:(0,i.jsxs)("div",{className:"-my-6 divide-y divide-gray-800",children:[(0,i.jsx)("div",{className:"space-y-2 py-6",children:x.map(t=>(0,i.jsx)(r(),{href:t.href,className:"-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 transition-colors hover:bg-gray-900 ".concat(e===t.href?"text-blue-400":"text-white"),onClick:()=>a(!1),children:t.name},t.name))}),(0,i.jsx)("div",{className:"py-6",children:(0,i.jsx)(h.$,{asChild:!0,className:"gradient-metamorphic w-full text-white font-semibold",onClick:()=>a(!1),children:(0,i.jsx)(r(),{href:"/contact",children:"Get Started"})})})]})})]})]})]})}},2489:(e,t,a)=>{"use strict";a.d(t,{MetamorphicLogo:()=>n});var i=a(5155),s=a(230),r=a(9434);function n(e){let{className:t,animated:a=!1}=e;return(0,i.jsxs)(s.P.svg,{className:(0,r.cn)("text-white",t),viewBox:"0 0 200 40",fill:"none",xmlns:"http://www.w3.org/2000/svg",initial:a?{opacity:0,scale:.8}:void 0,animate:a?{opacity:1,scale:1}:void 0,transition:a?{duration:.8,ease:"easeOut"}:void 0,children:[(0,i.jsx)("defs",{children:(0,i.jsxs)("linearGradient",{id:"gradient-stroke",x1:"0%",y1:"0%",x2:"100%",y2:"0%",children:[(0,i.jsx)("stop",{offset:"0%",stopColor:"#3B82F6",children:a&&(0,i.jsx)("animate",{attributeName:"stop-color",values:"#3B82F6;#8B5CF6;#9333EA;#3B82F6",dur:"3s",repeatCount:"indefinite"})}),(0,i.jsx)("stop",{offset:"50%",stopColor:"#8B5CF6"}),(0,i.jsx)("stop",{offset:"100%",stopColor:"#9333EA",children:a&&(0,i.jsx)("animate",{attributeName:"stop-color",values:"#9333EA;#3B82F6;#8B5CF6;#9333EA",dur:"3s",repeatCount:"indefinite"})})]})}),(0,i.jsxs)(s.P.g,{initial:a?{opacity:0}:void 0,animate:a?{opacity:1}:void 0,transition:a?{delay:.3,duration:.6}:void 0,children:[(0,i.jsx)(s.P.path,{d:"M10 20 L25 5 L40 20 L25 35 Z",stroke:"url(#gradient-stroke)",strokeWidth:"2",fill:"none",initial:a?{pathLength:0}:void 0,animate:a?{pathLength:1}:void 0,transition:a?{delay:.5,duration:1.5,ease:"easeInOut"}:void 0}),(0,i.jsx)(s.P.circle,{cx:"25",cy:"20",r:"3",fill:"url(#gradient-stroke)",initial:a?{scale:0}:void 0,animate:a?{scale:1}:void 0,transition:a?{delay:1.8,duration:.4,ease:"easeOut"}:void 0})]}),(0,i.jsx)(s.P.text,{x:"55",y:"16",className:"fill-current text-sm font-bold tracking-wide",style:{fontFamily:"Inter, sans-serif"},initial:a?{opacity:0,x:-10}:void 0,animate:a?{opacity:1,x:0}:void 0,transition:a?{delay:.8,duration:.6}:void 0,children:"METAMORPHIC"}),(0,i.jsx)(s.P.text,{x:"55",y:"30",className:"fill-current text-xs font-medium tracking-wider opacity-80",style:{fontFamily:"Inter, sans-serif"},initial:a?{opacity:0,x:-10}:void 0,animate:a?{opacity:1,x:0}:void 0,transition:a?{delay:1,duration:.6}:void 0,children:"LABS"})]})}},5012:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,6874,23)),Promise.resolve().then(a.t.bind(a,8575,23)),Promise.resolve().then(a.t.bind(a,8132,23)),Promise.resolve().then(a.t.bind(a,347,23)),Promise.resolve().then(a.bind(a,2489)),Promise.resolve().then(a.bind(a,1357))},8132:e=>{e.exports={style:{fontFamily:"'JetBrains Mono', 'JetBrains Mono Fallback'",fontStyle:"normal"},className:"__className_3c557b",variable:"__variable_3c557b"}},8575:e=>{e.exports={style:{fontFamily:"'Inter', 'Inter Fallback'",fontStyle:"normal"},className:"__className_e8ce0c",variable:"__variable_e8ce0c"}},9434:(e,t,a)=>{"use strict";a.d(t,{cn:()=>r});var i=a(2596),s=a(9688);function r(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,s.QP)((0,i.$)(t))}}},e=>{var t=t=>e(e.s=t);e.O(0,[853,497,874,230,441,684,358],()=>t(5012)),_N_E=e.O()}]);