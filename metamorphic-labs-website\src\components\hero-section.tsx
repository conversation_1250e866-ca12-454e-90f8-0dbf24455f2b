'use client';

import Link from 'next/link';
import { motion } from 'framer-motion';
import { But<PERSON> } from '@/components/ui/button';
import { MetamorphicLogo } from '@/components/metamorphic-logo';
import { <PERSON>R<PERSON>, Spark<PERSON>, Zap } from 'lucide-react';

export function HeroSection() {
  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Background gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-900 to-black" />
      
      {/* Animated background elements */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-blue-500/10 rounded-full blur-3xl animate-pulse" />
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-pulse delay-1000" />
      </div>

      <div className="relative z-10 mx-auto max-w-7xl px-6 py-24 sm:py-32 lg:px-8">
        <div className="text-center">
          {/* Animated Logo */}
          <div className="mb-8">
            <MetamorphicLogo className="h-20 w-auto mx-auto mb-6" animated />
          </div>

          {/* Main Heading */}
          <motion.h1
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-4xl font-bold tracking-tight text-white sm:text-6xl lg:text-7xl"
          >
            Redefining Reality with{' '}
            <span className="gradient-text">AI</span>,{' '}
            <span className="gradient-text">Quantum Systems</span> &{' '}
            <span className="gradient-text">Intelligent Software</span>
          </motion.h1>

          {/* Subtitle */}
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="mt-6 text-lg leading-8 text-gray-300 max-w-3xl mx-auto"
          >
            Metamorphic Labs pioneers next-generation AI systems, multi-agent orchestration, 
            generative creativity, and boundary-pushing software solutions. From deep prompt 
            ecosystems to decentralized art galleries—we build the future.
          </motion.p>

          {/* CTA Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="mt-10 flex flex-col sm:flex-row items-center justify-center gap-4"
          >
            <Button
              asChild
              variant="gradient"
              size="lg"
              className="holographic-hover group"
            >
              <Link 
                href="https://catalyst.metamorphiclabs.ai"
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center gap-2"
              >
                <Sparkles className="h-5 w-5" />
                Explore Catalyst
                <ArrowRight className="h-4 w-4 transition-transform group-hover:translate-x-1" />
              </Link>
            </Button>

            <Button
              asChild
              variant="outline"
              size="lg"
              className="border-gray-600 text-white hover:bg-gray-800 holographic-hover group"
            >
              <Link 
                href="#metamorphic-reactor"
                className="flex items-center gap-2"
              >
                <Zap className="h-5 w-5" />
                Learn About Metamorphic Reactor
                <ArrowRight className="h-4 w-4 transition-transform group-hover:translate-x-1" />
              </Link>
            </Button>

            <Button
              asChild
              className="bg-black border border-yellow-400 text-yellow-400 hover:bg-yellow-400 hover:text-black holographic-hover group"
              size="lg"
            >
              <Link 
                href="https://vault024.metamorphiclabs.ai"
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center gap-2"
              >
                <span className="text-xs">✦</span>
                Visit Vault 024
                <ArrowRight className="h-4 w-4 transition-transform group-hover:translate-x-1" />
              </Link>
            </Button>
          </motion.div>

          {/* Scroll indicator */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 1, delay: 1 }}
            className="mt-16"
          >
            <div className="flex flex-col items-center">
              <p className="text-sm text-gray-400 mb-4">Discover our ecosystem</p>
              <motion.div
                animate={{ y: [0, 8, 0] }}
                transition={{ duration: 2, repeat: Infinity }}
                className="w-6 h-10 border-2 border-gray-600 rounded-full flex justify-center"
              >
                <div className="w-1 h-3 bg-gradient-to-b from-blue-400 to-purple-400 rounded-full mt-2" />
              </motion.div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
