var e=require("@hookform/resolvers"),r=require("react-hook-form"),s=require("valibot");exports.valibotResolver=function(t,o,a){return void 0===a&&(a={}),function(i,u,n){try{var v=!n.shouldUseNativeValidation&&"all"===n.criteriaMode;return Promise.resolve(s.safeParseAsync(t,i,Object.assign({},o,{abortPipeEarly:!v}))).then(function(t){if(t.issues){for(var o={};t.issues.length;){var u=t.issues[0],c=s.getDotPath(u);if(c&&(o[c]||(o[c]={message:u.message,type:u.type}),v)){var l=o[c].types,f=l&&l[u.type];o[c]=r.appendErrors(c,v,o,u.type,f?[].concat(f,u.message):u.message)}t.issues.shift()}return{values:{},errors:e.toNestErrors(o,n)}}return{values:a.raw?Object.assign({},i):t.output,errors:{}}})}catch(e){return Promise.reject(e)}}};
//# sourceMappingURL=valibot.js.map
