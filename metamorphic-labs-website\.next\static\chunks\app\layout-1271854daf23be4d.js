(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{285:(e,a,s)=>{"use strict";s.d(a,{$:()=>o});var t=s(5155);s(2115);var i=s(4624),r=s(2085),n=s(9434);let l=(0,r.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline",gradient:"gradient-metamorphic text-white shadow-xs hover:opacity-90 transition-opacity"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:a,variant:s,size:r,asChild:o=!1,...d}=e,c=o?i.DX:"button";return(0,t.jsx)(c,{"data-slot":"button",className:(0,n.cn)(l({variant:s,size:r,className:a})),...d})}},347:()=>{},1357:(e,a,s)=>{"use strict";s.d(a,{Navigation:()=>u});var t=s(5155),i=s(6874),r=s.n(i),n=s(8999),l=s(2115),o=s(9946);let d=(0,o.A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]]),c=(0,o.A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);var h=s(285),m=s(2489);let x=[{name:"Home",href:"/"},{name:"About",href:"/about"},{name:"Systems",href:"/systems"},{name:"Contact",href:"/contact"}];function u(){let e=(0,n.usePathname)(),[a,s]=(0,l.useState)(!1);return(0,t.jsxs)("header",{className:"sticky top-0 z-50 w-full border-b border-gray-800 bg-black/80 backdrop-blur-md",children:[(0,t.jsxs)("nav",{className:"mx-auto flex max-w-7xl items-center justify-between p-6 lg:px-8","aria-label":"Main navigation",children:[(0,t.jsx)("div",{className:"flex lg:flex-1",children:(0,t.jsxs)(r(),{href:"/",className:"-m-1.5 p-1.5","aria-label":"Metamorphic Labs home",children:[(0,t.jsx)("span",{className:"sr-only",children:"Metamorphic Labs"}),(0,t.jsx)(m.MetamorphicLogo,{className:"h-8 w-auto"})]})}),(0,t.jsx)("div",{className:"flex lg:hidden",children:(0,t.jsxs)(h.$,{variant:"ghost",className:"-m-2.5 inline-flex items-center justify-center rounded-md p-2.5 text-white",onClick:()=>s(!0),children:[(0,t.jsx)("span",{className:"sr-only",children:"Open main menu"}),(0,t.jsx)(d,{className:"h-6 w-6","aria-hidden":"true"})]})}),(0,t.jsx)("div",{className:"hidden lg:flex lg:gap-x-12",children:x.map(a=>(0,t.jsx)(r(),{href:a.href,className:"text-sm font-semibold leading-6 transition-colors hover:text-blue-400 ".concat(e===a.href?"text-blue-400":"text-white"),children:a.name},a.name))}),(0,t.jsx)("div",{className:"hidden lg:flex lg:flex-1 lg:justify-end",children:(0,t.jsx)(h.$,{asChild:!0,className:"gradient-metamorphic text-white font-semibold",children:(0,t.jsx)(r(),{href:"/contact",children:"Get Started"})})})]}),a&&(0,t.jsxs)("div",{className:"lg:hidden",children:[(0,t.jsx)("div",{className:"fixed inset-0 z-50"}),(0,t.jsxs)("div",{className:"fixed inset-y-0 right-0 z-50 w-full overflow-y-auto bg-black px-6 py-6 sm:max-w-sm sm:ring-1 sm:ring-gray-800",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)(r(),{href:"/",className:"-m-1.5 p-1.5",children:[(0,t.jsx)("span",{className:"sr-only",children:"Metamorphic Labs"}),(0,t.jsx)(m.MetamorphicLogo,{className:"h-8 w-auto"})]}),(0,t.jsxs)(h.$,{variant:"ghost",className:"-m-2.5 rounded-md p-2.5 text-white",onClick:()=>s(!1),children:[(0,t.jsx)("span",{className:"sr-only",children:"Close menu"}),(0,t.jsx)(c,{className:"h-6 w-6","aria-hidden":"true"})]})]}),(0,t.jsx)("div",{className:"mt-6 flow-root",children:(0,t.jsxs)("div",{className:"-my-6 divide-y divide-gray-800",children:[(0,t.jsx)("div",{className:"space-y-2 py-6",children:x.map(a=>(0,t.jsx)(r(),{href:a.href,className:"-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 transition-colors hover:bg-gray-900 ".concat(e===a.href?"text-blue-400":"text-white"),onClick:()=>s(!1),children:a.name},a.name))}),(0,t.jsx)("div",{className:"py-6",children:(0,t.jsx)(h.$,{asChild:!0,className:"gradient-metamorphic w-full text-white font-semibold",onClick:()=>s(!1),children:(0,t.jsx)(r(),{href:"/contact",children:"Get Started"})})})]})})]})]})]})}},2489:(e,a,s)=>{"use strict";s.d(a,{MetamorphicLogo:()=>l});var t=s(5155),i=s(6766),r=s(230),n=s(9434);function l(e){let{className:a,animated:s=!1}=e;return(0,t.jsx)(r.P.div,{className:(0,n.cn)("flex items-center",a),initial:s?{opacity:0,scale:.8}:void 0,animate:s?{opacity:1,scale:1}:void 0,transition:s?{duration:.8,ease:"easeOut"}:void 0,children:(0,t.jsx)(i.default,{src:"/metamorphic-labs-logo.png",alt:"Metamorphic Labs",width:200,height:40,className:"h-auto w-auto",priority:!0})})}},5012:(e,a,s)=>{Promise.resolve().then(s.t.bind(s,6874,23)),Promise.resolve().then(s.t.bind(s,8575,23)),Promise.resolve().then(s.t.bind(s,8132,23)),Promise.resolve().then(s.t.bind(s,347,23)),Promise.resolve().then(s.bind(s,2489)),Promise.resolve().then(s.bind(s,1357))},8132:e=>{e.exports={style:{fontFamily:"'JetBrains Mono', 'JetBrains Mono Fallback'",fontStyle:"normal"},className:"__className_3c557b",variable:"__variable_3c557b"}},8575:e=>{e.exports={style:{fontFamily:"'Inter', 'Inter Fallback'",fontStyle:"normal"},className:"__className_e8ce0c",variable:"__variable_e8ce0c"}},9434:(e,a,s)=>{"use strict";s.d(a,{cn:()=>r});var t=s(2596),i=s(9688);function r(){for(var e=arguments.length,a=Array(e),s=0;s<e;s++)a[s]=arguments[s];return(0,i.QP)((0,t.$)(a))}}},e=>{var a=a=>e(e.s=a);e.O(0,[853,497,874,230,766,441,684,358],()=>a(5012)),_N_E=e.O()}]);