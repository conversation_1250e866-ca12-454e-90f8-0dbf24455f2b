[{"C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\app\\about\\page.tsx": "1", "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\app\\contact\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\app\\layout.tsx": "3", "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\app\\not-found.tsx": "4", "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\app\\page.tsx": "5", "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\app\\sitemap.ts": "6", "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\app\\systems\\page.tsx": "7", "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\contact-form.tsx": "8", "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\features-grid.tsx": "9", "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\footer.tsx": "10", "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\hero-section.tsx": "11", "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\intro-section.tsx": "12", "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\metamorphic-logo.tsx": "13", "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\navigation.tsx": "14", "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\projects-preview.tsx": "15", "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\structured-data.tsx": "16", "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\system-card.tsx": "17", "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\ui\\badge.tsx": "18", "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\ui\\button.tsx": "19", "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\ui\\card.tsx": "20", "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\ui\\form.tsx": "21", "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\ui\\input.tsx": "22", "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\ui\\label.tsx": "23", "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\ui\\textarea.tsx": "24", "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\lib\\utils.ts": "25", "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\__tests__\\components\\metamorphic-logo.test.tsx": "26"}, {"size": 5681, "mtime": 1751158400226, "results": "27", "hashOfConfig": "28"}, {"size": 3504, "mtime": 1751155369657, "results": "29", "hashOfConfig": "28"}, {"size": 2532, "mtime": 1751158881225, "results": "30", "hashOfConfig": "28"}, {"size": 1945, "mtime": 1751155381986, "results": "31", "hashOfConfig": "28"}, {"size": 1060, "mtime": 1751159065699, "results": "32", "hashOfConfig": "28"}, {"size": 680, "mtime": 1751158991100, "results": "33", "hashOfConfig": "28"}, {"size": 4682, "mtime": 1751155393949, "results": "34", "hashOfConfig": "28"}, {"size": 5278, "mtime": 1751158454549, "results": "35", "hashOfConfig": "28"}, {"size": 4257, "mtime": 1751158694758, "results": "36", "hashOfConfig": "28"}, {"size": 4384, "mtime": 1751155216606, "results": "37", "hashOfConfig": "28"}, {"size": 5306, "mtime": 1751158964025, "results": "38", "hashOfConfig": "28"}, {"size": 2644, "mtime": 1751158674801, "results": "39", "hashOfConfig": "28"}, {"size": 794, "mtime": 1751159940585, "results": "40", "hashOfConfig": "28"}, {"size": 4541, "mtime": 1751159080560, "results": "41", "hashOfConfig": "28"}, {"size": 7736, "mtime": 1751158725270, "results": "42", "hashOfConfig": "28"}, {"size": 2333, "mtime": 1751159007197, "results": "43", "hashOfConfig": "28"}, {"size": 5089, "mtime": 1751158478032, "results": "44", "hashOfConfig": "28"}, {"size": 1631, "mtime": 1751158501965, "results": "45", "hashOfConfig": "28"}, {"size": 2232, "mtime": 1751158519413, "results": "46", "hashOfConfig": "28"}, {"size": 1989, "mtime": 1751154983699, "results": "47", "hashOfConfig": "28"}, {"size": 3759, "mtime": 1751154983772, "results": "48", "hashOfConfig": "28"}, {"size": 967, "mtime": 1751154983706, "results": "49", "hashOfConfig": "28"}, {"size": 611, "mtime": 1751154983785, "results": "50", "hashOfConfig": "28"}, {"size": 759, "mtime": 1751154983713, "results": "51", "hashOfConfig": "28"}, {"size": 166, "mtime": 1751154939297, "results": "52", "hashOfConfig": "28"}, {"size": 1039, "mtime": 1751159476331, "results": "53", "hashOfConfig": "28"}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "p39a21", {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\app\\about\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\app\\contact\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\app\\not-found.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\app\\sitemap.ts", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\app\\systems\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\contact-form.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\features-grid.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\footer.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\hero-section.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\intro-section.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\metamorphic-logo.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\navigation.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\projects-preview.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\structured-data.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\system-card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\ui\\badge.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\ui\\form.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\ui\\input.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\ui\\label.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\ui\\textarea.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\__tests__\\components\\metamorphic-logo.test.tsx", [], []]