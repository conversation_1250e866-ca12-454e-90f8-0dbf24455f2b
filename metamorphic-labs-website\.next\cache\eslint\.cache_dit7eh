[{"C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\app\\about\\page.tsx": "1", "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\app\\contact\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\app\\layout.tsx": "3", "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\app\\not-found.tsx": "4", "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\app\\page.tsx": "5", "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\app\\systems\\page.tsx": "6", "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\footer.tsx": "7", "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\metamorphic-logo.tsx": "8", "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\navigation.tsx": "9", "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\ui\\button.tsx": "10", "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\ui\\card.tsx": "11", "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\ui\\form.tsx": "12", "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\ui\\input.tsx": "13", "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\ui\\label.tsx": "14", "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\ui\\textarea.tsx": "15", "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\lib\\utils.ts": "16"}, {"size": 5676, "mtime": 1751155359035, "results": "17", "hashOfConfig": "18"}, {"size": 3504, "mtime": 1751155369657, "results": "19", "hashOfConfig": "18"}, {"size": 2526, "mtime": 1751155173349, "results": "20", "hashOfConfig": "18"}, {"size": 1945, "mtime": 1751155381986, "results": "21", "hashOfConfig": "18"}, {"size": 4086, "mtime": 1751154637022, "results": "22", "hashOfConfig": "18"}, {"size": 4682, "mtime": 1751155393949, "results": "23", "hashOfConfig": "18"}, {"size": 4384, "mtime": 1751155216606, "results": "24", "hashOfConfig": "18"}, {"size": 1474, "mtime": 1751155229440, "results": "25", "hashOfConfig": "18"}, {"size": 4497, "mtime": 1751155196683, "results": "26", "hashOfConfig": "18"}, {"size": 2123, "mtime": 1751154983655, "results": "27", "hashOfConfig": "18"}, {"size": 1989, "mtime": 1751154983699, "results": "28", "hashOfConfig": "18"}, {"size": 3759, "mtime": 1751154983772, "results": "29", "hashOfConfig": "18"}, {"size": 967, "mtime": 1751154983706, "results": "30", "hashOfConfig": "18"}, {"size": 611, "mtime": 1751154983785, "results": "31", "hashOfConfig": "18"}, {"size": 759, "mtime": 1751154983713, "results": "32", "hashOfConfig": "18"}, {"size": 166, "mtime": 1751154939297, "results": "33", "hashOfConfig": "18"}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "p39a21", {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\app\\about\\page.tsx", ["82"], [], "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\app\\contact\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\app\\not-found.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\app\\systems\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\footer.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\metamorphic-logo.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\navigation.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\ui\\form.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\ui\\input.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\ui\\label.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\ui\\textarea.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\lib\\utils.ts", [], [], {"ruleId": "83", "severity": 2, "message": "84", "line": 45, "column": 27, "nodeType": "85", "messageId": "86", "suggestions": "87"}, "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["88", "89", "90", "91"], {"messageId": "92", "data": "93", "fix": "94", "desc": "95"}, {"messageId": "92", "data": "96", "fix": "97", "desc": "98"}, {"messageId": "92", "data": "99", "fix": "100", "desc": "101"}, {"messageId": "92", "data": "102", "fix": "103", "desc": "104"}, "replaceWithAlt", {"alt": "105"}, {"range": "106", "text": "107"}, "Replace with `&apos;`.", {"alt": "108"}, {"range": "109", "text": "110"}, "Replace with `&lsquo;`.", {"alt": "111"}, {"range": "112", "text": "113"}, "Replace with `&#39;`.", {"alt": "114"}, {"range": "115", "text": "116"}, "Replace with `&rsquo;`.", "&apos;", [2267, 2514], "\n              From deep prompt ecosystems that enhance AI interactions to decentralized art \n              galleries that revolutionize creative expression, we build the infrastructure \n              for tomorrow&apos;s digital landscape.\n            ", "&lsquo;", [2267, 2514], "\n              From deep prompt ecosystems that enhance AI interactions to decentralized art \n              galleries that revolutionize creative expression, we build the infrastructure \n              for tomorrow&lsquo;s digital landscape.\n            ", "&#39;", [2267, 2514], "\n              From deep prompt ecosystems that enhance AI interactions to decentralized art \n              galleries that revolutionize creative expression, we build the infrastructure \n              for tomorrow&#39;s digital landscape.\n            ", "&rsquo;", [2267, 2514], "\n              From deep prompt ecosystems that enhance AI interactions to decentralized art \n              galleries that revolutionize creative expression, we build the infrastructure \n              for tomorrow&rsquo;s digital landscape.\n            "]