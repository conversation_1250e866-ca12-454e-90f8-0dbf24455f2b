[{"C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\app\\about\\page.tsx": "1", "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\app\\contact\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\app\\layout.tsx": "3", "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\app\\not-found.tsx": "4", "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\app\\page.tsx": "5", "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\app\\sitemap.ts": "6", "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\app\\systems\\page.tsx": "7", "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\contact-form.tsx": "8", "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\features-grid.tsx": "9", "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\footer.tsx": "10", "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\hero-section.tsx": "11", "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\intro-section.tsx": "12", "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\metamorphic-logo.tsx": "13", "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\navigation.tsx": "14", "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\projects-preview.tsx": "15", "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\structured-data.tsx": "16", "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\system-card.tsx": "17", "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\ui\\badge.tsx": "18", "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\ui\\button.tsx": "19", "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\ui\\card.tsx": "20", "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\ui\\form.tsx": "21", "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\ui\\input.tsx": "22", "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\ui\\label.tsx": "23", "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\ui\\textarea.tsx": "24", "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\lib\\utils.ts": "25"}, {"size": 5681, "mtime": 1751158400226, "results": "26", "hashOfConfig": "27"}, {"size": 3504, "mtime": 1751155369657, "results": "28", "hashOfConfig": "27"}, {"size": 2532, "mtime": 1751158881225, "results": "29", "hashOfConfig": "27"}, {"size": 1945, "mtime": 1751155381986, "results": "30", "hashOfConfig": "27"}, {"size": 1060, "mtime": 1751159065699, "results": "31", "hashOfConfig": "27"}, {"size": 680, "mtime": 1751158991100, "results": "32", "hashOfConfig": "27"}, {"size": 4682, "mtime": 1751155393949, "results": "33", "hashOfConfig": "27"}, {"size": 5278, "mtime": 1751158454549, "results": "34", "hashOfConfig": "27"}, {"size": 4257, "mtime": 1751158694758, "results": "35", "hashOfConfig": "27"}, {"size": 4384, "mtime": 1751155216606, "results": "36", "hashOfConfig": "27"}, {"size": 5306, "mtime": 1751158964025, "results": "37", "hashOfConfig": "27"}, {"size": 2644, "mtime": 1751158674801, "results": "38", "hashOfConfig": "27"}, {"size": 3313, "mtime": 1751159377881, "results": "39", "hashOfConfig": "27"}, {"size": 4541, "mtime": 1751159080560, "results": "40", "hashOfConfig": "27"}, {"size": 7736, "mtime": 1751158725270, "results": "41", "hashOfConfig": "27"}, {"size": 2333, "mtime": 1751159007197, "results": "42", "hashOfConfig": "27"}, {"size": 5089, "mtime": 1751158478032, "results": "43", "hashOfConfig": "27"}, {"size": 1631, "mtime": 1751158501965, "results": "44", "hashOfConfig": "27"}, {"size": 2232, "mtime": 1751158519413, "results": "45", "hashOfConfig": "27"}, {"size": 1989, "mtime": 1751154983699, "results": "46", "hashOfConfig": "27"}, {"size": 3759, "mtime": 1751154983772, "results": "47", "hashOfConfig": "27"}, {"size": 967, "mtime": 1751154983706, "results": "48", "hashOfConfig": "27"}, {"size": 611, "mtime": 1751154983785, "results": "49", "hashOfConfig": "27"}, {"size": 759, "mtime": 1751154983713, "results": "50", "hashOfConfig": "27"}, {"size": 166, "mtime": 1751154939297, "results": "51", "hashOfConfig": "27"}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "p39a21", {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\app\\about\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\app\\contact\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\app\\not-found.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\app\\sitemap.ts", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\app\\systems\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\contact-form.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\features-grid.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\footer.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\hero-section.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\intro-section.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\metamorphic-logo.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\navigation.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\projects-preview.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\structured-data.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\system-card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\ui\\badge.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\ui\\form.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\ui\\input.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\ui\\label.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\components\\ui\\textarea.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\lib\\utils.ts", [], []]