import Link from 'next/link';
import { MetamorphicLogo } from '@/components/metamorphic-logo';

const navigation = {
  main: [
    { name: 'Home', href: '/' },
    { name: 'About', href: '/about' },
    { name: 'Systems', href: '/systems' },
    { name: 'Contact', href: '/contact' },
  ],
  systems: [
    { name: 'Catalyst', href: 'https://catalyst.metamorphiclabs.ai' },
    { name: 'Metamorphic Reactor', href: '#metamorphic-reactor' },
    { name: 'Vault 024', href: 'https://vault024.metamorphiclabs.ai' },
  ],
  legal: [
    { name: 'Privacy Policy', href: '/privacy' },
    { name: 'Terms of Service', href: '/terms' },
  ],
};

export function Footer() {
  return (
    <footer className="bg-black border-t border-gray-800" aria-labelledby="footer-heading">
      <h2 id="footer-heading" className="sr-only">
        Footer
      </h2>
      <div className="mx-auto max-w-7xl px-6 pb-8 pt-16 sm:pt-24 lg:px-8 lg:pt-32">
        <div className="xl:grid xl:grid-cols-3 xl:gap-8">
          <div className="space-y-8">
            <MetamorphicLogo className="h-10 w-auto" />
            <p className="text-sm leading-6 text-gray-300">
              Redefining Reality with AI, Quantum Systems & Intelligent Software
            </p>
            <p className="text-xs leading-5 text-gray-400">
              Metamorphic Labs pioneers next-generation AI systems, multi-agent orchestration, 
              generative creativity, and boundary-pushing software solutions.
            </p>
          </div>
          <div className="mt-16 grid grid-cols-2 gap-8 xl:col-span-2 xl:mt-0">
            <div className="md:grid md:grid-cols-2 md:gap-8">
              <div>
                <h3 className="text-sm font-semibold leading-6 text-white">Navigation</h3>
                <ul role="list" className="mt-6 space-y-4">
                  {navigation.main.map((item) => (
                    <li key={item.name}>
                      <Link 
                        href={item.href} 
                        className="text-sm leading-6 text-gray-300 hover:text-white transition-colors"
                      >
                        {item.name}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
              <div className="mt-10 md:mt-0">
                <h3 className="text-sm font-semibold leading-6 text-white">Systems</h3>
                <ul role="list" className="mt-6 space-y-4">
                  {navigation.systems.map((item) => (
                    <li key={item.name}>
                      <Link 
                        href={item.href} 
                        className="text-sm leading-6 text-gray-300 hover:text-white transition-colors"
                        target={item.href.startsWith('http') ? '_blank' : undefined}
                        rel={item.href.startsWith('http') ? 'noopener noreferrer' : undefined}
                      >
                        {item.name}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
            <div className="md:grid md:grid-cols-1 md:gap-8">
              <div>
                <h3 className="text-sm font-semibold leading-6 text-white">Legal</h3>
                <ul role="list" className="mt-6 space-y-4">
                  {navigation.legal.map((item) => (
                    <li key={item.name}>
                      <Link 
                        href={item.href} 
                        className="text-sm leading-6 text-gray-300 hover:text-white transition-colors"
                      >
                        {item.name}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>
        <div className="mt-16 border-t border-gray-800 pt-8 sm:mt-20 lg:mt-24">
          <div className="flex flex-col items-center justify-between sm:flex-row">
            <p className="text-xs leading-5 text-gray-400">
              &copy; {new Date().getFullYear()} Metamorphic Labs LLC. All rights reserved.
            </p>
            <p className="mt-4 text-xs leading-5 text-gray-400 sm:mt-0">
              Built with Next.js, React 19, and Tailwind CSS
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
}
