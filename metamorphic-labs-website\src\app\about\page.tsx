import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'About Us | Metamorphic Labs',
  description: 'Learn about Metamorphic Labs mission, vision, core values, and meet our team of innovators building the future of AI and quantum systems.',
};

export default function AboutPage() {
  return (
    <div className="bg-black text-white">
      <div className="mx-auto max-w-7xl px-6 py-24 sm:py-32 lg:px-8">
        <div className="mx-auto max-w-2xl lg:mx-0">
          <h1 className="text-4xl font-bold tracking-tight text-white sm:text-6xl">
            About <span className="gradient-text">Metamorphic Labs</span>
          </h1>
          <p className="mt-6 text-lg leading-8 text-gray-300">
            We are pioneers in next-generation AI systems, multi-agent orchestration,
            and boundary-pushing software solutions that redefine what&apos;s possible.
          </p>
        </div>

        {/* Mission & Vision */}
        <div className="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-x-8 gap-y-16 lg:mx-0 lg:mt-10 lg:max-w-none lg:grid-cols-12">
          <div className="relative lg:order-last lg:col-span-5">
            <div className="absolute inset-0 gradient-metamorphic rounded-2xl opacity-20"></div>
            <div className="relative rounded-2xl bg-gray-900 p-8">
              <h2 className="text-2xl font-bold text-white">Our Mission</h2>
              <p className="mt-4 text-gray-300">
                To push the boundaries of artificial intelligence and quantum systems, 
                creating innovative solutions that transform how we interact with technology 
                and understand reality itself.
              </p>
            </div>
          </div>
          <div className="max-w-xl text-base leading-7 text-gray-300 lg:col-span-7">
            <h2 className="text-2xl font-bold text-white">Our Vision</h2>
            <p className="mt-4">
              We envision a future where AI systems seamlessly integrate with human creativity, 
              where quantum computing unlocks new possibilities, and where intelligent software 
              solutions empower individuals and organizations to achieve unprecedented innovation.
            </p>
            <p className="mt-6">
              From deep prompt ecosystems that enhance AI interactions to decentralized art 
              galleries that revolutionize creative expression, we build the infrastructure 
              for tomorrow's digital landscape.
            </p>
          </div>
        </div>

        {/* Core Values */}
        <div className="mt-24">
          <h2 className="text-3xl font-bold text-white mb-12">Core Values</h2>
          <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
            <div className="rounded-lg bg-gray-900 p-6 border border-gray-800">
              <h3 className="text-xl font-semibold text-white mb-4">Innovation</h3>
              <p className="text-gray-300">
                We constantly push the boundaries of what&apos;s possible, exploring new
                technologies and methodologies to solve complex challenges.
              </p>
            </div>
            <div className="rounded-lg bg-gray-900 p-6 border border-gray-800">
              <h3 className="text-xl font-semibold text-white mb-4">Excellence</h3>
              <p className="text-gray-300">
                We maintain the highest standards in everything we do, from code quality 
                to user experience and system reliability.
              </p>
            </div>
            <div className="rounded-lg bg-gray-900 p-6 border border-gray-800">
              <h3 className="text-xl font-semibold text-white mb-4">Collaboration</h3>
              <p className="text-gray-300">
                We believe in the power of collective intelligence and work together 
                to achieve outcomes greater than the sum of our parts.
              </p>
            </div>
          </div>
        </div>

        {/* Team Profiles */}
        <div className="mt-24">
          <h2 className="text-3xl font-bold text-white mb-12">Our Team</h2>
          <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
            <div className="rounded-lg bg-gray-900 p-6 border border-gray-800">
              <h3 className="text-xl font-semibold gradient-text mb-2">Vernox</h3>
              <p className="text-sm text-gray-400 mb-4">AI Systems Architect</p>
              <p className="text-gray-300">
                Leading the development of next-generation AI orchestration systems 
                and multi-agent frameworks.
              </p>
            </div>
            <div className="rounded-lg bg-gray-900 p-6 border border-gray-800">
              <h3 className="text-xl font-semibold gradient-text mb-2">0a</h3>
              <p className="text-sm text-gray-400 mb-4">Quantum Computing Specialist</p>
              <p className="text-gray-300">
                Pioneering quantum algorithms and their integration with classical 
                AI systems for unprecedented computational capabilities.
              </p>
            </div>
            <div className="rounded-lg bg-gray-900 p-6 border border-gray-800">
              <h3 className="text-xl font-semibold gradient-text mb-2">Nyra</h3>
              <p className="text-sm text-gray-400 mb-4">Creative Technology Director</p>
              <p className="text-gray-300">
                Bridging the gap between technology and creativity, leading our 
                generative art and NFT initiatives.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
