(()=>{var e={};e.id=747,e.ids=[747],e.modules={325:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,5814,23))},481:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>eD,metadata:()=>eT});var o=t(7413),a=t(4536),s=t.n(a),n=t(1120);function i(){for(var e,r,t=0,o="",a=arguments.length;t<a;t++)(e=arguments[t])&&(r=function e(r){var t,o,a="";if("string"==typeof r||"number"==typeof r)a+=r;else if("object"==typeof r)if(Array.isArray(r)){var s=r.length;for(t=0;t<s;t++)r[t]&&(o=e(r[t]))&&(a&&(a+=" "),a+=o)}else for(o in r)r[o]&&(a&&(a+=" "),a+=o);return a}(e))&&(o&&(o+=" "),o+=r);return o}let l=e=>{let r=p(e),{conflictingClassGroups:t,conflictingClassGroupModifiers:o}=e;return{getClassGroupId:e=>{let t=e.split("-");return""===t[0]&&1!==t.length&&t.shift(),d(t,r)||m(e)},getConflictingClassGroupIds:(e,r)=>{let a=t[e]||[];return r&&o[e]?[...a,...o[e]]:a}}},d=(e,r)=>{if(0===e.length)return r.classGroupId;let t=e[0],o=r.nextPart.get(t),a=o?d(e.slice(1),o):void 0;if(a)return a;if(0===r.validators.length)return;let s=e.join("-");return r.validators.find(({validator:e})=>e(s))?.classGroupId},c=/^\[(.+)\]$/,m=e=>{if(c.test(e)){let r=c.exec(e)[1],t=r?.substring(0,r.indexOf(":"));if(t)return"arbitrary.."+t}},p=e=>{let{theme:r,classGroups:t}=e,o={nextPart:new Map,validators:[]};for(let e in t)u(t[e],o,e,r);return o},u=(e,r,t,o)=>{e.forEach(e=>{if("string"==typeof e){(""===e?r:g(r,e)).classGroupId=t;return}if("function"==typeof e)return b(e)?void u(e(o),r,t,o):void r.validators.push({validator:e,classGroupId:t});Object.entries(e).forEach(([e,a])=>{u(a,g(r,e),t,o)})})},g=(e,r)=>{let t=e;return r.split("-").forEach(e=>{t.nextPart.has(e)||t.nextPart.set(e,{nextPart:new Map,validators:[]}),t=t.nextPart.get(e)}),t},b=e=>e.isThemeGetter,f=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let r=0,t=new Map,o=new Map,a=(a,s)=>{t.set(a,s),++r>e&&(r=0,o=t,t=new Map)};return{get(e){let r=t.get(e);return void 0!==r?r:void 0!==(r=o.get(e))?(a(e,r),r):void 0},set(e,r){t.has(e)?t.set(e,r):a(e,r)}}},h=e=>{let{prefix:r,experimentalParseClassName:t}=e,o=e=>{let r,t=[],o=0,a=0,s=0;for(let n=0;n<e.length;n++){let i=e[n];if(0===o&&0===a){if(":"===i){t.push(e.slice(s,n)),s=n+1;continue}if("/"===i){r=n;continue}}"["===i?o++:"]"===i?o--:"("===i?a++:")"===i&&a--}let n=0===t.length?e:e.substring(s),i=x(n);return{modifiers:t,hasImportantModifier:i!==n,baseClassName:i,maybePostfixModifierPosition:r&&r>s?r-s:void 0}};if(r){let e=r+":",t=o;o=r=>r.startsWith(e)?t(r.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:r,maybePostfixModifierPosition:void 0}}if(t){let e=o;o=r=>t({className:r,parseClassName:e})}return o},x=e=>e.endsWith("!")?e.substring(0,e.length-1):e.startsWith("!")?e.substring(1):e,v=e=>{let r=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let t=[],o=[];return e.forEach(e=>{"["===e[0]||r[e]?(t.push(...o.sort(),e),o=[]):o.push(e)}),t.push(...o.sort()),t}},y=e=>({cache:f(e.cacheSize),parseClassName:h(e),sortModifiers:v(e),...l(e)}),w=/\s+/,k=(e,r)=>{let{parseClassName:t,getClassGroupId:o,getConflictingClassGroupIds:a,sortModifiers:s}=r,n=[],i=e.trim().split(w),l="";for(let e=i.length-1;e>=0;e-=1){let r=i[e],{isExternal:d,modifiers:c,hasImportantModifier:m,baseClassName:p,maybePostfixModifierPosition:u}=t(r);if(d){l=r+(l.length>0?" "+l:l);continue}let g=!!u,b=o(g?p.substring(0,u):p);if(!b){if(!g||!(b=o(p))){l=r+(l.length>0?" "+l:l);continue}g=!1}let f=s(c).join(":"),h=m?f+"!":f,x=h+b;if(n.includes(x))continue;n.push(x);let v=a(b,g);for(let e=0;e<v.length;++e){let r=v[e];n.push(h+r)}l=r+(l.length>0?" "+l:l)}return l};function j(){let e,r,t=0,o="";for(;t<arguments.length;)(e=arguments[t++])&&(r=N(e))&&(o&&(o+=" "),o+=r);return o}let N=e=>{let r;if("string"==typeof e)return e;let t="";for(let o=0;o<e.length;o++)e[o]&&(r=N(e[o]))&&(t&&(t+=" "),t+=r);return t},z=e=>{let r=r=>r[e]||[];return r.isThemeGetter=!0,r},A=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,C=/^\((?:(\w[\w-]*):)?(.+)\)$/i,P=/^\d+\/\d+$/,M=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,I=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,E=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,_=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,S=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,q=e=>P.test(e),G=e=>!!e&&!Number.isNaN(Number(e)),O=e=>!!e&&Number.isInteger(Number(e)),$=e=>e.endsWith("%")&&G(e.slice(0,-1)),R=e=>M.test(e),W=()=>!0,T=e=>I.test(e)&&!E.test(e),V=()=>!1,D=e=>_.test(e),L=e=>S.test(e),F=e=>!B(e)&&!J(e),U=e=>es(e,ed,V),B=e=>A.test(e),Z=e=>es(e,ec,T),Q=e=>es(e,em,G),H=e=>es(e,ei,V),K=e=>es(e,el,L),X=e=>es(e,eu,D),J=e=>C.test(e),Y=e=>en(e,ec),ee=e=>en(e,ep),er=e=>en(e,ei),et=e=>en(e,ed),eo=e=>en(e,el),ea=e=>en(e,eu,!0),es=(e,r,t)=>{let o=A.exec(e);return!!o&&(o[1]?r(o[1]):t(o[2]))},en=(e,r,t=!1)=>{let o=C.exec(e);return!!o&&(o[1]?r(o[1]):t)},ei=e=>"position"===e||"percentage"===e,el=e=>"image"===e||"url"===e,ed=e=>"length"===e||"size"===e||"bg-size"===e,ec=e=>"length"===e,em=e=>"number"===e,ep=e=>"family-name"===e,eu=e=>"shadow"===e;Symbol.toStringTag;let eg=function(e,...r){let t,o,a,s=function(i){return o=(t=y(r.reduce((e,r)=>r(e),e()))).cache.get,a=t.cache.set,s=n,n(i)};function n(e){let r=o(e);if(r)return r;let s=k(e,t);return a(e,s),s}return function(){return s(j.apply(null,arguments))}}(()=>{let e=z("color"),r=z("font"),t=z("text"),o=z("font-weight"),a=z("tracking"),s=z("leading"),n=z("breakpoint"),i=z("container"),l=z("spacing"),d=z("radius"),c=z("shadow"),m=z("inset-shadow"),p=z("text-shadow"),u=z("drop-shadow"),g=z("blur"),b=z("perspective"),f=z("aspect"),h=z("ease"),x=z("animate"),v=()=>["auto","avoid","all","avoid-page","page","left","right","column"],y=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],w=()=>[...y(),J,B],k=()=>["auto","hidden","clip","visible","scroll"],j=()=>["auto","contain","none"],N=()=>[J,B,l],A=()=>[q,"full","auto",...N()],C=()=>[O,"none","subgrid",J,B],P=()=>["auto",{span:["full",O,J,B]},O,J,B],M=()=>[O,"auto",J,B],I=()=>["auto","min","max","fr",J,B],E=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],_=()=>["start","end","center","stretch","center-safe","end-safe"],S=()=>["auto",...N()],T=()=>[q,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...N()],V=()=>[e,J,B],D=()=>[...y(),er,H,{position:[J,B]}],L=()=>["no-repeat",{repeat:["","x","y","space","round"]}],es=()=>["auto","cover","contain",et,U,{size:[J,B]}],en=()=>[$,Y,Z],ei=()=>["","none","full",d,J,B],el=()=>["",G,Y,Z],ed=()=>["solid","dashed","dotted","double"],ec=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],em=()=>[G,$,er,H],ep=()=>["","none",g,J,B],eu=()=>["none",G,J,B],eg=()=>["none",G,J,B],eb=()=>[G,J,B],ef=()=>[q,"full",...N()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[R],breakpoint:[R],color:[W],container:[R],"drop-shadow":[R],ease:["in","out","in-out"],font:[F],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[R],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[R],shadow:[R],spacing:["px",G],text:[R],"text-shadow":[R],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",q,B,J,f]}],container:["container"],columns:[{columns:[G,B,J,i]}],"break-after":[{"break-after":v()}],"break-before":[{"break-before":v()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:w()}],overflow:[{overflow:k()}],"overflow-x":[{"overflow-x":k()}],"overflow-y":[{"overflow-y":k()}],overscroll:[{overscroll:j()}],"overscroll-x":[{"overscroll-x":j()}],"overscroll-y":[{"overscroll-y":j()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:A()}],"inset-x":[{"inset-x":A()}],"inset-y":[{"inset-y":A()}],start:[{start:A()}],end:[{end:A()}],top:[{top:A()}],right:[{right:A()}],bottom:[{bottom:A()}],left:[{left:A()}],visibility:["visible","invisible","collapse"],z:[{z:[O,"auto",J,B]}],basis:[{basis:[q,"full","auto",i,...N()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[G,q,"auto","initial","none",B]}],grow:[{grow:["",G,J,B]}],shrink:[{shrink:["",G,J,B]}],order:[{order:[O,"first","last","none",J,B]}],"grid-cols":[{"grid-cols":C()}],"col-start-end":[{col:P()}],"col-start":[{"col-start":M()}],"col-end":[{"col-end":M()}],"grid-rows":[{"grid-rows":C()}],"row-start-end":[{row:P()}],"row-start":[{"row-start":M()}],"row-end":[{"row-end":M()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":I()}],"auto-rows":[{"auto-rows":I()}],gap:[{gap:N()}],"gap-x":[{"gap-x":N()}],"gap-y":[{"gap-y":N()}],"justify-content":[{justify:[...E(),"normal"]}],"justify-items":[{"justify-items":[..._(),"normal"]}],"justify-self":[{"justify-self":["auto",..._()]}],"align-content":[{content:["normal",...E()]}],"align-items":[{items:[..._(),{baseline:["","last"]}]}],"align-self":[{self:["auto",..._(),{baseline:["","last"]}]}],"place-content":[{"place-content":E()}],"place-items":[{"place-items":[..._(),"baseline"]}],"place-self":[{"place-self":["auto",..._()]}],p:[{p:N()}],px:[{px:N()}],py:[{py:N()}],ps:[{ps:N()}],pe:[{pe:N()}],pt:[{pt:N()}],pr:[{pr:N()}],pb:[{pb:N()}],pl:[{pl:N()}],m:[{m:S()}],mx:[{mx:S()}],my:[{my:S()}],ms:[{ms:S()}],me:[{me:S()}],mt:[{mt:S()}],mr:[{mr:S()}],mb:[{mb:S()}],ml:[{ml:S()}],"space-x":[{"space-x":N()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":N()}],"space-y-reverse":["space-y-reverse"],size:[{size:T()}],w:[{w:[i,"screen",...T()]}],"min-w":[{"min-w":[i,"screen","none",...T()]}],"max-w":[{"max-w":[i,"screen","none","prose",{screen:[n]},...T()]}],h:[{h:["screen","lh",...T()]}],"min-h":[{"min-h":["screen","lh","none",...T()]}],"max-h":[{"max-h":["screen","lh",...T()]}],"font-size":[{text:["base",t,Y,Z]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[o,J,Q]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",$,B]}],"font-family":[{font:[ee,B,r]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[a,J,B]}],"line-clamp":[{"line-clamp":[G,"none",J,Q]}],leading:[{leading:[s,...N()]}],"list-image":[{"list-image":["none",J,B]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",J,B]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:V()}],"text-color":[{text:V()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...ed(),"wavy"]}],"text-decoration-thickness":[{decoration:[G,"from-font","auto",J,Z]}],"text-decoration-color":[{decoration:V()}],"underline-offset":[{"underline-offset":[G,"auto",J,B]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:N()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",J,B]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",J,B]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:D()}],"bg-repeat":[{bg:L()}],"bg-size":[{bg:es()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},O,J,B],radial:["",J,B],conic:[O,J,B]},eo,K]}],"bg-color":[{bg:V()}],"gradient-from-pos":[{from:en()}],"gradient-via-pos":[{via:en()}],"gradient-to-pos":[{to:en()}],"gradient-from":[{from:V()}],"gradient-via":[{via:V()}],"gradient-to":[{to:V()}],rounded:[{rounded:ei()}],"rounded-s":[{"rounded-s":ei()}],"rounded-e":[{"rounded-e":ei()}],"rounded-t":[{"rounded-t":ei()}],"rounded-r":[{"rounded-r":ei()}],"rounded-b":[{"rounded-b":ei()}],"rounded-l":[{"rounded-l":ei()}],"rounded-ss":[{"rounded-ss":ei()}],"rounded-se":[{"rounded-se":ei()}],"rounded-ee":[{"rounded-ee":ei()}],"rounded-es":[{"rounded-es":ei()}],"rounded-tl":[{"rounded-tl":ei()}],"rounded-tr":[{"rounded-tr":ei()}],"rounded-br":[{"rounded-br":ei()}],"rounded-bl":[{"rounded-bl":ei()}],"border-w":[{border:el()}],"border-w-x":[{"border-x":el()}],"border-w-y":[{"border-y":el()}],"border-w-s":[{"border-s":el()}],"border-w-e":[{"border-e":el()}],"border-w-t":[{"border-t":el()}],"border-w-r":[{"border-r":el()}],"border-w-b":[{"border-b":el()}],"border-w-l":[{"border-l":el()}],"divide-x":[{"divide-x":el()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":el()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...ed(),"hidden","none"]}],"divide-style":[{divide:[...ed(),"hidden","none"]}],"border-color":[{border:V()}],"border-color-x":[{"border-x":V()}],"border-color-y":[{"border-y":V()}],"border-color-s":[{"border-s":V()}],"border-color-e":[{"border-e":V()}],"border-color-t":[{"border-t":V()}],"border-color-r":[{"border-r":V()}],"border-color-b":[{"border-b":V()}],"border-color-l":[{"border-l":V()}],"divide-color":[{divide:V()}],"outline-style":[{outline:[...ed(),"none","hidden"]}],"outline-offset":[{"outline-offset":[G,J,B]}],"outline-w":[{outline:["",G,Y,Z]}],"outline-color":[{outline:V()}],shadow:[{shadow:["","none",c,ea,X]}],"shadow-color":[{shadow:V()}],"inset-shadow":[{"inset-shadow":["none",m,ea,X]}],"inset-shadow-color":[{"inset-shadow":V()}],"ring-w":[{ring:el()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:V()}],"ring-offset-w":[{"ring-offset":[G,Z]}],"ring-offset-color":[{"ring-offset":V()}],"inset-ring-w":[{"inset-ring":el()}],"inset-ring-color":[{"inset-ring":V()}],"text-shadow":[{"text-shadow":["none",p,ea,X]}],"text-shadow-color":[{"text-shadow":V()}],opacity:[{opacity:[G,J,B]}],"mix-blend":[{"mix-blend":[...ec(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ec()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[G]}],"mask-image-linear-from-pos":[{"mask-linear-from":em()}],"mask-image-linear-to-pos":[{"mask-linear-to":em()}],"mask-image-linear-from-color":[{"mask-linear-from":V()}],"mask-image-linear-to-color":[{"mask-linear-to":V()}],"mask-image-t-from-pos":[{"mask-t-from":em()}],"mask-image-t-to-pos":[{"mask-t-to":em()}],"mask-image-t-from-color":[{"mask-t-from":V()}],"mask-image-t-to-color":[{"mask-t-to":V()}],"mask-image-r-from-pos":[{"mask-r-from":em()}],"mask-image-r-to-pos":[{"mask-r-to":em()}],"mask-image-r-from-color":[{"mask-r-from":V()}],"mask-image-r-to-color":[{"mask-r-to":V()}],"mask-image-b-from-pos":[{"mask-b-from":em()}],"mask-image-b-to-pos":[{"mask-b-to":em()}],"mask-image-b-from-color":[{"mask-b-from":V()}],"mask-image-b-to-color":[{"mask-b-to":V()}],"mask-image-l-from-pos":[{"mask-l-from":em()}],"mask-image-l-to-pos":[{"mask-l-to":em()}],"mask-image-l-from-color":[{"mask-l-from":V()}],"mask-image-l-to-color":[{"mask-l-to":V()}],"mask-image-x-from-pos":[{"mask-x-from":em()}],"mask-image-x-to-pos":[{"mask-x-to":em()}],"mask-image-x-from-color":[{"mask-x-from":V()}],"mask-image-x-to-color":[{"mask-x-to":V()}],"mask-image-y-from-pos":[{"mask-y-from":em()}],"mask-image-y-to-pos":[{"mask-y-to":em()}],"mask-image-y-from-color":[{"mask-y-from":V()}],"mask-image-y-to-color":[{"mask-y-to":V()}],"mask-image-radial":[{"mask-radial":[J,B]}],"mask-image-radial-from-pos":[{"mask-radial-from":em()}],"mask-image-radial-to-pos":[{"mask-radial-to":em()}],"mask-image-radial-from-color":[{"mask-radial-from":V()}],"mask-image-radial-to-color":[{"mask-radial-to":V()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":y()}],"mask-image-conic-pos":[{"mask-conic":[G]}],"mask-image-conic-from-pos":[{"mask-conic-from":em()}],"mask-image-conic-to-pos":[{"mask-conic-to":em()}],"mask-image-conic-from-color":[{"mask-conic-from":V()}],"mask-image-conic-to-color":[{"mask-conic-to":V()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:D()}],"mask-repeat":[{mask:L()}],"mask-size":[{mask:es()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",J,B]}],filter:[{filter:["","none",J,B]}],blur:[{blur:ep()}],brightness:[{brightness:[G,J,B]}],contrast:[{contrast:[G,J,B]}],"drop-shadow":[{"drop-shadow":["","none",u,ea,X]}],"drop-shadow-color":[{"drop-shadow":V()}],grayscale:[{grayscale:["",G,J,B]}],"hue-rotate":[{"hue-rotate":[G,J,B]}],invert:[{invert:["",G,J,B]}],saturate:[{saturate:[G,J,B]}],sepia:[{sepia:["",G,J,B]}],"backdrop-filter":[{"backdrop-filter":["","none",J,B]}],"backdrop-blur":[{"backdrop-blur":ep()}],"backdrop-brightness":[{"backdrop-brightness":[G,J,B]}],"backdrop-contrast":[{"backdrop-contrast":[G,J,B]}],"backdrop-grayscale":[{"backdrop-grayscale":["",G,J,B]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[G,J,B]}],"backdrop-invert":[{"backdrop-invert":["",G,J,B]}],"backdrop-opacity":[{"backdrop-opacity":[G,J,B]}],"backdrop-saturate":[{"backdrop-saturate":[G,J,B]}],"backdrop-sepia":[{"backdrop-sepia":["",G,J,B]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":N()}],"border-spacing-x":[{"border-spacing-x":N()}],"border-spacing-y":[{"border-spacing-y":N()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",J,B]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[G,"initial",J,B]}],ease:[{ease:["linear","initial",h,J,B]}],delay:[{delay:[G,J,B]}],animate:[{animate:["none",x,J,B]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[b,J,B]}],"perspective-origin":[{"perspective-origin":w()}],rotate:[{rotate:eu()}],"rotate-x":[{"rotate-x":eu()}],"rotate-y":[{"rotate-y":eu()}],"rotate-z":[{"rotate-z":eu()}],scale:[{scale:eg()}],"scale-x":[{"scale-x":eg()}],"scale-y":[{"scale-y":eg()}],"scale-z":[{"scale-z":eg()}],"scale-3d":["scale-3d"],skew:[{skew:eb()}],"skew-x":[{"skew-x":eb()}],"skew-y":[{"skew-y":eb()}],transform:[{transform:[J,B,"","none","gpu","cpu"]}],"transform-origin":[{origin:w()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:ef()}],"translate-x":[{"translate-x":ef()}],"translate-y":[{"translate-y":ef()}],"translate-z":[{"translate-z":ef()}],"translate-none":["translate-none"],accent:[{accent:V()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:V()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",J,B]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":N()}],"scroll-mx":[{"scroll-mx":N()}],"scroll-my":[{"scroll-my":N()}],"scroll-ms":[{"scroll-ms":N()}],"scroll-me":[{"scroll-me":N()}],"scroll-mt":[{"scroll-mt":N()}],"scroll-mr":[{"scroll-mr":N()}],"scroll-mb":[{"scroll-mb":N()}],"scroll-ml":[{"scroll-ml":N()}],"scroll-p":[{"scroll-p":N()}],"scroll-px":[{"scroll-px":N()}],"scroll-py":[{"scroll-py":N()}],"scroll-ps":[{"scroll-ps":N()}],"scroll-pe":[{"scroll-pe":N()}],"scroll-pt":[{"scroll-pt":N()}],"scroll-pr":[{"scroll-pr":N()}],"scroll-pb":[{"scroll-pb":N()}],"scroll-pl":[{"scroll-pl":N()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",J,B]}],fill:[{fill:["none",...V()]}],"stroke-w":[{stroke:[G,Y,Z,Q]}],stroke:[{stroke:["none",...V()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}});function eb(...e){return eg(i(e))}function ef({className:e,...r}){return(0,o.jsx)("div",{"data-slot":"card",className:eb("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...r})}function eh({className:e,...r}){return(0,o.jsx)("div",{"data-slot":"card-content",className:eb("px-6",e),...r})}function ex(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}var ev=function(e){let r=function(e){let r=n.forwardRef((e,r)=>{let{children:t,...o}=e;if(n.isValidElement(t)){var a;let e,s,i=(a=t,(s=(e=Object.getOwnPropertyDescriptor(a.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.ref:(s=(e=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.props.ref:a.props.ref||a.ref),l=function(e,r){let t={...r};for(let o in r){let a=e[o],s=r[o];/^on[A-Z]/.test(o)?a&&s?t[o]=(...e)=>{let r=s(...e);return a(...e),r}:a&&(t[o]=a):"style"===o?t[o]={...a,...s}:"className"===o&&(t[o]=[a,s].filter(Boolean).join(" "))}return{...e,...t}}(o,t.props);return t.type!==n.Fragment&&(l.ref=r?function(...e){return r=>{let t=!1,o=e.map(e=>{let o=ex(e,r);return t||"function"!=typeof o||(t=!0),o});if(t)return()=>{for(let r=0;r<o.length;r++){let t=o[r];"function"==typeof t?t():ex(e[r],null)}}}}(r,i):i),n.cloneElement(t,l)}return n.Children.count(t)>1?n.Children.only(null):null});return r.displayName=`${e}.SlotClone`,r}(e),t=n.forwardRef((e,t)=>{let{children:a,...s}=e,i=n.Children.toArray(a),l=i.find(ew);if(l){let e=l.props.children,a=i.map(r=>r!==l?r:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,o.jsx)(r,{...s,ref:t,children:n.isValidElement(e)?n.cloneElement(e,void 0,a):null})}return(0,o.jsx)(r,{...s,ref:t,children:a})});return t.displayName=`${e}.Slot`,t}("Slot"),ey=Symbol("radix.slottable");function ew(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===ey}let ek=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,ej=(e,r)=>t=>{var o;if((null==r?void 0:r.variants)==null)return i(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:a,defaultVariants:s}=r,n=Object.keys(a).map(e=>{let r=null==t?void 0:t[e],o=null==s?void 0:s[e];if(null===r)return null;let n=ek(r)||ek(o);return a[e][n]}),l=t&&Object.entries(t).reduce((e,r)=>{let[t,o]=r;return void 0===o||(e[t]=o),e},{});return i(e,n,null==r||null==(o=r.compoundVariants)?void 0:o.reduce((e,r)=>{let{class:t,className:o,...a}=r;return Object.entries(a).every(e=>{let[r,t]=e;return Array.isArray(t)?t.includes({...s,...l}[r]):({...s,...l})[r]===t})?[...e,t,o]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)},eN=ej("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline",gradient:"gradient-metamorphic text-white shadow-xs hover:opacity-90 transition-opacity"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function ez({className:e,variant:r,size:t,asChild:a=!1,...s}){return(0,o.jsx)(a?ev:"button",{"data-slot":"button",className:eb(eN({variant:r,size:t,className:e})),...s})}let eA=ej("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function eC({className:e,variant:r,asChild:t=!1,...a}){return(0,o.jsx)(t?ev:"span",{"data-slot":"badge",className:eb(eA({variant:r}),e),...a})}let eP=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),eM=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,t)=>t?t.toUpperCase():r.toLowerCase()),eI=e=>{let r=eM(e);return r.charAt(0).toUpperCase()+r.slice(1)},eE=(...e)=>e.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim(),e_=e=>{for(let r in e)if(r.startsWith("aria-")||"role"===r||"title"===r)return!0};var eS={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let eq=(0,n.forwardRef)(({color:e="currentColor",size:r=24,strokeWidth:t=2,absoluteStrokeWidth:o,className:a="",children:s,iconNode:i,...l},d)=>(0,n.createElement)("svg",{ref:d,...eS,width:r,height:r,stroke:e,strokeWidth:o?24*Number(t)/Number(r):t,className:eE("lucide",a),...!s&&!e_(l)&&{"aria-hidden":"true"},...l},[...i.map(([e,r])=>(0,n.createElement)(e,r)),...Array.isArray(s)?s:[s]])),eG=(e,r)=>{let t=(0,n.forwardRef)(({className:t,...o},a)=>(0,n.createElement)(eq,{ref:a,iconNode:r,className:eE(`lucide-${eP(eI(e))}`,`lucide-${e}`,t),...o}));return t.displayName=eI(e),t},eO=eG("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]);function e$({system:e}){let{name:r,description:t,status:a,url:n,purpose:i,icon:l,gradient:d,vault:c}=e,m=n.startsWith("http"),p="Active"===a;return(0,o.jsx)(ef,{className:eb("holographic-hover transition-all duration-300",c?"vault-card":"bg-gray-900 border-gray-800"),children:(0,o.jsxs)(eh,{className:"p-8",children:[(0,o.jsxs)("div",{className:"flex items-start justify-between mb-6",children:[(0,o.jsxs)("div",{className:"flex items-center gap-4",children:[(0,o.jsx)("div",{className:eb("p-3 rounded-lg",c?"bg-black border border-yellow-400":"bg-gray-800"),children:(0,o.jsx)(l,{className:eb("h-6 w-6",c?"text-yellow-400":d?"text-blue-400":"text-white")})}),(0,o.jsxs)("div",{children:[(0,o.jsx)("h3",{className:eb("text-2xl font-bold mb-1",c?"text-yellow-400":"text-white"),children:r}),(0,o.jsx)("p",{className:eb("text-sm",c?"text-yellow-400/80":"text-gray-400"),children:i})]})]}),(0,o.jsx)(eC,{variant:p?"default":"secondary",className:eb(p?c?"bg-yellow-400 text-black":"bg-green-500 text-white":"bg-gray-600 text-gray-300"),children:a})]}),(0,o.jsx)("p",{className:eb("text-base leading-relaxed mb-6",c?"text-yellow-400/90":"text-gray-300"),children:t}),(0,o.jsxs)("div",{className:"flex items-center justify-between",children:[(0,o.jsxs)("div",{className:"flex items-center gap-2 text-sm",children:[(0,o.jsx)("span",{className:eb(c?"text-yellow-400/70":"text-gray-400"),children:"Status:"}),(0,o.jsx)("span",{className:eb("font-medium",p?c?"text-yellow-400":"text-green-400":c?"text-yellow-400/70":"text-gray-300"),children:p?"Live & Operational":"In Development"})]}),(0,o.jsx)(ez,{asChild:!0,className:eb("font-semibold",c?"bg-yellow-400 text-black hover:bg-yellow-300":d?"gradient-metamorphic text-white":"bg-blue-600 hover:bg-blue-700 text-white"),children:(0,o.jsxs)(s(),{href:n,target:m?"_blank":void 0,rel:m?"noopener noreferrer":void 0,className:"flex items-center gap-2",children:[p?"Access System":"Learn More",m&&(0,o.jsx)(eO,{className:"h-4 w-4"})]})})]}),(0,o.jsx)("div",{className:eb("mt-6 pt-6 border-t",c?"border-yellow-400/30":"border-gray-700"),children:(0,o.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("span",{className:eb("block font-medium mb-1",c?"text-yellow-400":"text-white"),children:"Technology Stack"}),(0,o.jsxs)("span",{className:eb(c?"text-yellow-400/70":"text-gray-400"),children:["Catalyst"===r&&"AI/ML, Python, FastAPI","Metamorphic Reactor"===r&&"Multi-Agent, TypeScript, Node.js","Vault 024"===r&&"Blockchain, NFTs, Web3"]})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("span",{className:eb("block font-medium mb-1",c?"text-yellow-400":"text-white"),children:"API Access"}),(0,o.jsx)("span",{className:eb(c?"text-yellow-400/70":"text-gray-400"),children:p?"Available":"Coming Soon"})]})]})})]})})}let eR=eG("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]]),eW=eG("palette",[["path",{d:"M12 22a1 1 0 0 1 0-20 10 9 0 0 1 10 9 5 5 0 0 1-5 5h-2.25a1.75 1.75 0 0 0-1.4 2.8l.3.4a1.75 1.75 0 0 1-1.4 2.8z",key:"e79jfc"}],["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}]]),eT={title:"Systems & Environments | Metamorphic Labs",description:"Explore our cutting-edge AI systems: Catalyst for prompt engineering, Metamorphic Reactor for multi-agent orchestration, and Vault 024 for generative art and NFTs."},eV=[{name:"Catalyst",description:"Advanced prompt engineering platform for optimizing AI interactions and developing sophisticated prompt ecosystems.",status:"Active",url:"https://catalyst.metamorphiclabs.ai",purpose:"Prompt Engineering & AI Optimization",icon:eR,gradient:!0},{name:"Metamorphic Reactor",description:"Multi-agent orchestration system enabling complex AI workflows and intelligent automation across diverse domains.",status:"Development",url:"#metamorphic-reactor",purpose:"Multi-AI Orchestration & Automation",icon:eO,gradient:!0},{name:"Vault 024",description:"Decentralized art gallery and NFT platform showcasing generative creativity and blockchain-based digital assets.",status:"Active",url:"https://vault024.metamorphiclabs.ai",purpose:"Generative Art & NFT Marketplace",icon:eW,vault:!0}];function eD(){return(0,o.jsx)("div",{className:"bg-black text-white",children:(0,o.jsxs)("div",{className:"mx-auto max-w-7xl px-6 py-24 sm:py-32 lg:px-8",children:[(0,o.jsxs)("div",{className:"mx-auto max-w-2xl lg:mx-0",children:[(0,o.jsxs)("h1",{className:"text-4xl font-bold tracking-tight text-white sm:text-6xl",children:["Systems & ",(0,o.jsx)("span",{className:"gradient-text",children:"Environments"})]}),(0,o.jsx)("p",{className:"mt-6 text-lg leading-8 text-gray-300",children:"Explore our suite of cutting-edge AI systems and platforms designed to push the boundaries of what's possible in artificial intelligence and creative technology."})]}),(0,o.jsx)("div",{className:"mt-16 space-y-8",children:eV.map(e=>(0,o.jsx)(e$,{system:e},e.name))}),(0,o.jsxs)("div",{className:"mt-24 rounded-2xl bg-gray-900 p-8 border border-gray-800",children:[(0,o.jsx)("h2",{className:"text-2xl font-bold text-white mb-6",children:"System Integration"}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Interconnected Ecosystem"}),(0,o.jsx)("p",{className:"text-gray-300",children:"Our systems are designed to work together seamlessly, creating a powerful ecosystem where AI orchestration, prompt engineering, and creative generation combine to deliver unprecedented capabilities."})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"API Access"}),(0,o.jsx)("p",{className:"text-gray-300",children:"Each system provides robust API access for developers and organizations looking to integrate our AI capabilities into their own applications and workflows."})]})]})]}),(0,o.jsxs)("div",{className:"mt-16",children:[(0,o.jsx)("h2",{className:"text-2xl font-bold text-white mb-8",children:"Coming Soon"}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,o.jsxs)("div",{className:"rounded-lg bg-gray-900/50 p-6 border border-gray-800 border-dashed",children:[(0,o.jsx)("h3",{className:"text-lg font-semibold gradient-text mb-2",children:"Quantum Bridge"}),(0,o.jsx)("p",{className:"text-gray-400 text-sm mb-4",children:"Quantum-Classical AI Integration"}),(0,o.jsx)("p",{className:"text-gray-300",children:"Revolutionary platform bridging quantum computing with classical AI systems for exponential performance improvements."})]}),(0,o.jsxs)("div",{className:"rounded-lg bg-gray-900/50 p-6 border border-gray-800 border-dashed",children:[(0,o.jsx)("h3",{className:"text-lg font-semibold gradient-text mb-2",children:"Neural Forge"}),(0,o.jsx)("p",{className:"text-gray-400 text-sm mb-4",children:"Custom AI Model Training"}),(0,o.jsx)("p",{className:"text-gray-300",children:"Advanced platform for training and deploying custom AI models with automated optimization and deployment pipelines."})]})]})]})]})})}},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1229:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>p,tree:()=>d});var o=t(5239),a=t(8088),s=t(8170),n=t.n(s),i=t(893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);t.d(r,l);let d={children:["",{children:["systems",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,481)),"C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\app\\systems\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,1075)),"C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,4413)),"C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\New folder (9)\\metamorphic-labs-website\\src\\app\\systems\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},p=new o.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/systems/page",pathname:"/systems",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},8426:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,4536,23))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[447,596,84],()=>t(1229));module.exports=o})();