import type { Metada<PERSON> } from 'next';
import { SystemCard } from '@/components/system-card';
import { ExternalLink, Zap, Palette } from 'lucide-react';

export const metadata: Metadata = {
  title: 'Systems & Environments | Metamorphic Labs',
  description: 'Explore our cutting-edge AI systems: Catalyst for prompt engineering, Metamorphic Reactor for multi-agent orchestration, and Vault 024 for generative art and NFTs.',
};

const systems = [
  {
    name: 'Catalyst',
    description: 'Advanced prompt engineering platform for optimizing AI interactions and developing sophisticated prompt ecosystems.',
    status: 'Active',
    url: 'https://catalyst.metamorphiclabs.ai',
    purpose: 'Prompt Engineering & AI Optimization',
    icon: Zap,
    gradient: true,
  },
  {
    name: 'Metamorphic Reactor',
    description: 'Multi-agent orchestration system enabling complex AI workflows and intelligent automation across diverse domains.',
    status: 'Development',
    url: '#metamorphic-reactor',
    purpose: 'Multi-AI Orchestration & Automation',
    icon: ExternalLink,
    gradient: true,
  },
  {
    name: 'Vault 024',
    description: 'Decentralized art gallery and NFT platform showcasing generative creativity and blockchain-based digital assets.',
    status: 'Active',
    url: 'https://vault024.metamorphiclabs.ai',
    purpose: 'Generative Art & NFT Marketplace',
    icon: Palette,
    vault: true,
  },
];

export default function SystemsPage() {
  return (
    <div className="bg-black text-white">
      <div className="mx-auto max-w-7xl px-6 py-24 sm:py-32 lg:px-8">
        <div className="mx-auto max-w-2xl lg:mx-0">
          <h1 className="text-4xl font-bold tracking-tight text-white sm:text-6xl">
            Systems & <span className="gradient-text">Environments</span>
          </h1>
          <p className="mt-6 text-lg leading-8 text-gray-300">
            Explore our suite of cutting-edge AI systems and platforms designed to push 
            the boundaries of what&apos;s possible in artificial intelligence and creative technology.
          </p>
        </div>

        <div className="mt-16 space-y-8">
          {systems.map((system) => (
            <SystemCard key={system.name} system={system} />
          ))}
        </div>

        {/* Additional Information */}
        <div className="mt-24 rounded-2xl bg-gray-900 p-8 border border-gray-800">
          <h2 className="text-2xl font-bold text-white mb-6">System Integration</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h3 className="text-lg font-semibold text-white mb-4">Interconnected Ecosystem</h3>
              <p className="text-gray-300">
                Our systems are designed to work together seamlessly, creating a powerful 
                ecosystem where AI orchestration, prompt engineering, and creative generation 
                combine to deliver unprecedented capabilities.
              </p>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-white mb-4">API Access</h3>
              <p className="text-gray-300">
                Each system provides robust API access for developers and organizations 
                looking to integrate our AI capabilities into their own applications and workflows.
              </p>
            </div>
          </div>
        </div>

        {/* Coming Soon */}
        <div className="mt-16">
          <h2 className="text-2xl font-bold text-white mb-8">Coming Soon</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="rounded-lg bg-gray-900/50 p-6 border border-gray-800 border-dashed">
              <h3 className="text-lg font-semibold gradient-text mb-2">Quantum Bridge</h3>
              <p className="text-gray-400 text-sm mb-4">Quantum-Classical AI Integration</p>
              <p className="text-gray-300">
                Revolutionary platform bridging quantum computing with classical AI systems 
                for exponential performance improvements.
              </p>
            </div>
            <div className="rounded-lg bg-gray-900/50 p-6 border border-gray-800 border-dashed">
              <h3 className="text-lg font-semibold gradient-text mb-2">Neural Forge</h3>
              <p className="text-gray-400 text-sm mb-4">Custom AI Model Training</p>
              <p className="text-gray-300">
                Advanced platform for training and deploying custom AI models with 
                automated optimization and deployment pipelines.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
