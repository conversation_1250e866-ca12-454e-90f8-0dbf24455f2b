{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/New%20folder%20%289%29/metamorphic-labs-website/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/New%20folder%20%289%29/metamorphic-labs-website/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n        gradient:\n          \"gradient-metamorphic text-white shadow-xs hover:opacity-90 transition-opacity\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;YACN,UACE;QACJ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/New%20folder%20%289%29/metamorphic-labs-website/src/components/metamorphic-logo.tsx"], "sourcesContent": ["'use client';\n\nimport Image from 'next/image';\nimport { motion } from 'framer-motion';\nimport { cn } from '@/lib/utils';\n\ninterface MetamorphicLogoProps {\n  className?: string;\n  animated?: boolean;\n}\n\nexport function MetamorphicLogo({ className, animated = false }: MetamorphicLogoProps) {\n  return (\n    <motion.div\n      className={cn('flex items-center', className)}\n      initial={animated ? { opacity: 0, scale: 0.8 } : undefined}\n      animate={animated ? { opacity: 1, scale: 1 } : undefined}\n      transition={animated ? { duration: 0.8, ease: \"easeOut\" } : undefined}\n    >\n      <Image\n        src=\"/metamorphic-labs-logo.png\"\n        alt=\"Metamorphic Labs\"\n        width={200}\n        height={40}\n        className=\"h-auto w-auto\"\n        priority\n      />\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAWO,SAAS,gBAAgB,EAAE,SAAS,EAAE,WAAW,KAAK,EAAwB;IACnF,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,qBAAqB;QACnC,SAAS,WAAW;YAAE,SAAS;YAAG,OAAO;QAAI,IAAI;QACjD,SAAS,WAAW;YAAE,SAAS;YAAG,OAAO;QAAE,IAAI;QAC/C,YAAY,WAAW;YAAE,UAAU;YAAK,MAAM;QAAU,IAAI;kBAE5D,cAAA,6LAAC,gIAAA,CAAA,UAAK;YACJ,KAAI;YACJ,KAAI;YACJ,OAAO;YACP,QAAQ;YACR,WAAU;YACV,QAAQ;;;;;;;;;;;AAIhB;KAlBgB", "debugId": null}}, {"offset": {"line": 147, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/New%20folder%20%289%29/metamorphic-labs-website/src/components/navigation.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { useState } from 'react';\nimport { Menu, X } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\nimport { MetamorphicLogo } from '@/components/metamorphic-logo';\n\nconst navigation = [\n  { name: 'Home', href: '/' },\n  { name: 'About', href: '/about' },\n  { name: 'Systems', href: '/systems' },\n  { name: 'Contact', href: '/contact' },\n];\n\nexport function Navigation() {\n  const pathname = usePathname();\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\n\n  return (\n    <header className=\"sticky top-0 z-50 w-full border-b border-gray-800 bg-black/80 backdrop-blur-md\">\n      <nav className=\"mx-auto flex max-w-7xl items-center justify-between p-6 lg:px-8\" aria-label=\"Main navigation\">\n        <div className=\"flex lg:flex-1\">\n          <Link href=\"/\" className=\"-m-1.5 p-1.5\" aria-label=\"Metamorphic Labs home\">\n            <span className=\"sr-only\">Metamorphic Labs</span>\n            <MetamorphicLogo className=\"h-8 w-auto\" />\n          </Link>\n        </div>\n        \n        <div className=\"flex lg:hidden\">\n          <Button\n            variant=\"ghost\"\n            className=\"-m-2.5 inline-flex items-center justify-center rounded-md p-2.5 text-white\"\n            onClick={() => setMobileMenuOpen(true)}\n          >\n            <span className=\"sr-only\">Open main menu</span>\n            <Menu className=\"h-6 w-6\" aria-hidden=\"true\" />\n          </Button>\n        </div>\n        \n        <div className=\"hidden lg:flex lg:gap-x-12\">\n          {navigation.map((item) => (\n            <Link\n              key={item.name}\n              href={item.href}\n              className={`text-sm font-semibold leading-6 transition-colors hover:text-blue-400 ${\n                pathname === item.href\n                  ? 'text-blue-400'\n                  : 'text-white'\n              }`}\n            >\n              {item.name}\n            </Link>\n          ))}\n        </div>\n        \n        <div className=\"hidden lg:flex lg:flex-1 lg:justify-end\">\n          <Button\n            asChild\n            className=\"gradient-metamorphic text-white font-semibold\"\n          >\n            <Link href=\"/contact\">\n              Get Started\n            </Link>\n          </Button>\n        </div>\n      </nav>\n      \n      {/* Mobile menu */}\n      {mobileMenuOpen && (\n        <div className=\"lg:hidden\">\n          <div className=\"fixed inset-0 z-50\" />\n          <div className=\"fixed inset-y-0 right-0 z-50 w-full overflow-y-auto bg-black px-6 py-6 sm:max-w-sm sm:ring-1 sm:ring-gray-800\">\n            <div className=\"flex items-center justify-between\">\n              <Link href=\"/\" className=\"-m-1.5 p-1.5\">\n                <span className=\"sr-only\">Metamorphic Labs</span>\n                <MetamorphicLogo className=\"h-8 w-auto\" />\n              </Link>\n              <Button\n                variant=\"ghost\"\n                className=\"-m-2.5 rounded-md p-2.5 text-white\"\n                onClick={() => setMobileMenuOpen(false)}\n              >\n                <span className=\"sr-only\">Close menu</span>\n                <X className=\"h-6 w-6\" aria-hidden=\"true\" />\n              </Button>\n            </div>\n            <div className=\"mt-6 flow-root\">\n              <div className=\"-my-6 divide-y divide-gray-800\">\n                <div className=\"space-y-2 py-6\">\n                  {navigation.map((item) => (\n                    <Link\n                      key={item.name}\n                      href={item.href}\n                      className={`-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 transition-colors hover:bg-gray-900 ${\n                        pathname === item.href\n                          ? 'text-blue-400'\n                          : 'text-white'\n                      }`}\n                      onClick={() => setMobileMenuOpen(false)}\n                    >\n                      {item.name}\n                    </Link>\n                  ))}\n                </div>\n                <div className=\"py-6\">\n                  <Button\n                    asChild\n                    className=\"gradient-metamorphic w-full text-white font-semibold\"\n                    onClick={() => setMobileMenuOpen(false)}\n                  >\n                    <Link href=\"/contact\">\n                      Get Started\n                    </Link>\n                  </Button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AACA;;;AAPA;;;;;;;AASA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAQ,MAAM;IAAI;IAC1B;QAAE,MAAM;QAAS,MAAM;IAAS;IAChC;QAAE,MAAM;QAAW,MAAM;IAAW;IACpC;QAAE,MAAM;QAAW,MAAM;IAAW;CACrC;AAEM,SAAS;;IACd,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,qBACE,6LAAC;QAAO,WAAU;;0BAChB,6LAAC;gBAAI,WAAU;gBAAkE,cAAW;;kCAC1F,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;4BAAe,cAAW;;8CACjD,6LAAC;oCAAK,WAAU;8CAAU;;;;;;8CAC1B,6LAAC,4IAAA,CAAA,kBAAe;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAI/B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,WAAU;4BACV,SAAS,IAAM,kBAAkB;;8CAEjC,6LAAC;oCAAK,WAAU;8CAAU;;;;;;8CAC1B,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;oCAAU,eAAY;;;;;;;;;;;;;;;;;kCAI1C,6LAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAW,CAAC,sEAAsE,EAChF,aAAa,KAAK,IAAI,GAClB,kBACA,cACJ;0CAED,KAAK,IAAI;+BARL,KAAK,IAAI;;;;;;;;;;kCAapB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;4BACL,OAAO;4BACP,WAAU;sCAEV,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;0CAAW;;;;;;;;;;;;;;;;;;;;;;YAQ3B,gCACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;;0DACvB,6LAAC;gDAAK,WAAU;0DAAU;;;;;;0DAC1B,6LAAC,4IAAA,CAAA,kBAAe;gDAAC,WAAU;;;;;;;;;;;;kDAE7B,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,WAAU;wCACV,SAAS,IAAM,kBAAkB;;0DAEjC,6LAAC;gDAAK,WAAU;0DAAU;;;;;;0DAC1B,6LAAC,+LAAA,CAAA,IAAC;gDAAC,WAAU;gDAAU,eAAY;;;;;;;;;;;;;;;;;;0CAGvC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;oDAEH,MAAM,KAAK,IAAI;oDACf,WAAW,CAAC,uGAAuG,EACjH,aAAa,KAAK,IAAI,GAClB,kBACA,cACJ;oDACF,SAAS,IAAM,kBAAkB;8DAEhC,KAAK,IAAI;mDATL,KAAK,IAAI;;;;;;;;;;sDAapB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDACL,OAAO;gDACP,WAAU;gDACV,SAAS,IAAM,kBAAkB;0DAEjC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;8DAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAY1C;GA5GgB;;QACG,qIAAA,CAAA,cAAW;;;KADd", "debugId": null}}]}