import type { <PERSON>ada<PERSON> } from "next";
import { Inter, JetBrains_Mono } from "next/font/google";
import "./globals.css";
import { Navigation } from "@/components/navigation";
import { Footer } from "@/components/footer";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
  display: "swap",
});

const jetbrainsMono = JetBrains_Mono({
  variable: "--font-jetbrains-mono",
  subsets: ["latin"],
  display: "swap",
});

export const metadata: Metadata = {
  title: "Metamorphic Labs | Redefining Reality with AI & Quantum Systems",
  description: "Metamorphic Labs pioneers next-generation AI systems, multi-agent orchestration, generative creativity, and boundary-pushing software solutions. From deep prompt ecosystems to decentralized art galleries—we build the future.",
  keywords: ["AI", "Quantum Systems", "Multi-Agent Orchestration", "Generative Art", "NFTs", "Prompt Engineering", "Software Development"],
  authors: [{ name: "Metamorphic Labs" }],
  creator: "Metamorphic Labs",
  publisher: "Metamorphic Labs",
  openGraph: {
    title: "Metamorphic Labs | Redefining Reality with AI & Quantum Systems",
    description: "Metamorphic Labs pioneers next-generation AI systems, multi-agent orchestration, generative creativity, and boundary-pushing software solutions.",
    url: "https://metamorphiclabs.ai",
    siteName: "Metamorphic Labs",
    type: "website",
    locale: "en_US",
  },
  twitter: {
    card: "summary_large_image",
    title: "Metamorphic Labs | Redefining Reality with AI & Quantum Systems",
    description: "Metamorphic Labs pioneers next-generation AI systems, multi-agent orchestration, generative creativity, and boundary-pushing software solutions.",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  viewport: {
    width: "device-width",
    initialScale: 1,
    maximumScale: 1,
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="dark">
      <body
        className={`${inter.variable} ${jetbrainsMono.variable} font-sans antialiased bg-black text-white min-h-screen`}
      >
        <div className="flex flex-col min-h-screen">
          <Navigation />
          <main className="flex-1">
            {children}
          </main>
          <Footer />
        </div>
      </body>
    </html>
  );
}
